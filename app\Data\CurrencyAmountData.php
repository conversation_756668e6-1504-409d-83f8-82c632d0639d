<?php

namespace App\Data;

use <PERSON><PERSON>\LaravelData\Attributes\Validation\ExcludeIf;
use Spatie\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Attributes\Validation\Min;
use Spatie\LaravelData\Attributes\Validation\Numeric;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Data;

class CurrencyAmountData extends BaseNonNullableData
{
    #[Required,Numeric]
    public float $amount;
    #[Required,Min(3),Max(3)]
    public string $currency;

    // #[ExcludeIf]
    // public ?int $status;
    public function __construct(
        public ?float $status=null
    ) {
    }
    public function currencyName():?string {
        return __("{$this->currency}");
    }
    public function currencyFullName():?string {
        return __("{$this->currency}-Full");
    }

    // public function exceptProperties() : array
    // {
    //     return [
    //         'status'=>is_null($this->status)
    //     ];
    // }

}