<?php

use <PERSON><PERSON>\LaravelSettings\Migrations\SettingsMigration;

class AddObdxBankyThirdPartySettings extends SettingsMigration
{
    public function up(): void
    {
        if(env('APP_FLAG',"banky.commercial")=="banky.islamic"){
            $this->migrator->addEncrypted('thirdParty.obdxBanky',array (
                'url' => 'https://online.yki-bank.com:8080',
                'client_id' => '',
                'client_secret' => '',
                'token' => array (
                    'access_token' => '',
                    'token_type' => '',
                    'expires_in' => ''
                ),
                'is_test' => '1',
                'test_data' => array (
                    'url' => 'http://172.22.20.50:7777',
                    'client_id' => '',
                    'client_secret' => '',
                    'token' => array (
                        'access_token' => '',
                        'token_type' => '',
                        'expires_in' => ''
                    ),
                )
            ));
        }else{
            $this->migrator->addEncrypted('thirdParty.obdxBanky',array (
                'url' => 'https://online.yk-bank.com:8080',
                'client_id' => '',
                'client_secret' => '',
                'token' => array (
                    'access_token' => '',
                    'token_type' => '',
                    'expires_in' => ''
                ),
                'is_test' => '1',
                'test_data' => array (
                    'url' => 'http://172.22.40.45:7777',
                    'client_id' => '',
                    'client_secret' => '',
                    'token' => array (
                        'access_token' => '',
                        'token_type' => '',
                        'expires_in' => ''
                    ),
                )
            ));
        }

    }
    public function down()
    {
        $this->migrator->delete('thirdParty.obdxBanky');
    }
}
