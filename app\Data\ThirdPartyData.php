<?php

namespace App\Data;

use <PERSON><PERSON>\LaravelData\Data;
use Spatie\LaravelData\Normalizers\ObjectNormalizer;
use Illuminate\Support\Collection;

class ThirdPartyData extends Data
{
    public ?TokenData $token;
    public ?string $url;

    public ?string $client_id;
    public ?string $client_secret;
    public ?string $instid;
    public ?CardlessData $cardless;
    public $agent_info;
    public ?UtilityPaymentData $utility_payment;
    public ?int $is_test;
    public ?ThirdPartyData $test_data;

    public static function prepareForPipeline(array $properties) : array
    {
        if(!is_null(data_get($properties,"agent_info"))){
            if(!is_array(data_get($properties,"agent_info"))){
                $properties['agent_info']= collect(data_get($properties,"agent_info"))->toJson();
            }
        }
        return $properties;
    }
}
