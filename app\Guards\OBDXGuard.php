<?php

namespace App\Guards;
use App\Data\StatusData;
use App\Models\CustomerType;
use App\Models\PartyVerify;
use App\Providers\OBDXUserProvider;
use App\Scopes\CustomerScope;
use App\Scopes\UsernameScope;
use Illuminate\Container\Container;
use Illuminate\Contracts\Debug\ExceptionHandler;
use Illuminate\Support\Facades\Crypt;
use Laravel\Passport\Exceptions\OAuthServerException;

use Illuminate\Http\Request;
use Laravel\Passport\TokenRepository;
use League\OAuth2\Server\ResourceServer;
use Nyholm\Psr7\Factory\Psr17Factory;
use Symfony\Bridge\PsrHttpMessage\Factory\PsrHttpFactory;

class OBDXGuard
{
    /**
     * The resource server instance.
     *
     * @var \League\OAuth2\Server\ResourceServer
     */
    protected $server;
    /**
     * The user provider implementation.
     *
     * @var \Laravel\Passport\PassportUserProvider
     */
    protected $provider;

    /**
     * The token repository instance.
     *
     * @var \Laravel\Passport\TokenRepository
     */
    protected $tokens;
    /**
     * The partyVerify instance.
     *
     * @var PartyVerify
     */
    protected $partyVerify;

    /**
     * Create a new token guard instance.
     *
     * @param  \League\OAuth2\Server\ResourceServer  $server
     * @param  OBDXUserProvider  $provider
     * @return void
     */
    public function __construct(
        ResourceServer $server,
        OBDXUserProvider $provider,
        TokenRepository $tokens,
    ) {
        $this->server = $server;
        $this->provider = $provider;
        $this->tokens = $tokens;

    }
    /**
     * Get the user for the incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function user(Request $request)
    {
        if ($request->header("token")!=null) {

            $this->partyVerify=PartyVerify::
                withoutGlobalScope(CustomerScope::class)
                ->withoutGlobalScope(UsernameScope::class)
                ->where('device_key',$request->header("deviceKey"))
                ->where('token',$request->header("token"))
                ->first();
                $this->provider->partyVerify=$this->partyVerify;
                $tokenValidation=app(\App\Settings\ConfigSettings::class)->appConfig->tokenValidation;
                if($tokenValidation==1){
                    if(is_null($this->partyVerify)){
                        abort(response()->json(StatusData::from([
                            "result"    => "SUCCESSFUL",
                            "contextID" => "MIDDLEWARE",
                            "message"   => [
                                "title"   => __("System cannot process the request currently. Please try later."),
                                "detail"  => "Guard",
                                "code"    => "DIGX_PROD_DEF_0000",
                                "type"    => "ERROR",
                                "relatedMessage"=> [
                                    [
                                        "detail"=> "Access denied.",
                                        "code"=> "FC_SM_025"
                                    ]
                                ],
                            ]
                        ]),\Symfony\Component\HttpFoundation\Response::HTTP_UNAUTHORIZED));
                        //return null;
                    }
                }

                if(!is_null($this->partyVerify) && $this->partyVerify->customer_type_id==CustomerType::GUEST){
                    $request->headers->add([
                        "Authorization"=>'Bearer '.$request->header("token")
                    ]);
                    return $this->authenticateViaBearerToken($request);
                }else{
                    return $this->authenticateViaJwtToken($request);
                }
        }else if($request->cookie('JSESSIONID')){
            return $this->authenticateViaCookie($request);

        }

    }
    /**
     * Authenticate the incoming request via the token cookie.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    protected function authenticateViaCookie($request)
    {

        // If this user exists, we will return this user and attach a "transient" token to
        // the user model. The transient token assumes it has all scopes since the user
        // is physically logged into the application via the application's interface.
       return $this->provider->retrieveById(null);
    }

    /**
     * Authenticate the incoming request via the Bearer token.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    protected function authenticateViaBearerToken($request)
    {
       // \Log::critical("authenticateViaBearerToken - before");
        if (! $psr = $this->getPsrRequestViaBearerToken($request)) {
            return;
        }
       // \Log::critical("authenticateViaBearerToken - after");

        // if (! $this->hasValidProvider($request)) {
        //     return;
        // }

        try {
            $decrypted = Crypt::decrypt($this->partyVerify->terminal->credentials);
            //\Log::critical("$decrypted");
            $explode=explode(':',$decrypted);
            $credentials=[
                'username' => $explode[0],
                'password' => $explode[1],
            ];
            //\Log::critical(json_encode($credentials));
        } catch (\Illuminate\Contracts\Encryption\DecryptException $e) {
            return;
        }
        // If the access token is valid we will retrieve the user according to the user ID
        // associated with the token. We will use the provider implementation which may
        // be used to retrieve users from Eloquent. Next, we'll be ready to continue.
        $user = $this->provider->retrieveByCredentials($credentials);

        if (! $user) {
            return;
        }

        // Next, we will assign a token instance to this user which the developers may use
        // to determine if the token has a given scope, etc. This will be useful during
        // authorization such as within the developer's Laravel model policy classes.
        $token = $this->tokens->find(
            $psr->getAttribute('oauth_access_token_id')
        );

       // $clientId = $psr->getAttribute('oauth_client_id');

        // Finally, we will verify if the client that issued this token is still valid and
        // its tokens may still be used. If not, we will bail out since we don't want a
        // user to be able to send access tokens for deleted or revoked applications.
        // if ($this->clients->revoked($clientId)) {
        //     return;
        // }

        return $token ? $user->withAccessToken($token) : null;
    }

    /**
     * Authenticate and get the incoming PSR-7 request via the Bearer token.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Psr\Http\Message\ServerRequestInterface
     */
    protected function getPsrRequestViaBearerToken($request)
    {
        // First, we will convert the Symfony request to a PSR-7 implementation which will
        // be compatible with the base OAuth2 library. The Symfony bridge can perform a
        // conversion for us to a new Nyholm implementation of this PSR-7 request.
        $psr = (new PsrHttpFactory(
            new Psr17Factory,
            new Psr17Factory,
            new Psr17Factory,
            new Psr17Factory
        ))->createRequest($request);

        try {
            return $this->server->validateAuthenticatedRequest($psr);
        } catch (OAuthServerException $e) {
            $request->headers->set('Authorization', '', true);

            Container::getInstance()->make(
                ExceptionHandler::class
            )->report($e);
        }
    }
    /**
     * Decode and decrypt the JWT token cookie.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    protected function authenticateViaJwtToken($request)
    {
        // If the access token is valid we will retrieve the user according to the user ID
        // associated with the token. We will use the provider implementation which may
        // be used to retrieve users from Eloquent. Next, we'll be ready to continue.
        $user = $this->provider->retrieveByToken(null,$request->header("token"));
        return $user;
    }
}
