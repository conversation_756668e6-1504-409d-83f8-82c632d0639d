<?php

namespace App\Http\Middleware;
use App\Data\GeneralResponseData;
use App\Models\AgentUser;
use App\Models\CustomerType;
use App\Services\OBDX\CustomerService;
use Closure;

class AgentMiddleware
{
    public function handle($request, Closure $next)
    {
        $userProfile=$request->user()->userProfile;
        if(!is_null($userProfile)){
            $agentUser=AgentUser::where('party_id',$userProfile->partyId->value)->first();
            if(!is_null($agentUser)){
                // $userProfile->agent=$agentUser;
                // $userProfile->customerType=CustomerType::AGENT;
                // $user=$request->user();
                // $user->userProfile=$userProfile;

                // $request->setUserResolver(function () use ($user) {
                //     return $user;
                // });
                return $next($request);
            }
        }
        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "",
                "message"   => [
                    "title"   => "UNAUTHORIZED",
                    "detail"  => "",
                    "code"    => "DIGX_SWITCH_AGENT",
                    "type"    => "ERROR"
                ]
            ],
        ]),\Symfony\Component\HttpFoundation\Response::HTTP_UNAUTHORIZED);
        
    }
}
