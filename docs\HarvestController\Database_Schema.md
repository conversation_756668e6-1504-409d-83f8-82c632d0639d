# HarvestController Database Schema Documentation

## Overview
This document describes the database schema and relationships used by the HarvestController service.

## Tables

### 1. harvests
Primary table storing harvest transaction records.

#### Schema
```sql
CREATE TABLE harvests (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    party_id VARCHAR(45) NOT NULL,
    service_code_id BIGINT NOT NULL,
    request_id VARCHAR(100) NULL,
    data TEXT NULL,
    amount VARCHAR(255) NULL,
    token VARCHAR(500) NULL,
    sender_info TEXT NULL,
    receiver_info TEXT NULL,
    received_data TEXT NULL,
    status INTEGER DEFAULT 0,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL
);
```

#### Indexes
- `PRIMARY KEY (id)`
- `INDEX harvests_party_id (party_id)`
- `INDEX harvests_service_code_id (service_code_id)`
- `INDEX harvests_request_id (request_id)`
- `INDEX harvests_status (status)`

#### Field Descriptions
| Field | Type | Description |
|-------|------|-------------|
| id | BIGINT | Primary key, auto-increment |
| party_id | VARCHAR(45) | User/customer identifier |
| service_code_id | BIGINT | Foreign key to harvest_service_codes |
| request_id | VARCHAR(100) | Unique request identifier for tracking |
| data | TEXT | JSON data containing request parameters |
| amount | VARCHAR(255) | JSON data containing amount and currency |
| token | VARCHAR(500) | Transaction token from Yeah Money API |
| sender_info | TEXT | JSON data about remittance sender |
| receiver_info | TEXT | JSON data about remittance receiver |
| received_data | TEXT | JSON response data from Yeah Money API |
| status | INTEGER | Transaction status (0=pending, 1=completed, -1=failed) |
| created_at | TIMESTAMP | Record creation timestamp |
| updated_at | TIMESTAMP | Record last update timestamp |

#### JSON Field Structures

**data field (HarvestRequestData):**
```json
{
    "trackingCode": "YM401707304",
    "amount": {
        "amount": 124.5,
        "currency": "USD"
    },
    "senderPhone": "*********",
    "creditAccountId": {
        "value": "40118341500100010001"
    },
    "serviceCode": 1000000,
    "branchId": "401",
    "firstName": "John",
    "secondName": "Michael",
    "thirdName": "Smith",
    "lastName": "Doe",
    "fullName": "John Michael Smith Doe"
}
```

**amount field (CurrencyAmountData):**
```json
{
    "amount": 124.5,
    "currency": "USD"
}
```

**sender_info field:**
```json
{
    "Sender_Full_Name": "Jane Doe",
    "Sender_Mobile": "**********",
    "Agent_Name": "Yeah Money Agent"
}
```

**receiver_info field:**
```json
{
    "Receiver_Full_Name": "John Michael Smith Doe",
    "Receiver_Mobile": "*********",
    "Receiver_Type": "I"
}
```

**received_data field:**
```json
{
    "Result_Code": 0,
    "Result_Desc": "Success",
    "Payout_Trx_Id": "EXT123456789"
}
```

### 2. harvest_service_codes
Lookup table for remittance service providers.

#### Schema
```sql
CREATE TABLE harvest_service_codes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(45) NOT NULL,
    name VARCHAR(255) NULL,
    process BOOLEAN DEFAULT FALSE,
    status BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL
);
```

#### Field Descriptions
| Field | Type | Description |
|-------|------|-------------|
| id | BIGINT | Primary key, auto-increment |
| code | VARCHAR(45) | Service provider code (e.g., 'YMNY', 'RIA') |
| name | VARCHAR(255) | Display name of service provider |
| process | BOOLEAN | Whether service supports processing |
| status | BOOLEAN | Whether service is active/enabled |
| created_at | TIMESTAMP | Record creation timestamp |
| updated_at | TIMESTAMP | Record last update timestamp |

#### Default Data
```sql
INSERT INTO harvest_service_codes VALUES
(1000000, 'YMNY', 'شبكه محلية', false, true),
(1000001, 'SHIFT', 'SHIFT Financial Services Ltd.', false, false),
(1000002, 'BAJ', 'Fawri Services', false, false),
(1000003, 'UREMIT', 'U Remit Money Transfer Services', false, false),
(1000004, 'RIA', 'RIA Money Transfer', false, false),
(1000005, 'SW', 'Small World Money Transfer', false, false),
(1000006, 'YCASH', 'National Cash Transfer Co.', false, false);
```

## Relationships

### 1. harvests → harvest_service_codes
- **Type**: Many-to-One (belongsTo)
- **Foreign Key**: `harvests.service_code_id`
- **Reference**: `harvest_service_codes.id`
- **Relationship Method**: `Harvest::service()`

```php
public function service()
{
    return $this->belongsTo('App\Models\HarvestServiceCode', "service_code_id");
}
```

### 2. harvests → log_entries
- **Type**: One-to-Many (hasMany)
- **Foreign Key**: `log_entries.model_id`
- **Relationship Method**: `Harvest::logs()`

```php
public function logs()
{
    $_name = static::class;
    return $this->hasMany('App\Models\LogEntry', "model_id")
        ->where('model', $_name)
        ->where('type', 'request')
        ->with('relatedEntries');
}
```

## Model Configuration

### Harvest Model
```php
class Harvest extends Model
{
    use HasFactory;
    
    protected $dates = ['created_at', 'updated_at'];
    
    protected $casts = [
        'data' => 'object',
        'received_data' => 'object',
        'amount' => 'object',
        'credit_account_id' => 'object',
        'sender_info' => 'object',
        'receiver_info' => 'object',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];
    
    protected $hidden = ['rn', 'token'];
    
    public static $hiddenParameters = ['token'];
}
```

### Global Scopes
- **CustomerScope**: Automatically filters records by authenticated user's party_id
- **Auto-setting party_id**: Automatically sets party_id on record creation

## Database Compatibility

### MySQL
- Uses JSON data type for storing complex objects
- JSON path queries: `data->trackingCode`, `sender_info->Sender_Full_Name`

### Oracle
- Uses CLOB with JSON functions
- JSON queries: `json_query(data,'$.trackingCode')`, `json_query(sender_info,'$.Sender_Full_Name')`

## Query Examples

### List User's Completed Harvests
```sql
SELECT 
    h.id,
    JSON_UNQUOTE(JSON_EXTRACT(h.data, '$.trackingCode')) as tracking_code,
    JSON_UNQUOTE(JSON_EXTRACT(h.data, '$.senderPhone')) as sender_phone,
    JSON_UNQUOTE(JSON_EXTRACT(h.sender_info, '$.Sender_Full_Name')) as sender_name,
    h.amount,
    JSON_UNQUOTE(JSON_EXTRACT(h.received_data, '$.Payout_Trx_Id')) as reference_id,
    hsc.name,
    h.status,
    h.created_at as date
FROM harvests h
LEFT JOIN harvest_service_codes hsc ON hsc.id = h.service_code_id
WHERE h.status = 1 
    AND h.party_id = ?
ORDER BY h.id DESC
LIMIT ? OFFSET ?;
```

### Find Pending Transaction
```sql
SELECT * FROM harvests 
WHERE id = ? 
    AND party_id = ? 
    AND status = 0;
```

## Performance Considerations

### Indexing Strategy
- Primary operations are filtered by party_id and status
- request_id used for tracking and duplicate prevention
- service_code_id for joins with service codes table

### JSON Field Optimization
- Oracle: Functional indexes on frequently queried JSON paths
- MySQL: Generated columns for commonly accessed JSON fields

### Pagination
- Uses LIMIT/OFFSET for result pagination
- Ordered by ID DESC for chronological listing
