<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('party_id', 45)->nullable()->index('notifications_party_id');
            $table->string('type')->index('notifications_type');
            $table->text('title')->nullable();
            $table->text('body')->nullable();
            $table->text('result')->nullable();
            $table->bigInteger('user_id')->index('notifications_user_id');  
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('notifications');

    }
};
