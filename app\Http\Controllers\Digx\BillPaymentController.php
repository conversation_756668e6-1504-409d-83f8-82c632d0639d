<?php

namespace App\Http\Controllers\Digx;
use App\Data\AccountConfigData;
use App\Data\AccountData;
use App\Data\BaseNonNullableDataCollection;
use App\Data\CurrencyAmountData;
use App\Data\GeneralResponseData;
use App\Data\OBDX\BillPayment\BillPaymentData;
use App\Data\OBDX\BillPayment\BillPaymentDynamicFormData;
use App\Data\PaymentResultData;
use App\Data\ReceiptData;
use App\Enums\BillPaymentServiceCodeEnum;
use App\Enums\InvoiceTransactionsTypeEnum;
use App\Enums\ServiceTagEnum;
use App\Enums\TransactionStatusEnum;
use App\Helpers\JsonCamel\JsonCamelHelperFacade;
use App\LogItem;
use App\Models\BillPayment;
use App\Models\BillPaymentItem;
use App\Models\BillPaymentService;
use App\Services\NotificationService;
use App\Services\OBDX\CustomerService;
use App\Http\Controllers\Controller;
use App\Services\UtilityPayementService;
use App\Traits\AuthorizesServices;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Lang;

class BillPaymentController extends Controller
{
    use AuthorizesServices;

    protected function getServiceTags(): array{
        return [
            ServiceTagEnum::COMMERCIAL_ELECTRICITY,
            ServiceTagEnum::MUASALAT_WALLET,
            ServiceTagEnum::FLOOSAK,
            ServiceTagEnum::MASTERCARD,
            ServiceTagEnum::MOBILE,
            ServiceTagEnum::YEMEN_4G,
            ServiceTagEnum::ADSL,
            ServiceTagEnum::LANDLINE,
            ServiceTagEnum::GAMES,
            ServiceTagEnum::MUASALAT_CARD,
            ServiceTagEnum::WATER,
            ServiceTagEnum::ELECTRICYBOARD,
        ];
    }
    /**
     * Display a listing of the resource.
     *
     * @return ?\Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $validator=validator()->make($request->all(),[
            "service"=> 'required|exists:bill_payment_services,id',
            "item"=> 'nullable|exists:bill_payment_items,id',
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "INDEX-TRANSFER-FUND",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_TRANSFER_001",
                        "type"    => "ERROR"
                    ]
                 ]
            ]),400);
        }

        $billPayments=BillPayment::
        with(['bundle'=>function($query){
            $query->select('id','title');
        }])
        ->where('service',$request->service)

        ->where('status',TransactionStatusEnum::COMPLETED->value)
        ->skip( $request->from)
        ->take($request->limit)
        ->orderBy("created_at","DESC");


        if($request->filled('item')){
            $billPayments=$billPayments->withWhereHas('item',function($query)use($request){
                $query->select('id','title')->where('id',$request->item);
            });
        }else{
            $billPayments=$billPayments->with(['item'=>function($query)use($request){
                $query->select('id','title');
            }]);
        }

        if($request->filled('text')){
            $billPayments=$billPayments->where(function($query) use ($request) {
                $query->where('subscriber_number', 'like', '%' . $request->text . '%')
                      ->orWhere('remarks', 'like', '%' . $request->text . '%');
            });
        }
        $billPayments=$billPayments->get();
       // return response()->json($billPayments->toArray());
        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])
        ->additional([
            "transactions"=>BillPaymentData::collect($billPayments->toArray(),BaseNonNullableDataCollection::class)->toArray()
        ])->toArray());
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return ?\Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $service= BillPaymentService::withCache(["catalog:$request->service"],$request->item??null, function ($query) use($request) {
            $service= BillPaymentService::getServiceWithRelations(id:$request->service,item:$request->item??null);
            return BillPaymentDynamicFormData::from($service)->toArray();
        });
        if(!is_null($service)){
            return response()->json( GeneralResponseData::from([
                'status'=>[
                    "result"    => "SUCCESSFUL",
                    "contextID" => "",
                    "message"   => [
                        "title"   => "",
                        "detail"  => "",
                        "code"    => "0",
                        "type"    => "INFO"
                    ]
                ]
            ])
            ->additional($service));
        }
        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "BILL-PAYMNET",
                    "type"    => "ERROR"
                ]
            ]
        ]));
    }
    public function query(Request $request,$asObject=false)
    {
        $inputsToValidate=[
            'service'=>"required",
            'item'=>"required",
            'subscriberNumber'=>"required"
        ];
        $validator=validator()->make($request->all(),$inputsToValidate);
        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "UTILITY-PAYMENT-BALANCE",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_UTILITY-PAYMENT-BALANCE_001",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }
        $service=BillPaymentService::with(['item' => function ($query) {
            $query
            ->where('status',1)
            ->where('id', request()->item);
        }])
        ->where('id',$request->service)
        ->where('status',1)
        ->first();
        $object=new \stdClass();
        $object->ac=$service->payload->ac;
        $object->sc=$service->payload->sc;
        if(BillPaymentServiceCodeEnum::government->value!=$service->payload->sc){
            $object->item=$service->item->payload->item;
        }

        $object->sno=$request->subscriberNumber;
        if($request->filled('extra.sac')){
            $object->sac=$request->input('extra.sac');
        }

        $result=UtilityPayementService::queryV2($object);
        if($asObject){
            return $result;
        }
        return response()->json($result->toAppResponse()->toArray(),200, [], JSON_OBJECT_AS_ARRAY);
    }
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return ?\Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $errorResult=GeneralResponseData::from([
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "STORE-BILL-PAYMENT",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "DIGX_SWITCH_BILL_PAYMENT_100",
                    "type"    => "ERROR"
                ]
            ]
        ]);

        $validator=validator()->make($request->all(),[
            'debitAccountId.displayValue'=>"nullable",
            'debitAccountId.value'=>"required|max:20|min:20",
            'amount.amount'=>"required|numeric",
            'amount.currency'=>"required|in:".join(",",\App\Enums\CurrencyTypeEnum::values()),
            'remarks'=>"nullable|string",
            "subscriberNumber"=> "required",
            "service"=> 'required|exists:bill_payment_services,id',
            "item"=> 'required|exists:bill_payment_items,id',
            "bundle"=> 'nullable|exists:bill_payment_bundles,id',
            'payload'=>"required|array",
            'payload.region'=>"nullable|in:north,south",
            'payload.biller'=>"nullable|string",
            'payload.billerCategory'=>"nullable|string",
            'payload.link'=>"nullable|in:sim,programming",
            'payload.filter'=>"nullable|in:volte,4g,mazaya,net,hadaya",
            'payload.subscriberType'=>"nullable|in:PREPAID,POSTPAID",
            'payload.type'=>"nullable|in:payment,package",
            'payload.city'=>"nullable",
            'payload.lend'=>"nullable|in:0,120,200",
            'extra'=>"nullable|array",
        ]);

        if($validator->fails()){
            $errorResult->status->message->title = join("\n",$validator->errors()->all());
            return response()->json($errorResult,400);
        }
        $billPaymentData= BillPaymentData::from($request->all());



        $item=BillPaymentItem::
        withWhereHas('service',function ($query) {
            $query->where('id', request()->service)->where('status',1);
        })
        ->with(['filters' => function ($query) {
            $id=request()->item;
            $query->whereNull('parent_filter_id')->with([
                'filters' => function ($query) use ($id) {
                    $query->with([
                        'filters' => function ($query) use ($id) {
                            $query->with('options')
                            ->where('model_type', BillPaymentItem::class)
                            ->where('model_id', $id);
                        }
                    ])
                    ->with('options')
                    ->where('model_type', BillPaymentItem::class)
                    ->where('model_id', $id);
                }
            ])->with('options');
        }])
        ->with("options")
        ->whereNotNull('biller')
        ->where('id', request()->item)
        ->where('bill_payment_service_id',request()->service)
        ->where('status',1);

        if($request->filled('bundle')){
            $item=$item->withWhereHas('bundle',function ($query) {
                $query->with("options")->where('id', request()->bundle)->where('status',1);
            });
        }
        $item=$item->first();
        if(!is_null($item)){
            // if(in_array($item->payload?->mt??0,[1,2])){
            //     $billPaymentData->payload->s
            // }
            if(!is_null($item->additional_fields)){
                foreach ($item->additional_fields as $additionalField) {
                    if(!$request->filled("$additionalField")){
                        $errorResult->status->message->title = __("Please make a query & fill the required fields!");
                        return response()->json($errorResult,400);
                    }
                }
            }

            $currentAddress=[
                "item"=>"{$item->payload?->item}",
            ];


            if($request->filled('bundle') && ($item->bundle->payload?->denId??"0000000")!="0000000"){
                $currentAddress['den']=$item->bundle->payload?->denId;
                $billPaymentData->amount=CurrencyAmountData::from($item->bundle->amount);
                $remarks=Lang::get('Bill Payment - (:bundle) for subscriber - (:subscriberNumber)', ['bundle' => $item->bundle->name, 'subscriberNumber' => $billPaymentData->subscriberNumber], 'ar');

                // if(is_null($billPaymentData->remarks) || empty($billPaymentData->remarks)){
                //     $billPaymentData->remarks=Lang::get('Bill Payment - (:bundle) for subscriber - (:subscriberNumber)', ['bundle' => $item->bundle->name, 'subscriberNumber' => $billPaymentData->subscriberNumber], 'ar');
                // }else{
                //     $billPaymentData->remarks=Lang::get('Bill Payment - (:bundle) for subscriber - (:subscriberNumber) [:remarks]', ['bundle' =>$item->bundle->name, 'subscriberNumber' => $billPaymentData->subscriberNumber, 'remarks'=>$billPaymentData->remarks], 'ar');
                // }
            }else{
                $remarks=Lang::get('Bill Payment - (:bundle) for subscriber - (:subscriberNumber)', ['bundle' => $item->name, 'subscriberNumber' => $billPaymentData->subscriberNumber], 'ar');

                // if(is_null($billPaymentData->remarks) || empty($billPaymentData->remarks)){
                //     $billPaymentData->remarks=Lang::get('Bill Payment - (:bundle) for subscriber - (:subscriberNumber)', ['bundle' => $item->name, 'subscriberNumber' => $billPaymentData->subscriberNumber], 'ar');
                // }else{
                //     $billPaymentData->remarks=Lang::get('Bill Payment - (:bundle) for subscriber - (:subscriberNumber) [:remarks]', ['bundle' =>$item->name, 'subscriberNumber' => $billPaymentData->subscriberNumber, 'remarks'=>$billPaymentData->remarks], 'ar');
                // }
            }

            if(($item->payload?->allowOpenAmount==true) && ($billPaymentData->amount->amount<$item->payload?->minAmount || $billPaymentData->amount->amount>$item->payload?->maxAmount)){
                $errorResult->status->message->title = Lang::get('Minimum amount :min Maximum amount :max', ['min' => $item->payload?->minAmount, 'max' => $item->payload?->maxAmount]);
                return response()->json($errorResult,400);
            }

            if("{$item->service->payload?->sc}"==BillPaymentServiceCodeEnum::yemenMobile->value){
                if(!$billPaymentData->debitAccountId->isNorth()){
                   $item->biller="{$item->biller}2";
                }
                $currentAddress['oac']=4002;
            }
            if($request->filled('extra')){
                $currentAddress+=$request->extra;
            }
            if($request->filled('extra.billno')){

                $response = $this->query($request,true);
                $amount=$response->getAmountbyBillNo($request->input('extra.billno'));
                if(is_null($amount)){
                    $errorResult->status->message->title = __("The violation not found!");
                    return response()->json($errorResult,400);
                }
                $billPaymentData->amount=CurrencyAmountData::from([
                    'amount'=>$amount,
                    'currency'=>$item->payload->amountCurrency??$item->payload->costCurrency
                ]);
            }
            $pair= [[
                "name"=> "com.ofss.digx.cz.domain.payment.entity.transfer.CZBillPayment.currentAddress",
                "value"=> json_encode($currentAddress),
                "genericName"=> "com.ofss.digx.cz.domain.payment.entity.transfer.CZBillPayment.currentAddress"
            ]];
            $processingType=$item->payload?->ac==7200?'bundle':($item->payload?->ac==7700?'espay':null);
            if(!is_null($processingType)){
                $pair[]=[
                    "name"=> "com.ofss.digx.cz.domain.payment.entity.transfer.CZBillPayment.processingType",
                    "value"=> $processingType,
                    "genericName"=> "com.ofss.digx.cz.domain.payment.entity.transfer.CZBillPayment.processingType"
                ];
            }

            if($request->filled('payload.lend')){
                $mergedField=$item->fields->toCollection()->firstWhere(function($field){
                    return $field->name=='payload:lend';
                });
                $option=$mergedField?->options?->toCollection()->firstWhere(function($option)use($request){
                    return $option->id==$request->input('payload.lend');
                });
                if(is_null($option)){
                    $errorResult->status->message->title = __( "The selected lend not correct!");
                    return response()->json($errorResult,400);
                }

                $billPaymentData->amount->amount+=$request->input('payload.lend');
            }

            $billDate=CustomerService::currentDate();
            $requestData=[
                'debitAccountId'=>$billPaymentData->debitAccountId->toArray(),
                'amount'=>$billPaymentData->amount->toArray(),
                'remarks'=>$remarks,
                'billerId'=>$item->biller,
                'billNumber'=>$billPaymentData->subscriberNumber,
                'billDate'=>$billDate,
                'relationshipNumber'=>$billPaymentData->subscriberNumber,
                'dictionaryArray'=>[
                    [
                        "nameValuePairDTOArray"=> $pair
                    ]
                ]
            ];

        }else{
            $errorResult->status->message->title = __( "The service not available right now!");
            return response()->json($errorResult,400);
        }

        $valid=false;
        $message="You don't have the correct account!";
        $result=CustomerService::account($request,$billPaymentData->debitAccountId->value);
        if($result instanceof AccountData){
            $account=$result;
            $valid=$account->status=="ACTIVE" && $account->isCash() && $account->currencyId==$billPaymentData->amount->currency&&
            $account->allowedService(AccountConfigData::utilityPayment);
        }

        if(!$valid){
            $errorResult->status->message->title = __( $message);
            return response()->json($errorResult,400);
        }



        $result=CustomerService::billPayment($requestData);
        $billPaymentData->referenceId=$result->paymentId;
        $billPayment=$billPaymentData->toDB();
        $billPayment["request"]=$requestData;


        $billPayment=BillPayment::create($billPayment);
        LogItem::store($billPayment);

        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "INI-INVOICE",
                "message"   => [
                    "title"   => __("The initial request has been created"),
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])->additional([
            "paymentId"=>$billPayment->id,
        ])->transform());

    }

    /**
     * Display the specified resource.
     *
     * This method generates a PDF document with the title "Hello World" and content "Hello World".
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\BillPayment $billPayment
     * @return void
     */
    public function show(Request $request,BillPayment $billPayment)
    {
        if($billPayment->status!=TransactionStatusEnum::INIT->value){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "OIL-DETAILS",
                    "message"   => [
                        "title"   => __("Transaction not found!"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_OIL_101",
                        "type"    => "ERROR"
                    ]
                ]
            ]),400);
        }

        return JsonCamelHelperFacade::json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "OIL-DETAILS",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])->additional([
            "transferDetails"=>['remarks'=>$billPayment->request->remarks]+$billPayment->only('id','status','debit_account_id','subscriber_number','amount','fee','remarks'),
            "request"=>$billPayment->request,
        ])->transform());
    }
    protected function receipt(Request $request,BillPayment $bill)
    {
        $validator=validator()->make($request->all(),[
            'showAccountInfo' => 'Nullable|numeric|in:0,1',
            'showAccountNumber' => 'Nullable|numeric|in:0,1',
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "INDEX-TRANSFER-FUND",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_TRANSFER_001",
                        "type"    => "ERROR"
                    ]
                 ]
            ]),400);
        }

        $this->generateReceipt($this->getReceiptData($bill));
    }
    protected function getReceiptData(BillPayment $billPayment): ReceiptData
    {
        return ReceiptData::from([
            "id"=> $billPayment->id,
            "date"=> date_format(date_create($billPayment->created_at), "Y-m-d H:i:s"),
            "title"=> __("Bill payment"),
            "beneficiary"=> $billPayment->subscriber_number,
            "statement"=> $billPayment->request->remarks,
            "details"=> [
                "referenceId"=> $billPayment->reference_id,
                "debitAccountId"=> $billPayment->debit_account_id,
                "remarks"=>$billPayment->remarks,
                "amount"=>$billPayment->amount,
                "fee"=>$billPayment->fee??null,
            ]
        ]);
    }
    /**
     * Update the transfer fund details.
     *
     * This method retrieves the internal transfer details using the provided ID,
     * updates the transfer details, and processes the transfer based on the payment type.
     * Currently, it handles 'INDIADOMESTICFT' payment type.
     *
     * @param \Illuminate\Http\Request $request The HTTP request instance.
     * @param \App\Models\BillPayment $billPayment
     * @return \Illuminate\Http\JsonResponse The JSON response containing the result of the update operation.
     */
    public function update(Request $request,BillPayment $billPayment)
    {
        if($billPayment->status!=TransactionStatusEnum::INIT->value){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "OIL-DETAILS",
                    "message"   => [
                        "title"   => __("Transaction not found!"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_OIL_101",
                        "type"    => "ERROR"
                    ]
                ]
            ]),400);
        }
        LogItem::store($billPayment);

        $result=CustomerService::patchBillPayment($billPayment->reference_id,$billPayment->payload->subscriberType??null);

        if(isset($result->externalReferenceId)){
            $billPayment->payment_result=[
                'amount'=>new PaymentResultData(
                    $billPayment->reference_id,
                    $result->externalReferenceId,
                    InvoiceTransactionsTypeEnum::Payment->value
                ),
            ];
            $billPayment->reference_id=$result->externalReferenceId;
        }

        $billPayment->status=$result->flag;
        $billPayment->save();
        if($result->flag==TransactionStatusEnum::COMPLETED->value){
            $result->receipt=$this->getReceiptData($billPayment)->toArray();
        }
        NotificationService::sendMessagesToParty([
            [
                'title'=>__("Bill payment"),
                'body'=> $billPayment->request->remarks,
                'type'=>'operation',
            ]
        ]);
        return response()->json($result);

    }
}
