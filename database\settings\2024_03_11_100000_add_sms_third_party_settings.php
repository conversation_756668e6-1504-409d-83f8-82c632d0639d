<?php

use <PERSON><PERSON>\LaravelSettings\Migrations\SettingsMigration;

class AddSmsThirdPartySettings extends SettingsMigration
{
    public function up(): void
    {
        if(env('APP_FLAG',"banky.commercial")=="banky.islamic"){
            $this->migrator->addEncrypted('thirdParty.sms',array (
                'url' => 'http://10.47.1.101:8843/api/Send/SendSMS/v1',
                'client_id' => 'LBankyISL',
                'client_secret' => 'Banky$321',
                'instid' => 'YKBIslamic',
                'is_test' => '1',
                'test_data' => array (
                    'url' => 'http://10.47.1.101:8843/api/Send/SendSMS/v1',
                    'client_id' => 'LBankyISL',
                    'client_secret' => 'Banky$321',
                    'instid' => 'YKBIslamic',
                )
            ));
        }else{
            $this->migrator->addEncrypted('thirdParty.sms',array (
                'url' => 'http://10.47.1.101:8843/api/Send/SendSMS/v1',
                'client_id' => 'LBanky',
                'client_secret' => 'Banky$321',
                'instid' => 'YKB',
                'is_test' => '1',
                'test_data' => array (
                    'url' => 'http://10.47.1.101:8843/api/Send/SendSMS/v1',
                    'client_id' => 'LBanky',
                    'client_secret' => 'Banky$321',
                    'instid' => 'YKB',
                )
            ));
        }
        
    }
    public function down()
    {
        $this->migrator->delete('thirdParty.sms');
    }
}