<?php

namespace App\Data;
use <PERSON><PERSON>\LaravelData\Data;

class BaseNonNullableData extends Data
{
    public function toArray(): array
    {
        return $this->encodeJson(parent::transform());
    }
    public function encodeJson($value)
    {
        if ($value instanceof Arrayable) {
            return $this->encodeArrayable($value);
        } else if (is_array($value)) {
            return $this->encodeArray($value);
        } else if (is_object($value)) {
            return $this->encodeArray((array) $value);
        } else {
            return $value;
        }
    }

    /**
     * Encode a arrayable
     */
    public function encodeArrayable($arrayable)
    {
        $array = $arrayable->toArray();
        return $this->encodeJson($array);
    }

    /**
     * Encode an array
     */
    public function encodeArray($array)
    {
        $newArray = [];
        foreach ($array as $key => $val) {
            $data=$this->encodeJson($val);
            if(!(is_array($data) && !count($data))){
                $newArray[$key] = $data;
            }
        }
        return $this->removeNullableValues($newArray);
    }
    /**
     * Remove nullable values from a multidimensional array.
     *
     * @param array $array
     * @return array
     */
    function removeNullableValues(array $array): array
    {
        return array_filter($array, function($value) {
            if (is_array($value)) {
                return !empty($this->removeNullableValues($value));
            }
            return !is_null($value);
        });
    }
}
