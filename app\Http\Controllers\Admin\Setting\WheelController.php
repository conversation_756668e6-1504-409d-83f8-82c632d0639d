<?php

namespace App\Http\Controllers\Admin\Setting;


use App\Data\Wheel\WheelConfigData;
use App\Data\Wheel\WheelItemsData;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use \App\Settings\WheelSettings;
use Auth;

class WheelController extends Controller
{

    public function __construct(Request $request){$this->middleware('auth');}
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if (! Auth::user()->canAny(['setting.wheel.*','setting.wheel.list'])) return abort(401);

        $filter=$request->all();
        $items=app(WheelSettings::class)->toCollection();

        if($request->filled('searchname')){
            $items=$items->filter(function ($element,string $key) use($filter){
                return $key== str_contains($key,$filter['searchname']);
            });
        }
        return view('default.admin.setting.wheel.index')->with('items', $items)->with('filter', $filter);
    }


        /**
     * Display the specified resource.
     *
     * @param  Request $request
     * @return ?\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function create(Request $request){
       // return \Blade::render($view);

        // switch($request->input("id")){

        //     case 'limitPackageConfig':
        //         $item=LimitLinkageData::from([
        //             "limits" => [
        //                 [
        //                     "currency" => "YER",
        //                     "limitType" => "TXN",
        //                     "amountRange" => [
        //                         "minTransaction" => [
        //                             "currency" => "YER",
        //                             "amount" => 100
        //                         ],
        //                         "maxTransaction" => [
        //                             "currency" => "YER",
        //                             "amount" => 1000000
        //                         ]
        //                     ]
        //                 ],
        //                 [
        //                     "currency" => "YER",
        //                     "limitType" => "PER",
        //                     "maxAmount" => [
        //                         "currency" => "YER",
        //                         "amount" => 3000000
        //                     ],
        //                     "maxCount" => 1000,
        //                     "periodicity" => "DAILY"
        //                 ],
        //                 [
        //                     "currency" => "YER",
        //                     "limitType" => "PER",
        //                     "maxAmount" => [
        //                         "currency" => "YER",
        //                         "amount" => 10000000
        //                     ],
        //                     "maxCount" => 1000,
        //                     "periodicity" => "MONTHLY"
        //                 ]
        //             ],
        //         ]);
        //         return response(view('default.admin.setting.wheel.components.limit-package-item')
        //         ->with('key',$request->key)
        //         ->with('item', $item)/*,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED*/);
        //     default:
        //         return response(view('default.admin.setting.wheel.components.gold-service-item-config')
        //         ->with('isAjax',$request->ajax())
        //         ->with('key',$request->key)
        //         ->with('subChildKey',$request->subChildKey)
        //         ->with('subChildItem',null)/*,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED*/);
        //     }
    }

    /**
     * Display the specified resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function show(Request $request,$id){
        if (! Auth::user()->canAny(['setting.wheel.*','setting.wheel.view','setting.wheel.edit'])) return abort(401);

        $item=app(WheelSettings::class)->toCollection()
        ->filter(function ($element,string $key) use($id){
            return $key== $id;
        })->first();

        if(is_null( $item)){
            abort(\Symfony\Component\HttpFoundation\Response::HTTP_NOT_FOUND);
        }
        $contents =null;
        if($id=='wheelItems'){
            $contents["types"]=[
                "0"=>__("Bomb"),
                "1"=>__("Reward"),
            ];
        }

        return view('default.admin.setting.wheel.view')
        ->with('id', $id)
        ->with('item', $item)
        ->with('contents', $contents);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $service
     * @return \Illuminate\Http\Response | \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request,string $id)
    {
        // $key="goldTypes.*.services.*.icon";
        // $reault=$this->mmmm($key);
        // dd($reault);
        // return;

        if (! Auth::user()->canAny(['setting.wheel.*','setting.wheel.edit'])) abort(401);

        $item=app(WheelSettings::class)->toCollection()
        ->filter(function ($element,string $key) use($id){
            return $key== $id;
        })->first();

        if(is_null( $item)){
            abort(\Symfony\Component\HttpFoundation\Response::HTTP_NOT_FOUND);
        }

        $validations=[];
        switch($id){
            case 'wheelConfig':
                $validations+=[
                    "allowSpinningWheel" => 'required|numeric|max_digits:1|max:1|in:0,1',
                    "dailyMaxAmount.amount"=>'required|numeric',
                    "dailyMaxAmount.currency"=>'required|in:'.join(",",\App\Enums\CurrencyTypeEnum::values()),
                    "allowAccumulativeAmountThroughMonth" => 'required|numeric|max_digits:1|max:1|in:0,1',
                    "autoChangeProbability" => 'required|numeric|max_digits:1|max:1|in:0,1',

                ];
                break;
            case 'wheelItems':
                $validations+=[
                    "items.*.id" => 'required|numeric',
                    "items.*.name" => 'required|string|max:6',
                    "items.*.value" => 'required|numeric',
                    "items.*.type" => 'required|numeric|max_digits:1|max:1|in:0,1',
                    "items.*.color" => ['required', 'regex:/^#(?:[0-9a-fA-F]{3}){1,2}$/'],
                    "items.*.weight" => 'required|numeric',
                    "items.*.status" => 'required|numeric|max_digits:1|max:1|in:0,1',
                    "items.*.maxAmountPerDay" => 'required|numeric',

                ];
                break;
        }

        $this->validate($request,$validations);


        switch($id){
            case 'wheelConfig':
                $configData=WheelConfigData::from($request->all());
                break;
            case 'wheelItems':
                $this->setImage('items.*.image',"wheel");
                $this->validate($request,[
                    "items.*.image" => 'required',
                ]);
                $configData=WheelItemsData::from($request->all());
                break;
        }

        $settings=app(WheelSettings::class);
        $settings->{"$id"}=$configData;
        $settings->save();

        return back()->with('success',__("Operation accomplished successfully"));
    }



    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id){
        if (! Auth::user()->canAny(['setting.wheel.*','setting.wheel.delete'])) abort(401);

    }

}
