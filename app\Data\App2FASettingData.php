<?php

namespace App\Data;

use Spa<PERSON>\LaravelData\Data;

class App2FASettingData extends Data
{
    public ?int $firstTimeOnly;

    public ?int $attempts;
    public ?int $resends;
    public ?int $status;
    public function __construct(
        public ?array $bypassParties=[],
        public ?int $onlyOneDeviceAllowed=0,
        public ?array $allowedPackageIdsToPassOnlyOneDevice=[]
    ) {
    }
}
