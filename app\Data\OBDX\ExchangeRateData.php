<?php

namespace App\Data\OBDX;

use App\Data\BaseNonNullableData;
use App\Data\Classes\BranchData;
use App\Data\Classes\ProductData;
use App\Data\CurrencyAmountData;
use Illuminate\Support\Collection;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Data;

class ExchangeRateData extends BaseNonNullableData
{
    public function __construct(
        public ?CurrencyAmountData $debitRate=null,
        public ?CurrencyAmountData $creditRate=null,
        public ?CurrencyAmountData $rate=null,
        public ?CurrencyAmountData $rateNet=null,
        public ?CurrencyAmountData $total=null,
        public ?CurrencyAmountData $limit=null
    ) {
    }
    // public function exceptProperties() : array
    // {
    //     return [
    //         'debitRate'=>is_null($this->debitRate),
    //         'creditRate'=>is_null($this->creditRate),
    //         'rateNet'=>is_null($this->rateNet)

    //     ];
    // }
}
