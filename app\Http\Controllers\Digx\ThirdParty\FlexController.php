<?php

namespace App\Http\Controllers\Digx\ThirdParty;
use App\Data\GeneralResponseData;
use App\Enums\CurrencyTypeEnum;
use App\Models\ExchangeRate;
use Illuminate\Http\Client\Response;
use App\Http\Controllers\Controller;
use App\Models\Invoice;
use App\Models\User;
use App\Enums\InvoiceTransactionsTypeEnum;
use App\Enums\TransactionStatusEnum;
use App\LogItem;
use App\Models\InvoiceTransaction;
use Illuminate\Http\Request;
use PhpParser\Node\Scalar\String_;
use Validator;
use Illuminate\Support\Str;
use App\Services\FlexService;

class FlexController extends Controller
{

    /**
     * Initiate
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeExchangeRate(Request $request)
    {
        $validator=validator()->make($request->all(),[
            '*.brn'=>"required|max:3|min:3",
            '*.type'=>"required|in:ATM,CASH,REVAL,STANDARD",
            '*.cur_1'=>"required|max:3|min:3",
            '*.cur_2'=>"required|max:3|min:3|in:YER",
            '*.buy_rate'=>"required|numeric",
            '*.sell_rate'=>"required|numeric"
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-FLEX-EX",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_FLEX_EX_100",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }
        $equlivent=31.1;
        $exchangeAreaConfig=app(\App\Settings\ConfigSettings::class)->exchangeAreaConfig;

        $exchangeList=collect($request->all())->where('type',"ATM")->map(function($item) use($exchangeAreaConfig,$equlivent){
            //$area=collect($exchangeAreaConfig->areaBranches)->f
            $area=collect($exchangeAreaConfig->areaBranches)->filter(function ($element,string $key) use($item) {
                return in_array($item['brn'],$element);
            })->keys()->first();
            if($item['cur_1']==CurrencyTypeEnum::G24->value){
                $item['buy_rate']=((double)$item['buy_rate']*$equlivent);
                $item['sell_rate']=((double)$item['sell_rate']*$equlivent);
            }
            return [
                "branch"=> $item['brn'],
                "type"=> $item['type'],
                "currency1"=> $item['cur_1'],
                "currency2"=> $item['cur_2'],
                "buy_rate"=> (double)$item['buy_rate'],
                "sell_rate"=>(double)$item['sell_rate'],
                "area"=>$area,
                "created_at"=>\Carbon\Carbon::now()->toDateTimeString()
            ];
        });
        ExchangeRate::insert($exchangeList->values()->toArray());

        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "INI-FLEX-EX",
                "message"   => [
                    "title"   => __("Operation accomplished successfully"),
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ]));

    }


}
