<?php

namespace App\Http\Controllers\Admin;
use App\Http\Controllers\Controller;
use App\Models\CustomerType;
use App\Models\NewDevice;
use App\Models\PartyVerify;
use App\Scopes\CustomerScope;
use App\Scopes\UsernameScope;
use App\Services\OBDX\AdminService;
use Illuminate\Http\Request;
use Auth;



class NewDeviceController extends Controller
{

    public function __construct(Request $request){$this->middleware('auth');}

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response|\Illuminate\Contracts\View\View
     */
    public function index(Request $request)
    {
        if (! Auth::user()->canAny(['new_device.*','new_device.view','new_device.list'])) return abort(401);

        $filter=$request->all();

        $items=NewDevice::orderBy('created_at','desc');
        $items=$items->paginate(15);

        return view('default.admin.new_device.index')
        ->with('items', $items)
        ->with('filter', $filter);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        if (! Auth::user()->canAny(['new_device.*','new_device.create']))
            return abort(401);

        $this->validate($request,[
            'party_id' => 'required',
            'username' => 'nullable',
        ]);


        $partyVerifies=PartyVerify::withoutGlobalScope(CustomerScope::class)
        ->withoutGlobalScope(UsernameScope::class)
        ->select('id','party_id','username','phone_number', 'device_key', 'terminal','expiry_date', 'status','customer_type_id','created_at')
        ->where(function($query) use($request){
            $query->where("party_id",$this->getPartyId($request->party_id))
            ->orWhere("party_id",$request->party_id);
        });
        if($request->filled('username')){
            $partyVerifies=$partyVerifies->where('username',$request->username);
        }
        $partyVerifies=$partyVerifies->get();

        if(!count($partyVerifies)){
            return back()->with('error',__("No party found!"));
        }

        $partyId=$request->party_id;
        if(strlen($partyId)==6){
            $partyId="0$request->party_id";
        }

        if(strlen($partyId)==7){
            $params=[
                "partyId"=>$partyId,
                "userType"=>'retailuser'
            ];
        }else{
            $params=[
                "username"=>$partyId,
                "userType"=>'retailuser'
            ];
        }

        $partyVerify=$partyVerifies->first();
        if($request->filled('username') && $partyVerify->customerType!=CustomerType::RETAIL){
            $params["username"]=$request->username;
            $params["userType"]='corporateuser';

        }

        $object=new \stdClass();
        $customerInfo = AdminService::customerInfo($object,$params);

        if(is_null($customerInfo) || !is_object($customerInfo)){
            $userName=$partyId;
        }else{
            $userName=html_entity_decode($customerInfo->firstName)." ".html_entity_decode($customerInfo->middleName??"")." ".html_entity_decode($customerInfo->lastName);
        }


        NewDevice::create([
            "note"=>$request->note,
            "party_id"=>$request->party_id,
            "party_name"=>$userName,
            "user_id"=>auth()->user()->id,
            "devices"=>$partyVerifies->toArray()
        ]);

        $partyVerifies=PartyVerify::withoutGlobalScope(CustomerScope::class)
        ->withoutGlobalScope(UsernameScope::class)
        ->where(function($query) use($request){
            $query->where("party_id",$this->getPartyId($request->party_id))
            ->orWhere("party_id",$request->party_id);
        });
        if($request->filled('username')){
            $partyVerifies=$partyVerifies->where('username',$request->username);
        }
        $partyVerifies->delete();

       // $partyVerifies->delete();
        return back()->with('success',__("Seccussfully clear all user devices!"));
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\NewDevice $newDevice
     * @return ?\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function show($id){
        $partyVerifies=PartyVerify::withoutGlobalScope(CustomerScope::class)
        ->withoutGlobalScope(UsernameScope::class)
        ->select('id','party_id','username','phone_number', 'device_key', 'terminal', 'status','customer_type_id','created_at')
        ->where(function($query) use($id){
            $query->where("party_id",$this->getPartyId($id))
            ->orWhere("party_id",$id);
        })
        ->get();

        return response(
            view('default.admin.new_device.devices')
            ->with('items',$partyVerifies)
            ->with('action',true)
        );
    }
    public function devices(NewDevice $newDevice){
        return response(view('default.admin.new_device.devices')
        ->with('items',json_decode(json_encode($newDevice->devices))));

    }

    public function getPartyId($id)
    {
        if(substr($id,0,1)!="0"){
            $partyId="0{$id}";
        }else{
            $partyId=substr($id,1);
        }
        return $partyId;
    }
}
