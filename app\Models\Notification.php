<?php

namespace App\Models;

use App\Data\AppConfigData;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Notification extends Model
{
    protected $table = 'notifications';
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = ['party_id','type','user_id','title','body','image','action','service_id','url','topic','status','result'/*,'at_date','completed'*/];
    protected $appends = ['messageId'];
    protected $casts = [
        'result' => 'array',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];

    public function user()
    {
        return $this->belongsTo('App\Models\User',"user_id");
    }

    public function messageUserMappings()
    {
        return $this->hasMany(CustomerNotification::class,"notification_id");
    }

    public function getMessageIdAttribute()
    {
        return [
            "displayValue"=>"{$this->id}",
            "value"=>"{$this->id}",
        ];
    }

    public function customerType()
    {
        return $this->hasOne('App\Models\CustomerType', 'topic','topic');
    }

    public function service()
    {
        return $this->belongsTo(Service::class,"service_id");
    }
}
