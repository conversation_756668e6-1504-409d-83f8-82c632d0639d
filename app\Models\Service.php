<?php

namespace App\Models;

use App\Data\AppConfigData;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Service extends Model
{
    use HasFactory;
    protected $fillable = [
        "id",
        "tag",
        "type",
        "title",
        "description",
        "icon",
        "banner",
        "url",
        "help_url",
        "service_id",
        "trending_service",
        "status",
        "quick_access",
        "allow_more",
        "sort",
        "app_version",
        "os_types",
    ];
    protected $appends = ['name'];

    protected $casts = [
        'title' => 'object',
        'description' => 'object',
        'banner' => 'object',
        'os_types' => 'array'
    ];
    protected $hidden = [
        'sort','service_id','modify_user_id','created_at','updated_at'
    ];
    /**
     * The "booting" method of the model.
     *
     * @return void
     */
    protected static function boot(){
        parent::boot();
        static::saving(function() {
            AppConfigData::resetCacheSignature();
        });
        static::deleting(function($item) {
            AppConfigData::resetCacheSignature();
        });
    }
    public function getNameAttribute()
    {
        $local=app()->getLocale();
        return  $this->title?->{"$local"};
    }
    public function items()
    {
        return $this->hasMany('App\Models\Service', "service_id");
    }

    // public function customerRoles()
    // {
    //     return $this->belongsToMany(CustomerType::class, 'service_customer_roles','service_id','customer_type_id')
    //     ->select('id','service_customer_roles.role_identifer');
    // }
    public function customerTypes()
    {
        return $this->belongsToMany(CustomerType::class, 'service_customer_types','service_id','customer_type_id')
        ->using(ServiceCustomerType::class)
        ->withPivot('service_id','customer_type_id','page_service_id','status','status_type')
        ->as('pivot')
        ->select('id','name','service_customer_types.service_id','service_customer_types.customer_type_id','service_customer_types.page_service_id','service_customer_types.status','service_customer_types.status_type');
    }
    public function customerType()
    {
        return $this->hasOneThrough('App\Models\CustomerType', 'App\Models\ServiceCustomerType','service_id','id','id','customer_type_id')
        ->select('id','name','service_customer_types.page_service_id','service_customer_types.status','service_customer_types.status_type');//->withPivot('status');
    }
    public function serviceCustomerType()
    {
        return $this->hasOne(ServiceCustomerType::class, 'service_id', 'id');
    }
    public function logs()
    {
        $_name=Service::class;
        return $this->hasMany('App\Models\LogEntry', "model_id")->where('model',$_name)->where('type','request')->with('relatedEntries');
    }

    public static function getServiceWithTag($tag): Service|null
    {
        return static::with(['serviceCustomerType'=>function($query) {
            $query->select('customer_type_id','service_id','service_customer_types.status','status_type')
            ->where('customer_type_id',auth()->user()->customerType)
            ->whereNull('page_service_id')
            ->where('service_customer_types.status','<>',0);

            if(isset(auth()->user()->customerRole)){
                $query->whereHas('customerRole', function ($query)  {
                    return $query->where('role_identifer',auth()->user()->customerRole);
                });
            }

            return $query;
        }])
        ->whereNotNull('tag')
        ->where('tag',$tag)
        ->first();
    }
}
