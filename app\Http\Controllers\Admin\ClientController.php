<?php

namespace App\Http\Controllers\Admin;
use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Lara<PERSON>\Passport\Http\Controllers\ClientController as CT;
use <PERSON><PERSON>\Passport\Passport;
use Auth;
class ClientController extends CT
{
    public function index(Request $request)
    {
        if (! Auth::user()->canAny(['management.passport.*','management.passport.list'])) return abort(401);

        // $_request = Request::create(
        //     env('APP_URL').'/oauth/clients',
        //     'GET'
        // );
        // $clients=json_decode(\Illuminate\Support\Facades\Route::dispatch($_request)->getContent());

        // $_request = Request::create(
        //     env('APP_URL').'/oauth/tokens',
        //     'GET'
        // );
        // $tokens=json_decode(\Illuminate\Support\Facades\Route::dispatch($_request)->getContent());

        return view('default.admin.passport.index')
        ->with('clients', Passport::client()->all());
    }

    public function show(Request $request, $clientId){
        if (! Auth::user()->canAny(['management.passport.*','management.passport.view','management.passport.edit'])) return abort(401);

        $client = $this->clients->find($clientId);

        if (! $client) {
            return new \Illuminate\Http\Response('', 404);
        }

        $users =User::where('id', $client->user_id)->pluck('name','id');

        return view('default.admin.passport.view')
        ->with('users', $users)
        ->with('item', $client);

    }

    public function create(Request $request){
        if (! Auth::user()->canAny(['management.passport.*','management.passport.create'])) return abort(401);

        return view('default.admin.passport.view');

    }

    /**
     * Store a new client.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Laravel\Passport\Client|array
     */
    public function store(Request $request)
    {
        if (! Auth::user()->canAny(['management.passport.*','management.passport.create'])) return abort(401);

        $this->validation->make($request->all(), [
            'name' => 'required|max:191',
            'user_id' => 'required|exists:users,id',
            //'redirect' => ['required', $this->redirectRule],
            'confidential' => 'boolean',
        ])->validate();

        $client = $this->clients->create(
            $request->user_id, $request->name, $request->redirect??"http://localhost",
            "users", false, true, (bool) $request->input('confidential', true)
        );

        if (Passport::$hashesClientSecrets) {
            return ['plainSecret' => $client->plainSecret] + $client->toArray();
        }
        $client->makeVisible('secret');

        return redirect("/admin/passport/$client->id")
        ->with('success',__("Operation accomplished successfully"));
        return back()->with('success',__("Operation accomplished successfully"));

       // return $client->makeVisible('secret');
    }

    /**
     * Update the given client.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $clientId
     * @return \Illuminate\Http\Response|\Laravel\Passport\Client
     */
    public function update(Request $request, $clientId)
    {
        if (! Auth::user()->canAny(['management.passport.*','management.passport.edit'])) return abort(401);

        $client = $this->clients->find($clientId);

        if (! $client) {
            return new \Illuminate\Http\Response('', 404);
        }

        $this->validation->make($request->all(), [
            'name' => 'required|max:191',
            //'redirect' => ['required', $this->redirectRule],
        ])->validate();

        $this->clients->update(
            $client,
            $request->name,
            $request->redirect??$client->redirect
        );
        return back()->with('success',__("Operation accomplished successfully"));

    }

    public function destroy(Request $request, $clientId)
    {
        if (! Auth::user()->canAny(['management.passport.*','management.passport.delete'])) return abort(401);
        $client = $this->clients->find($clientId);

        if (! $client) {
            return new \Illuminate\Http\Response('', 404);
        }
        $this->clients->delete(
            $client
        );
        return back()->with('success',__("Operation accomplished successfully"));

    }
}
