<?php

namespace App\Data;

use <PERSON><PERSON>\LaravelData\Data;
use Spatie\LaravelData\Normalizers\ObjectNormalizer;

class GeneralItemData extends BaseNonNullableData
{
    public ?string $id;

    public ?NameData $name;
    public ?NameData $description;
    public ?string $date;

    public ?string $icon;

    public ?string $url;
    public ?int $status;
    public ?int $enabled;
    public ?NameData $message;

    // protected function exceptProperties() : array
    // {
    //     return [
    //         'id'=>is_null($this->id),
    //         'name'=>is_null($this->name),
    //         'icon'=>is_null($this->icon),
    //         'url'=>is_null($this->url),
    //         'status'=>is_null($this->status),
    //         'enabled'=>is_null($this->enabled),
    //         'message'=>is_null($this->message),
    //     ];
    // }
}
