<?php

use App\Enums\CurrencyTypeEnum;
use App\Models\CustomerType;
use Spa<PERSON>\LaravelSettings\Migrations\SettingsMigration;

class AddPNPLConfigSettings extends SettingsMigration
{
    public function up(): void
    {
        $this->migrator->add('config.pnplConfig',array (
            'activity'=>null,
            'sector'=>null,
            'product'=>null,
            "amount" => [
                "amount" => 10000.0,
                "currency" => "YER",
            ]
        ));

    }

    public function down()
    {
        $this->migrator->delete('config.pnplConfig');
    }
}
