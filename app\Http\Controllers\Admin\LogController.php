<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\LogEntry;
use App\Models\MyTelescopeEntry;
use Carbon\Carbon;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Telescope\Contracts\EntriesRepository;
use <PERSON><PERSON>\Telescope\EntryUpdate;
use <PERSON><PERSON>\Telescope\Storage\EntryQueryOptions;

class LogController extends Controller
{
    /**
     * LogController constructor.
     * Apply the 'auth' middleware to all methods in this controller.
     *
     * @param  \Illuminate\Http\Request  $request
     */
    public function __construct(Request $request)
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of the log entries.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Contracts\View\View|\Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        // Validate the request inputs
        $request->validate([
            'fromDate' => 'nullable|date',
            'toDate' => 'nullable|date',
            'action' => 'nullable|in:0,1',
            'type' => 'nullable|in:501,500,-1'
        ]);

        // Check if the user has the required permissions
        if (! \Auth::user()->canAny(['log.*','log.list'])) {
            abort(401);
        }
        if (!$request->filled('fromDate')) {
            $request->query->add(['fromDate'=>date('Y-m-d')]);
        }

        if(\Auth::user()->hasRole('developer')){
            if (!$request->filled('toDate')) {
                $request->query->add(['toDate'=>date('Y-m-d')]);
            }
        } else {
            $request->query->add(['toDate'=>$request->fromDate]);
            if (!$request->filled('action')) {
                $request->query->add(['action'=>"0"]);
            }
        }

        $filters = $request->all();

        $start = Carbon::parse($filters['fromDate'])->startOfDay()->toDateTimeString();
        $end = Carbon::parse($filters['toDate'])->endOfDay()->toDateTimeString();

        // Create filter options from the request
        $filter = EntryQueryOptions::fromRequest($request);

        // Get the log entries with the specified filter options and order them by descending sequence
        if(env('DB_CONNECTION')=='oracle'){
            $items = MyTelescopeEntry::withTelescopeOptions('request', $filter)
            ->whereRaw("created_at between TO_DATE(?,'YYYY-MM-DD HH24:MI:SS') and TO_DATE(?,'YYYY-MM-DD HH24:MI:SS') and EXISTS( select entry_uuid from DIGX_LITE_TELESCOPE_ENTRIES_TAGS WHERE entry_uuid=uuid AND tag=?)",[$start,$end,'error'])
            ->orderByDesc('sequence');

        }else{
            $items = MyTelescopeEntry::withTelescopeOptions('request', $filter)
            ->whereRaw("created_at between ? and ? and EXISTS( select entry_uuid from DIGX_LITE_TELESCOPE_ENTRIES_TAGS WHERE entry_uuid=uuid AND tag=?)",[$start,$end,'error'])
            ->orderByDesc('sequence');

        }

        // Filter by customer type if provided in the request
        if ($request->filled('action')) {
            if ($request->action == "0") {
                $items = $items->whereNull('content->resolved_at');
            } else if ($request->action == '1'){
                $items = $items->whereNotNull('content->resolved_at');
            }
        }

        // Filter by type if provided in the request
        if ($request->filled('type')) {
            if (in_array($request->type, ['501', '500'])) {
                $items = $items->whereRaw("json_value(CONTENT,'$.response_status')=?",[$request->type]);
            } else if ($request->type == '-1'){
                $items = $items->whereRaw("json_value(CONTENT,'$.response_status') not in(?,?)",['501','500']);
            }
        }

        // Paginate the items with 15 items per page
        $items = $items->paginate(15);

        $actions = [];
        if(\Auth::user()->hasRole('developer')){
            $actions[""] = __("All");
        }
        $actions = $actions + [
            "0" => __("Unsolved"),
            "1" => __("Resolved"),
        ];

        $types = [
            "" => __("All"),
            "-1" => __("Unknown"),
            "501" => __("High"),
            "500" => __("Critical"),
        ];

        // Return the view with the paginated items and the filter data
        return view('default.admin.log.index')
            ->with('items', $items)
            ->with('actions', $actions)
            ->with('types', $types)
            ->with('filter', $request->all() + $filters);
    }

    //function highlightPhpStringWithLineNumbers
    /**
     * Display the specified resource.
     *
     * @param  int|string  $id  The ID of the log entry to display.
     * @return \Illuminate\Contracts\View\View
     */
    public function show($id)
    {
        // Check if the id is numeric
        if (is_numeric($id)) {
            // Find the LogEntry by id
            $logEntry = LogEntry::find($id);
            if($logEntry->type!="request"){
                $logEntry->load('parent');
            }

        } else {
            if (! \Auth::user()->canAny(['log.*','log.view'])) {
                return abort(401);
            }
            // Find the MyTelescopeEntry by id
            $logEntry = (new MyTelescopeEntry)->find($id);
            $logEntry->tags=$logEntry->tags?->pluck('tag')->toArray()??[];

            if($logEntry->type!="request"){
                $logEntry->load(['parent'=>function($query){
                    return $query->with("tags");
                }]);
                $logEntry->parent->tags=$logEntry->parent->tags?->pluck('tag')->toArray()??[];
            }


            // If the log entry type is "request"
            // if ($logEntry->type == "request") {
            //     // Create an array with the log entry
            //     $logs = [$logEntry];
            //     // Return the view with the logs array
            //     return view('default.admin.log.details')->with('logs', $logs);
            // }
        }

        if(!\Auth::user()->hasRole('developer')){
            if($logEntry->type!="request"){
                // $logEntry->load(['parent'=>function($query){
                //     return $query->with("tags");
                // }]);
                if (!((
                        $logEntry->type==\Laravel\Telescope\EntryType::CLIENT_REQUEST  &&
                        (($logEntry->content->response_status??200)>=500 || ($logEntry->content->response->result_code??'0')!='0'))
                    || $logEntry->type==\Laravel\Telescope\EntryType::EXCEPTION
                    || in_array('error',$logEntry->parent->tags??[]))) {
                    return abort(401);
                }
            }else{
                if (!in_array('error', $logEntry->tags)) {
                    return abort(401);
                }
            }
        }
        $logs = [$logEntry];

        return view('default.admin.log.details')->with('logs', $logs);

        // Return the view with the single log entry
       // return view('default.admin.log.details')->with('log', $logEntry);
    }


    /**
     * Update an entry with the given ID.
     *
     * @param  \Laravel\Telescope\Contracts\EntriesRepository  $storage
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(EntriesRepository $storage, Request $request, $id)
    {
        $entry = $storage->find($id);

        if (is_null($entry->content['resolved_at']??null)) {
            $update = new EntryUpdate($entry->id, $entry->type, [
                'resolved_at' => Carbon::now()->toDateTimeString(),
                'resolved_by' => [
                    'id' => \Auth::id(),
                    'name' => \Auth::user()->name,
                ],
            ]);

            $storage->update(collect([$update]));

            return back()->with('success',__("Operation accomplished successfully"));
        }

        return back()->with('error',__("Operation failed"));
    }
}
