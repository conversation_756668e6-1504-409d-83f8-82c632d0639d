<?php

namespace App\Data\Pass;

use App\Data\AccountIdData;
use App\Data\BaseNonNullableData;
use App\Data\CurrencyAmountData;
use Illuminate\Support\Arr;
use Spatie\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Attributes\MapOutputName;
use Spatie\LaravelData\Attributes\Validation\Nullable;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Normalizers\ObjectNormalizer;
use Illuminate\Support\Collection;

class PassCreateRequestData extends BaseNonNullableData
{
    #[Nullable]
    public ?int $id;

    #[Nullable,MapName('reference_id')]
    public ?string $referenceId;

    #[Nullable,MapName('party_id')]
    public ?string $partyId;

    #[Required]
    public ?CurrencyAmountData $amount;

    #[Required,MapName('receiver_mobile')]
    public ?string $receiverMobile;

    #[Required,MapName('account_id')]
    public ?AccountIdData $accountId;

    #[Required,MapName('sender_mobile')]
    public ?string $senderMobile;
    #[Nullable]
    public ?string $remarks;

    #[Nullable]
    public ?int $status;

    #[Nullable,MapName('external_reference_id')]
    public ?string $externalReferenceId;

    #[Nullable,MapName('row_version')]
    public ?string $rowVersion;
    //#[Nullable,MapOutputName('sender_name')]
    public ?string $senderName;

    //#[Nullable,MapOutputName('sender_id_no')]
    public ?string $senderCardIdNumber;

    //#[Nullable,MapOutputName('sender_id_type')]
    public ?int $senderCardIdType;

    public ?CurrencyAmountData $fee;

    public int $retry=-1;

    #[Nullable,MapName('operation_reference')]
    public ?string $operationReference;
    // public function with():array {
    //     return [
    //         "amount"=> $this->amount->amount,
    //         "currency"=> $this->amount->currency,
    //         'sender_account_no'=>$this->debitAccountId->value
    //     ];
    // }
    // protected function exceptProperties() : array
    // {
    //     return [
    //         // 'amount'=>true,
    //         // 'debitAccountId'=>true,
    //         'id'=>is_null($this->id),
    //         'externalReferenceId'=>is_null($this->externalReferenceId),
    //         'retry'=>true,
    //         'senderName'=>true,
    //         'senderCardIdNumber'=>true,
    //         'senderCardIdType'=>true
    //     ];
    // }

    public static function prepareForPipeline(array $properties) : array
    {
        if(Arr::has($properties,"debitAccountId")){
            $properties['account_id']= AccountIdData::from($properties["debitAccountId"]);
        }
        //$properties['receiverMobile']= $properties["receiverMobile"]??$properties["receiver_mobile"];

        return $properties;
    }
    public function toSendRemoteApi($with=[]):array {
        return $with+[
            "req_ref_id"=> $this->referenceId,
            "amount"=> $this->amount->amount,
            "currency"=> $this->amount->currency,
            'recipient_account_no'=>$this->receiverMobile,
            'sender_account_no'=>$this->senderMobile,
            'sender_name'=>$this->senderName,
            'sender_id_no'=>$this->senderCardIdNumber,
            'sender_id_type'=>$this->senderCardIdType,
        ];
    }
    public function toVerifyRemoteApi($with=[]):array {
        return $with+[
            "req_ref_id"            => $this->referenceId,
            "transaction_reference" => $this->externalReferenceId,
            "operation_reference"   => $this->rowVersion,
        ];
    }
    public function toStatusRemoteApi($with=[]):array {
        return $with+[
            "req_ref_id"    => $this->referenceId,
            "amount"    => $this->amount->amount,
            "currency"  => $this->amount->currency,
            'recipient_account_no'=>$this->receiverMobile,
            'mpt'       =>$this->externalReferenceId
        ];
    }
    public function toReceiveRemoteApi($with=[]):array {
        return $with+[
            "req_ref_id"                => $this->referenceId,
            "transaction_reference" => $this->externalReferenceId,
            "row_version"           => $this->rowVersion,
        ];
    }

    public function toCommisionRemoteApi($with=[]):array {
        return $with+[
            "amount"    => $this->amount->amount,
            "currency"  => $this->amount->currency
        ];
    }

    public function toCancelRemoteApi($with=[]):array {
        return $with+[
            "req_ref_id"                => $this->referenceId,
            "transaction_reference" => $this->externalReferenceId,
            "row_version"           => $this->rowVersion,
        ];
    }
}
