<?php

namespace App\Http\Controllers\Admin;
use App\Data\GeneralItemData;
use App\Http\Controllers\Controller;
use App\Models\AppVersionHistory;
use Illuminate\Http\Request;
use Auth;
use Spatie\LaravelData\PaginatedDataCollection;


class AppVersionHistoryController extends Controller
{
    protected $osTypes=[
        "android",
        "ios"
    ];
    public function __construct(Request $request){$this->middleware('auth');}
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response|\Illuminate\Contracts\View\View
     */
    public function index(Request $request)
    {
        // Check if the authenticated user has the required permissions
        if (!Auth::user()->canAny(['app_version_history.*', 'app_version_history.list'])) {
            return abort(401);
        }

        // Get all request filters
        $filter = $request->all();

        // Initialize the query to get app version history items
        $items = AppVersionHistory::select('id', 'name', 'status')->orderBy('created_at', 'desc');

        // Apply status filter if provided
        if ($request->filled('status')) {
            $items = $items->where('status', $request->status);
        }

        // Apply search name filter if provided
        if ($request->filled('searchname')) {
            $items = $items->where(function ($query) use ($filter) {
                return $query->where('name', 'like', '%' . $filter['searchname'] . '%')
                            ->orWhere('name', 'like', '%' . $filter['searchname'] . '%');
            });
        }

        // Paginate the results
        $items = $items->paginate(15);

        // Define status labels
        $status = [
            "0" => __("Unactive"),
            "1" => __("Active")
        ];

        // Return the view with the items, status, and filter data
        return view('default.admin.app_version_history.index')
            ->with('items', $items)
            ->with('status', $status)
            ->with('filter', $filter);
    }

    /**
     * Display the form for creating a new app version history.
     *
     * This method checks if the authenticated user has the necessary permissions
     * to create an app version history. If the user does not have the required
     * permissions, a 401 Unauthorized response is returned.
     *
     * @return \Illuminate\View\View|\Illuminate\Http\Response
     */
    public function create()
    {
        if (! Auth::user()->canAny(['app_version_history.*','app_version_history.create'])) return abort(401);

        return view('default.admin.app_version_history.view')
        ->with('os_types', $this->osTypes);
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        if (! Auth::user()->canAny(['app_version_history.*','app_version_history.create'])) return abort(401);

        $this->validate($request,[
            'version' => 'required|numeric',
            'name' => 'required|max:500',
            'description.ar' => 'required',
            'description.en' => 'required',
            'os_types' => 'nullable|array',
            'os_types.*' => 'in:android,ios',
            'available' => 'required|in:0,1',
            'status' => 'required|in:0,1',
        ]);
        return $this->save($request);
    }


    /**
     * Display the specified resource.
     *
     * @param  \App\Models\AppVersionHistory$appVersionHistory
     * @return \Illuminate\Contracts\View\View|\Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response
     */
    public function show(AppVersionHistory $appVersionHistory){
        if (! Auth::user()->canAny(['app_version_history.*','app_version_history.view','app_version_history.edit'])) return abort(401);

        return view('default.admin.app_version_history.view')
        ->with('item', $appVersionHistory)
        ->with('os_types', $this->osTypes);
    }


    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\AppVersionHistory  $appVersionHistory
     * @return \Illuminate\Http\Response | \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, AppVersionHistory $appVersionHistory)
    {
        if (! Auth::user()->canAny(['app_version_history.*','app_version_history.edit'])) return abort(401);
        //return response()->json($request->all());
        $this->validate($request,[
            'version' => 'required|numeric',
            'name' => 'required|max:500',
            'description.ar' => 'required',
            'description.en' => 'required',
            'os_types' => 'nullable|array',
            'os_types.*' => 'in:android,ios',
            'available' => 'required|in:0,1',
            'status' => 'required|in:0,1',
        ]);
        return $this->save($request,$appVersionHistory);
    }


    /**
     * Save or update an AppVersionHistory item.
     *
     * This method handles the creation or updating of an AppVersionHistory item.
     * It sets the memory limit to 1024M to accommodate large data processing.
     * If the $item parameter is null, a new AppVersionHistory instance is created.
     * The method then populates the item with data from the request and saves it.
     *
     * @param \Illuminate\Http\Request $request The HTTP request object containing input data.
     * @param \App\Models\AppVersionHistory|null $item The AppVersionHistory item to be updated, or null to create a new one.
     *
     * @return \Illuminate\Http\RedirectResponse A redirect response to the appropriate route with a success message.
     */
    protected function save(Request $request, ?AppVersionHistory $item=null){
        ini_set('memory_limit', '1024M');
        if(is_null($item)){
            $isNew=true;
            $item = new AppVersionHistory;
        }

        $item->version = $request->version;
        $item->name = $request->name;
        $item->description = $request->description;
        $item->os_types = $request->os_types;
        $item->available = $request->available;
        $item->status = $request->status;

        $item->save();


        if(isset($isNew)){
            return redirect("/admin/appVersionHistory/$item->id")
            ->with('success',__("Operation accomplished successfully"));
        }else{
            return back()->with('success',__("Operation accomplished successfully"));
        }
    }
    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id){
        if (! Auth::user()->canAny(['app_version_history.*','app_version_history.delete'])) return abort(401);
        return back()->with('success',__("Operation accomplished successfully"));

    }

}
