<?php

namespace App\Data\Flex;

use App\Data\Classes\BranchData;
use App\Data\Classes\ProductData;
use App\Data\CurrencyAmountData;
use Illuminate\Support\Collection;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Data;

class ChargeFeeData extends Data
{
    #[MapInputName('ACLASS')]
    public ?string $productId;

    #[MapInputName('CCY')]
    public ?string $currencyId;

    #[MapInputName('SLABAMT')]
    public CurrencyAmountData $amount;

    #[MapInputName('CHGAMT')]
    public CurrencyAmountData $fee;

    #[MapInputName('CHGRT')]
    public CurrencyAmountData $precent;
    public static function prepareForPipeline(array $properties) : array
    {
        $properties['SLABAMT']= [
            "amount"=>(float)$properties["SLABAMT"],
            "currency"=>$properties["CCY"],
        ];
        $properties['CHGAMT']= [
            "amount"=>(float)$properties["CHGAMT"],
            "currency"=>""
        ];
        $properties['CHGRT']= [
            "amount"=>(float)$properties["CHGRT"],
            "currency"=>""
        ];
        return $properties;
    }

}
