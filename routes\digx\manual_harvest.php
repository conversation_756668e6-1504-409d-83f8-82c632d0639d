<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Manual Harvest Routes
|--------------------------------------------------------------------------
|
| Routes for manual harvest processing functionality
|
*/

// Manual harvest processing routes for customers
Route::group([
    'prefix' => 'harvest/manual',
    'middleware' => ['digx.obdx']
], function () {
    Route::get('/', 'ManualHarvestController@index');
    Route::post('/', 'ManualHarvestController@store');
    Route::get('/{manualHarvest}', 'ManualHarvestController@show');
});

// Admin routes for manual harvest processing
Route::group([
    'prefix' => 'admin/harvest/manual',
    'middleware' => ['digx.obdx'] // Add admin middleware as needed
], function () {
    Route::get('/', 'ManualHarvestController@adminIndex');
    Route::patch('/{manualHarvest}', 'ManualHarvestController@adminUpdate');
});
