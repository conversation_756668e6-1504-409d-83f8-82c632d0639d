<?php

namespace App\Http\Controllers\Digx;

use App\Data\GeneralResponseData;
use App\Http\Controllers\Controller;
use App\Models\CustomerType;
use App\Models\Party;
use App\Scopes\CustomerScope;
use App\Scopes\UsernameScope;
use Illuminate\Http\Request;
class PartyController extends Controller
{
    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $errorResult=GeneralResponseData::from([
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "STORE-PARTY",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "DIGX_SWITCH_PARTY_100",
                    "type"    => "ERROR"
                ]
            ]
        ]);


        $validator=validator()->make($request->all(),[
            'image_file_image' => 'nullable|image|mimes:jpeg,png|max:2048',
        ]);

        if($validator->fails()){
            $errorResult->status->message->title = join("\n",$validator->errors()->all());
            return response()->json($errorResult,400);
        }
        $this->setImage('image',"profiles");
        // $validator=validator()->make($request->all(),[
        //     "image" => 'required',
        // ]);

        // if($validator->fails()){
        //     return response()->json(GeneralResponseData::from([
        //         'status'=>[
        //             "result"    => "ERROR",
        //             "contextID" => "INDEX-TRANSFER-FUND",
        //             "message"   => [
        //                 "title"   => join("\n",$validator->errors()->all()),
        //                 "detail"  => join("\n",$validator->errors()->all()),
        //                 "code"    => "DIGX_SWITCH_TRANSFER_001",
        //                 "type"    => "ERROR"
        //             ]
        //          ]
        //     ]));
        // }

        $party =Party::withGlobalScope('CustomerScope',new CustomerScope)
        ->withGlobalScope('UsernameScope',new UsernameScope)
        ->first();

        if(is_null($party)){
            $errorResult->status->message->title = __("Faild to update profile image!");
            return response()->json($errorResult,400);
        }
        if(!is_null($request->image)){
            // Get just the file name without extension
            //$filename = pathinfo(parse_url($request->image, PHP_URL_PATH), PATHINFO_FILENAME);
            $filename = basename($request->image);

            //$party->image=route('digx.switch.party.image',$filename);
            //$party->image="https://".request()->header("x-forwarded-host").":4437/digx/v1/switch/party/image/".$filename;
            $party->image=$request->image;
        }else{
            $party->image=null;
        }

        $party->save();

        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => __("Successfully add friend to your friends list"),
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])->additional([
            "image"=> $party->image
        ]));
    }


    /**
     * Activate/Deactivate offline mode.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function offline(Request $request)
    {
        $errorResult=GeneralResponseData::from([
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "STORE-OFFLINE",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "DIGX_SWITCH_OFFLINE_100",
                    "type"    => "ERROR"
                ]
            ]
        ]);

        $validator=validator()->make($request->all(),[
            'offline' => 'required|in:1,0',
        ]);

        if($validator->fails()){
            $errorResult->status->message->title = join("\n",$validator->errors()->all());
            return response()->json($errorResult,400);
        }

        $party =Party::withGlobalScope('CustomerScope',new CustomerScope)
        ->withGlobalScope('UsernameScope',new UsernameScope)
        ->first();

        if(is_null($party)){
            $errorResult->status->message->title = __("Faild to update offline mode!");
            return response()->json($errorResult,400);
        }

        $party->offline=$request->offline;
        $party->save();

        $msg=$party->offline?
        __("Successfully activate offline mode"):
        __("Successfully deactivate offline mode");

        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => $msg,
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ]));
    }
    /**
     * Delete the specified resource in storage.
     *
     * @param  $image
     * @return \Illuminate\Http\JsonResponse
     */
    public function image($image)
    {
        $path = public_path("/storage/profiles/$image");
        if (!file_exists($path)) {
            abort(404);
        }
        return response()->file($path); // auto-sets Content-Type: image/jpeg;
    }

}
