<?php

namespace App\Http\Middleware;

use App\Data\StatusData;
use Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode as Middleware;
use Closure;

class CheckForMaintenanceMode extends Middleware
{
    /**
     * The URIs that should be reachable while maintenance mode is enabled.
     *
     * @var array
     */
    protected $except = [
        //
    ];
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     *
     * @throws \Symfony\Component\HttpKernel\Exception\HttpException
     */
    public function handle($request, Closure $next)
    {
        if ($request->hasHeader("lang")){
            \App::setLocale($request->header("lang"));
        }
        if ($this->app->maintenanceMode()->active()) {
            return response()->json(StatusData::from([
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => __("Sorry, the service is currently unavailable. We are working on new updates to the system, please try again later!"),
                    "detail"  => "",
                    "code"    => "DIGX_SWITCH_MAINTENANCE_MODE",
                    "type"    => "ERROR",
                ]
            ]),\Symfony\Component\HttpFoundation\Response::HTTP_SERVICE_UNAVAILABLE);
        }

        return $next($request);
    }
}
