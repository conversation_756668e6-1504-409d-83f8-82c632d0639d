<?php

namespace App\Http\Controllers\Admin\Setting;
use App\Data\ThirdPartyData;
use App\Data\ThirdPartyServiceNameData;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use \App\Settings\ThirdPartySettings;
use Auth;

class ThirdPartyController extends Controller
{

    public function __construct(Request $request){$this->middleware('auth');}
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if (! Auth::user()->canAny(['setting.third_party.*','setting.third_party.list'])) return abort(401);

        $filter=$request->all();
        $items=app(ThirdPartySettings::class)->toCollection();

        if($request->filled('searchname'))
            $items=$items->filter(function (ThirdPartyData $element,string $key) use($filter){
                    //only chosen account.
                    return $key== str_contains($key,$filter['searchname']);
                });
        return view('default.admin.setting.third-party.index')->with('items', $items)->with('filter', $filter);
    }



    /**
     * Display the specified resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function show(Request $request,$id){
        if (! Auth::user()->canAny(['setting.third_party.*','setting.third_party.view','setting.third_party.edit'])) return abort(401);

        $item=app(ThirdPartySettings::class)->toCollection()->filter(function ($element,string $key) use($id){
            //only chosen account.
            return $key== $id;
        })->first();
        if(is_null( $item)){
            return abort(\Symfony\Component\HttpFoundation\Response::HTTP_NOT_FOUND);
        }

        return view('default.admin.setting.third-party.view')
        ->with('id', $id)
        ->with('item', $item);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $service
     * @return \Illuminate\Http\Response | \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request,string $id)
    {
        if (! Auth::user()->canAny(['setting.third_party.*','setting.third_party.edit'])) return abort(401);

        $flag='';
        if($request->is_test){
            $flag="test_data.";
        }

        $validations=[
            // "{$flag}url" => 'required',
            // "{$flag}client_id" => 'required',
            // "{$flag}client_secret" => 'required'
        ];
        switch($id){
            case 'sms':
            case 'cardless':
            case 'ana':
                $validations+=[
                    "{$flag}url" => 'required',
                    "{$flag}client_id" => 'required',
                    "{$flag}client_secret" => 'required',
                    "{$flag}instid" => 'required'
                ];
                if($id=='cardless'){
                    $validations+=[
                        "{$flag}cardless.url" => 'required',
                        "{$flag}cardless.service_cd" => 'required',
                        "{$flag}cardless.username" => 'required',
                        "{$flag}cardless.password" => 'required',
                        "{$flag}cardless.zone_id" => 'required',
                    ];
                }
                break;
            case 'yeahMoney':
            case 'agent':
                $validations+=[
                    "{$flag}url" => 'required',
                    "{$flag}client_id" => 'required',
                    "{$flag}client_secret" => 'required',
                    "{$flag}agent_info.Agent_Code" => 'required',
                    "{$flag}agent_info.Branch_Code" => 'required',
                    "{$flag}agent_info.User_Info.Username" => 'required',
                    "{$flag}agent_info.User_Info.Password" => 'required',
                    "{$flag}agent_info.User_Info.Agent_User_Name" => 'required',
                ];
                break;
            case 'serviceName':
                $validations+=[
                    "{$flag}gift" => 'required',
                    "{$flag}wasil" => 'required',
                    "{$flag}wasilFee" => 'required',
                    "{$flag}oil" => 'required',
                    "{$flag}oilFee" => 'required',
                    "{$flag}oilExternal" => 'required',
                    "{$flag}wheel" => 'required',
                    "{$flag}split" => 'required'
                ];
                break;
            case 'obdxAdmin':
                $validations+=[
                    "{$flag}url" => 'required',
                    "{$flag}client_id" => 'required',
                    "{$flag}client_secret" => 'required'
                ];
                break;
            case 'obdxBanky':
                $validations+=[
                    "{$flag}url" => 'required',
                    "{$flag}client_id" => 'required',
                    "{$flag}client_secret" => 'required'
                ];
                break;
            case 'loyalty':
                $validations+=[
                    "{$flag}url" => 'required',
                    "{$flag}client_id" => 'required',
                    "{$flag}client_secret" => 'required',
                    "{$flag}instid" => 'required'
                ];
                break;
        }

        $this->validate($request,$validations);
        return $this->save($request,$id);
    }

    public function save(Request $request, string $id){

        $settings=app(ThirdPartySettings::class);
        switch($id){
            case 'serviceName':
                $thirdPartyData=ThirdPartyServiceNameData::from($request->all());
                break;
            default:
                $thirdPartyData=ThirdPartyData::from($request->all());
                $thirdPartyDataTest=$thirdPartyData->test_data;

                $model=$settings->{"$id"};

                if(isset($model->token) && !is_null($model->token)){
                    $thirdPartyData->token=$model->token;
                }

                if(isset($model->test_data->token) && !is_null($model->test_data->token)){
                    $thirdPartyDataTest->token=$model->test_data->token;
                }
                $thirdPartyData->test_data=$thirdPartyDataTest;

                break;
        }

        $settings->{"$id"}=$thirdPartyData;
        $settings->save();


        // $test_data=$model->test_data;

        // $model->url=$request->url;
        // $test_data->url=$request->input('test_data.url');


        // $test_data->instid="BANKY";
        // $model->test_data=$test_data;
        // $settings->{"$id"}=$model;
        // $settings->save();
        // switch($id){
        //     case 'cardless':
        //     case 'ana':
        //         $validations+=[
        //             "{$flag}instid" => 'required'
        //         ];
        //         if($id=='cardless'){
        //             $validations+=[
        //                 "{$flag}cardless.url" => 'required',
        //                 "{$flag}cardless.service_cd" => 'required',
        //                 "{$flag}cardless.username" => 'required',
        //                 "{$flag}cardless.password" => 'required',
        //                 "{$flag}cardless.zone_id" => 'required',
        //             ];
        //         }
        //         break;
        //     case 'yeahMoney':
        //     case 'agent':
        //         $validations+=[
        //             "{$flag}agent_info.Agent_Code" => 'required',
        //             "{$flag}agent_info.Branch_Code" => 'required',
        //             "{$flag}agent_info.User_Info.Username" => 'required',
        //             "{$flag}agent_info.User_Info.Password" => 'required',
        //             "{$flag}agent_info.User_Info.Agent_User_Name" => 'required',
        //         ];
        //         break;
        // }

        return back()->with('success',__("Operation accomplished successfully"));

    }
    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id){
        if (! Auth::user()->canAny(['setting.third_party.*','setting.third_party.delete'])) return abort(401);

    }

}
