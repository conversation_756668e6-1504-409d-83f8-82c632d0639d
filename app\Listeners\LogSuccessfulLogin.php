<?php

namespace App\Listeners;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Auth\Events\Login;
use Auth;

class LogSuccessfulLogin
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  object  $event
     * @return void
     */
    public function handle(Login $event)
    {
        if (Auth::guard($event->guard)->viaRemember()) {
            if (count(auth()->user()->roles->pluck('id')->toArray())==0) {
                //auth()->user()->setRememberToken(null);
                auth()->logout();
                return redirect('/');
            }
            # is user disabled? API call to another service.
            # yes, back to login and challenge for credentials.
        }
    }
}
