<?php

use <PERSON><PERSON>\LaravelSettings\Migrations\SettingsMigration;

class AddLoyaltyThirdPartySettings extends SettingsMigration
{
    public function up(): void
    {
        $this->migrator->addEncrypted('thirdParty.loyalty',array (
            'url' => '',
            'client_id' => '',
            'client_secret' => '',
            'instid' => '',
            'token' => array (
                'access_token' => '',
                'token_type' => '',
                'expires_in' => ''
            ),
            'is_test' => '1',
            'test_data' => array (
                'url' => 'http://localhost:3325/api/en-us/v1/partner',
                'client_id' => '<EMAIL>',
                'client_secret' => '123456',
                'instid' => '237374434811904',
                'token' => array (
                    'access_token' => '',
                    'token_type' => '',
                    'expires_in' => ''
                ),
            )
        ));
    }
    public function down()
    {
        $this->migrator->delete('thirdParty.loyalty');
    }
}
