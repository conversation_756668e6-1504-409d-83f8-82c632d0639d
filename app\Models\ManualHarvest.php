<?php

namespace App\Models;

use App\Scopes\CustomerScope;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ManualHarvest extends Model
{
    use HasFactory;

    /**
     * The list of hidden request headers.
     *
     * @var array
     */
    public static $hiddenParameters = [
        'admin_notes'
    ];

    protected $dates = ['created_at', 'updated_at', 'processed_at'];

    protected $casts = [
        'target_account_id' => 'object',
        'remittance_currency' => 'object',
        'receiver_info' => 'object',
        'sender_info' => 'object',
        'remittance_details' => 'object',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
        'processed_at' => 'datetime:Y-m-d H:i:s',
    ];

    protected $hidden = [
        'admin_notes'
    ];

    protected $fillable = [
        'party_id',
        'tracking_code',
        'network_type',
        'target_account_id',
        'remittance_currency',
        'customer_notes',
        'receiver_info',
        'sender_info',
        'remittance_details',
        'admin_notes',
        'processed_by',
        'external_reference_id',
        'status',
        'processed_at'
    ];

    protected static function boot()
    {
        parent::boot();
        static::addGlobalScope(new CustomerScope);
        
        // Auto-sets values on creation
        static::creating(function ($query) {
            $query->party_id = auth()->user()->id;
        });
    }

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->timezone('Asia/Aden')->format('Y-m-d H:i:s');
    }

    /**
     * Get the user that owns the manual harvest.
     */
    public function user()
    {
        return $this->belongsTo('App\Models\User', 'party_id');
    }

    /**
     * Get the admin user who processed this request.
     */
    public function processor()
    {
        return $this->belongsTo('App\Models\User', 'processed_by');
    }

    /**
     * Get the logs for this manual harvest.
     */
    public function logs()
    {
        $_name = static::class;
        return $this->hasMany('App\Models\LogEntry', "model_id")
            ->where('model', $_name)
            ->where('type', 'request')
            ->with('relatedEntries');
    }

    /**
     * Status constants
     */
    const STATUS_PENDING = 0;
    const STATUS_PROCESSING = 1;
    const STATUS_COMPLETED = 2;
    const STATUS_FAILED = -1;
    const STATUS_CANCELLED = -2;

    /**
     * Get status text
     */
    public function getStatusTextAttribute()
    {
        return match($this->status) {
            self::STATUS_PENDING => 'Pending',
            self::STATUS_PROCESSING => 'Processing',
            self::STATUS_COMPLETED => 'Completed',
            self::STATUS_FAILED => 'Failed',
            self::STATUS_CANCELLED => 'Cancelled',
            default => 'Unknown'
        };
    }

    /**
     * Determine network type from tracking code
     */
    public static function determineNetworkType($trackingCode)
    {
        // Common patterns for Yemen remittance networks
        $patterns = [
            'YM' => 'Yeah Money',
            'RIA' => 'RIA Money Transfer',
            'WU' => 'Western Union',
            'MG' => 'MoneyGram',
            'SW' => 'Small World',
            'AL' => 'Al Ansari Exchange',
            'UAE' => 'UAE Exchange',
            'SHIFT' => 'SHIFT Financial',
            'UREMIT' => 'U Remit',
            'FAWRI' => 'Fawri Services',
        ];

        $trackingCode = strtoupper($trackingCode);
        
        foreach ($patterns as $prefix => $network) {
            if (str_starts_with($trackingCode, $prefix)) {
                return $network;
            }
        }

        // Check for numeric patterns
        if (preg_match('/^\d{8,12}$/', $trackingCode)) {
            return 'Unknown Network (Numeric)';
        }

        return 'Unknown Network';
    }

    /**
     * Scope for pending requests
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Scope for processing requests
     */
    public function scopeProcessing($query)
    {
        return $query->where('status', self::STATUS_PROCESSING);
    }

    /**
     * Scope for completed requests
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }

    /**
     * Scope for admin dashboard (exclude customer scope)
     */
    public function scopeForAdmin($query)
    {
        return $query->withoutGlobalScope(CustomerScope::class);
    }

    /**
     * Mark as processing
     */
    public function markAsProcessing($adminUserId)
    {
        $this->update([
            'status' => self::STATUS_PROCESSING,
            'processed_by' => $adminUserId,
            'processed_at' => now()
        ]);
    }

    /**
     * Mark as completed
     */
    public function markAsCompleted($externalReferenceId = null)
    {
        $this->update([
            'status' => self::STATUS_COMPLETED,
            'external_reference_id' => $externalReferenceId,
            'processed_at' => now()
        ]);
    }

    /**
     * Mark as failed
     */
    public function markAsFailed($reason = null)
    {
        $this->update([
            'status' => self::STATUS_FAILED,
            'admin_notes' => $reason,
            'processed_at' => now()
        ]);
    }
}
