<?php

namespace App\Enums;

enum OilTypeEnum:string {
    case Petrol = "petrol";
    case Diesel = "diesel";
    case Gas = "gas";

    public static function values():array {
        return collect(static::cases())->pluck("value")->toArray();
    }

    public static function maps():array {
        return collect(static::cases())->map(function($item) {
            return [
                "id" => $item->value,
                "name" => __("$item->name"),
            ];
        })->toArray();
    }

}

