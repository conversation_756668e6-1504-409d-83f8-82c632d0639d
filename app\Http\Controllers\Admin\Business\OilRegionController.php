<?php

namespace App\Http\Controllers\Admin\Business;
use App\Data\GeneralItemData;
use App\Enums\OilTypeEnum;
use App\Http\Controllers\Controller;
use App\Models\OilRegion;
use Illuminate\Http\Request;
use Auth;
use Spatie\LaravelData\PaginatedDataCollection;

class OilRegionController extends Controller
{


    public function __construct(Request $request){$this->middleware('auth');}
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response|\Illuminate\Contracts\View\View
     */
    public function index(Request $request)
    {
        if (! Auth::user()->canAny(['business.oil_region.*','business.oil_region.list'])) return abort(401);

        $filter=$request->all();

        $items=OilRegion::select('id','name','account_id','status')->orderBy('id','desc');

        if($request->filled('searchname'))
            $items=$items->where(function($query) use($filter){
                return $query->where('name->ar','like','%'.$filter['searchname'].'%')
                ->orWhere('name->en','like','%'.$filter['searchname'].'%');
            });

        $items=$items->paginate(15);

      //  GeneralItemData::collect($items->paginate(15));//->wrap('paginated_data');


        return view('default.admin.business.oil_region.index')
        ->with('items', $items)
        ->with('filter', $filter);
    }


    /**
     * Show the form for creating a resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function create()
    {
        if (! Auth::user()->canAny(['business.oil_region.*','business.oil_region.create'])) return abort(401);

        return view('default.admin.business.oil_region.view');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        if (! Auth::user()->canAny(['business.oil_region.*','business.oil_region.create'])) return abort(401);

        $this->validate($request,[
            'name.en' => 'required|max:500',
            'name.ar' => 'required|max:500',
            'account_id' => 'required|min:19|max:19',
            'types' => 'required|array',
            'types.*' => 'in:'.join(",",OilTypeEnum::values()),// Ensures each value in the array is one of these
            'branches' => 'required|array',
            'branches.*' => 'in:'.join(',',\App\Data\Classes\BranchData::values()),// Ensures each value in the array is one of these
            'status' => 'required|in:0,1',

        ]);
        return $this->save($request);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\OilRegion $oilRegion
     * @return \Illuminate\Contracts\View\View
     */
    public function show(OilRegion $oilRegion){
        if (! Auth::user()->canAny(['business.oil_region.*','business.oil_region.view','business.oil_region.edit'])) return abort(401);

        return view('default.admin.business.oil_region.view')
        ->with('item', $oilRegion);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \App\Models\OilRegion $oilRegion
     * @return \Illuminate\Http\Response | \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, OilRegion $oilRegion)
    {
        if (! Auth::user()->canAny(['business.oil_region.*','business.oil_region.edit'])) return abort(401);
        //return response()->json($request->all());
        $this->validate($request,[
           'name.en' => 'required|max:500',
            'name.ar' => 'required|max:500',
            'account_id' => 'required|min:19|max:19',
            'types' => 'required|array',
            'types.*' => 'in:'.join(",",OilTypeEnum::values()),// Ensures each value in the array is one of these
            'branches' => 'required|array',
            'branches.*' => 'in:'.join(',',\App\Data\Classes\BranchData::values()),// Ensures each value in the array is one of these
            'status' => 'required|in:0,1',

        ]);
        return $this->save($request,$oilRegion);
    }

    public function save(Request $request, ?OilRegion $item=null){
        ini_set('memory_limit', '1024M');
        if(is_null($item)){
            $isNew=true;
            $item = new OilRegion;
        }

        $item->name = $request->name;
        $item->account_id = $request->account_id;
        $item->types = $request->types;
        $item->branches = $request->branches;
        $item->status = $request->status;

        $item->save();


        if(isset($isNew)){
            return redirect(route('admin.business.oil_region.show',$item->id))
            ->with('success',__("Operation accomplished successfully"));
        }else{
            return back()->with('success',__("Operation accomplished successfully"));
        }
    }
    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id){
        if (! Auth::user()->canAny(['business.oil_region.*','business.oil_region.delete'])) return abort(401);

    }

}
