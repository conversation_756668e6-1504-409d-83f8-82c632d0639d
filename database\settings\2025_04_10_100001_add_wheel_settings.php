<?php

use <PERSON><PERSON>\LaravelSettings\Migrations\SettingsMigration;

class AddWheelSettings extends SettingsMigration
{
    public function up(): void
    {
        $this->migrator->add('wheel.wheelConfig',array (
            'allowSpinningWheel' => 0,
            'dailyMaxAmount' => array (
                'amount' => 10000,
                'currency' => 'YER'
            ),
            'allowAccumulativeAmountThroughMonth' => 0,
            'autoChangeProbability' => 0,

        ));
        $this->migrator->add('wheel.wheelItems',array (
            'items' => [
                ["id" => 0, "type" => 1, "name" => "100","value" => 100, "image" => "packages/banky/assets/images/wheel/b1.png", "color" => "#FAFAE0", "weight" => 25.0,"status" => 1],
                ["id" => 1, "type" => 0, "name" => "Bomb","value" => 0, "image" => "packages/banky/assets/images/wheel/bomb1.png", "color" => "#FFBED8", "weight" => 8.333,"status" => 1],
                ["id" => 2, "type" => 1, "name" => "500","value" => 500, "image" => "packages/banky/assets/images/wheel/b1.png", "color" => "#FAFAE0", "weight" => 12.5,"status" => 1],
                ["id" => 3, "type" => 0, "name" => "Bomb","value" => 0, "image" => "packages/banky/assets/images/wheel/bomb2.png", "color" => "#FD93BD", "weight" => 8.333,"status" => 1],
                ["id" => 4, "type" => 1, "name" => "1000","value" => 1000, "image" => "packages/banky/assets/images/wheel/b1.png", "color" => "#FAFAE0", "weight" => 6.25,"status" => 1],
                ["id" => 5, "type" => 0, "name" => "Bomb","value" => 0, "image" => "packages/banky/assets/images/wheel/bomb3.png", "color" => "#FFBED8", "weight" => 8.333,"status" => 1],
                ["id" => 6, "type" => 1, "name" => "5000","value" => 5000, "image" => "packages/banky/assets/images/wheel/b1.png", "color" => "#FAFAE0", "weight" => 3.125,"status" => 1],
                ["id" => 7, "type" => 0, "name" => "Bomb","value" => 0, "image" => "packages/banky/assets/images/wheel/bomb2.png", "color" => "#FD93BD", "weight" => 8.333,"status" => 1],
                ["id" => 8, "type" => 1, "name" => "10000","value" => 10000, "image" => "packages/banky/assets/images/wheel/b1.png", "color" => "#FAFAE0", "weight" => 1.5625,"status" => 1],
                ["id" => 9, "type" => 0, "name" => "Bomb","value" => 0, "image" => "packages/banky/assets/images/wheel/bomb1.png", "color" => "#FFBED8", "weight" => 8.333,"status" => 1],
                ["id" => 10, "type" => 1, "name" => "50000","value" => 50000, "image" => "packages/banky/assets/images/wheel/b1.png", "color" =>"#FAFAE0", "weight" => 0.78125,"status" => 1],
                ["id" => 11, "type" => 0, "name" => "Bomb","value" => 0, "image" => "packages/banky/assets/images/wheel/bomb3.png", "color" => "#FD93BD", "weight" => 8.333,"status" => 1],
            ]
        ));
    }

    public function down()
    {
        $this->migrator->delete('wheel.wheelConfig');
        $this->migrator->delete('wheel.wheelItems');

    }
}
