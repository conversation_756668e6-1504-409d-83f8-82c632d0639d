<?php

namespace App\Data;

class ReceiptData extends BaseNonNullableData
{
    public function __construct(
        public string $id,
        public string $date,
        public string $title,
        public ?string $header,
        public ?string $footer,
        public ?string $beneficiary,
        public ?string $sender,
        public string $statement,
        public ReceiptDetailsData $details,
    ) {
        $locale=app()->getLocale();
        $this->header = $this->header ?? storage_path("app/public/header-$locale.jpg");
        $this->footer = $this->footer ?? storage_path("app/public/footer-$locale.jpg");
    }


}
