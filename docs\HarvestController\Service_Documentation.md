# HarvestController Service Documentation

## Overview
The HarvestController is a Laravel controller that manages money remittance claiming operations through the Yeah Money third-party service. It enables users to claim money transfers sent through various remittance services by providing tracking codes and confirming transactions to their bank accounts.

## Class Information
- **Namespace**: `App\Http\Controllers\Digx\HarvestController`
- **Extends**: `Controller`
- **Uses Traits**: `AuthorizesServices`
- **Middleware**: `digx.obdx` (applied via routes)

## Dependencies

### Models
- `App\Models\Harvest` - Main harvest transaction model
- `App\Models\HarvestServiceCode` - Service provider codes

### Data Classes
- `App\Data\HarvestRequestData` - Request data structure
- `App\Data\AccountIdData` - Account identifier data
- `App\Data\CurrencyAmountData` - Currency and amount data
- `App\Data\GeneralResponseData` - Standardized response format
- `App\Data\ReceiptData` - Receipt generation data
- `App\Data\TokenData` - OAuth token data
- `App\Data\AccountData` - Account information data
- `App\Data\Classes\BranchData` - Branch configuration data

### Services
- `App\Services\OBDX\CustomerService` - Banking operations service
- `App\Services\NotificationService` - User notification service
- `App\Settings\ThirdPartySettings` - Configuration settings

### Other Dependencies
- `App\LogItem` - Logging functionality
- `App\Traits\AuthorizesServices` - Service authorization
- `App\Enums\ServiceTagEnum` - Service type enumeration

## Constructor

### `__construct(Request $request)`
Initializes the controller with Yeah Money service settings.

**Parameters:**
- `$request` (Request): HTTP request instance

**Functionality:**
- Loads Yeah Money configuration from ThirdPartySettings
- Switches to test configuration if `is_test` flag is enabled
- Calls parent constructor

## Public Methods

### `getAccessToken($forceUpdate = false)`
Manages OAuth access token for Yeah Money API.

**Parameters:**
- `$forceUpdate` (bool): Force token refresh if true

**Returns:**
- `int`: HTTP status code (200 for success)

**Functionality:**
- Checks if current token is valid
- Requests new token if needed using client credentials
- Updates settings with new token
- Persists token to database

### `index(Request $request)`
Lists completed harvest transactions for the authenticated user.

**Parameters:**
- `$request` (Request): HTTP request with pagination parameters

**Returns:**
- `JsonResponse`: Array of harvest transactions

**Query Parameters:**
- `from`: Pagination offset
- `limit`: Number of records to return

**Database Query:**
- Selects from `harvests` table with left join to `harvest_service_codes`
- Filters by status = 1 (completed)
- Orders by ID descending
- Applies pagination

### `create()`
Returns available service codes for creating harvest transactions.

**Returns:**
- `JsonResponse`: Array of active service codes

**Functionality:**
- Queries `HarvestServiceCode` model for active services (status = 1)
- Returns ID and name fields only

### `store(Request $request)`
Creates a new harvest transaction by validating tracking code.

**Parameters:**
- `$request` (Request): HTTP request with transaction data

**Returns:**
- `JsonResponse`: Success response with harvest details or error

**Validation Rules:**
- `trackingCode`: required
- `amount.amount`: nullable, numeric
- `amount.currency`: nullable, 3 characters
- `senderPhone`: nullable, 9 characters
- `branchId`: required, 3 characters
- `serviceCode`: required, numeric

**Process Flow:**
1. Validate request data
2. Verify service code exists and is active
3. Get access token for Yeah Money API
4. Login to Yeah Money service
5. Query remittance information
6. Validate remittance availability
7. Create harvest record with status 0 (pending)
8. Return transaction details

### `update(Request $request, Harvest $harvest)`
Confirms and processes a harvest transaction.

**Parameters:**
- `$request` (Request): HTTP request with account information
- `$harvest` (Harvest): Harvest model instance

**Returns:**
- `JsonResponse`: Success response with receipt or error

**Validation Rules:**
- `creditAccountId.value`: required, exactly 20 characters

**Process Flow:**
1. Validate account ID format
2. Check transaction is pending (status = 0)
3. Validate account ownership and criteria
4. Get user card information
5. Submit payout request to Yeah Money
6. Update transaction status
7. Send notification to user
8. Return confirmation with receipt

### `receipt(Request $request, Harvest $harvest)`
Generates PDF receipt for completed transaction.

**Parameters:**
- `$request` (Request): HTTP request
- `$harvest` (Harvest): Harvest model instance

**Returns:**
- PDF file download

**Functionality:**
- Calls `generateReceipt()` with receipt data
- Uses `getReceiptData()` to format receipt information

## Protected Methods

### `getServiceTags(): array`
Returns service tags for authorization.

**Returns:**
- `array`: Array containing `ServiceTagEnum::CLAIM_MONEY`

### `getReceiptData(Harvest $harvest): ReceiptData`
Formats harvest data for receipt generation.

**Parameters:**
- `$harvest` (Harvest): Harvest model instance

**Returns:**
- `ReceiptData`: Formatted receipt data

**Functionality:**
- Formats title with service name
- Includes sender information with phone
- Creates receipt details with account and amount information

## Configuration

### Settings Structure
The controller uses `ThirdPartySettings::yeahMoney` configuration:

```php
[
    'url' => 'API base URL',
    'client_id' => 'OAuth client ID',
    'client_secret' => 'OAuth client secret',
    'token' => [
        'access_token' => 'Current access token',
        'token_type' => 'Bearer',
        'expires_in' => 'Token expiration'
    ],
    'agent_info' => [
        'Agent_Code' => 'Agent identifier',
        'Branch_Code' => 'Default branch code',
        'User_Info' => [
            'Username' => 'API username',
            'Password' => 'API password',
            'Language' => 'Interface language',
            'Agent_User_Name' => 'Agent display name'
        ]
    ],
    'is_test' => true/false,
    'test_data' => [/* Test environment settings */]
]
```

## Error Handling

### Error Codes
- `DIGX_SWITCH_HARVEST_100`: Validation errors
- `DIGX_SWITCH_HARVEST_004`: Service code or remittance issues
- `DIGX_SWITCH_HARVEST_005`: Remittance already paid
- `DIGX_SWITCH_HARVEST_101`: Transaction expired
- `DIGX_SWITCH_HARVEST_102`: Invalid account
- `DIGX_SWITCH_HARVEST_006`: Payout processing failed
- `DIGX_SWITCH_001`: External service connection error

### Response Format
All responses use `GeneralResponseData` structure:
```php
[
    'status' => [
        'result' => 'SUCCESSFUL|ERROR',
        'contextID' => 'Context identifier',
        'message' => [
            'title' => 'User message',
            'detail' => 'Detailed information',
            'code' => 'Error code',
            'type' => 'INFO|ERROR'
        ]
    ]
]
```

## Security Features

### Authorization
- Uses `AuthorizesServices` trait for service-level authorization
- Requires `CLAIM_MONEY` service tag
- Validates user permissions through middleware

### Account Validation
- Verifies account ownership through party ID matching
- Validates account status (must be ACTIVE)
- Ensures account type is cash account
- Checks currency compatibility
- Validates regional restrictions for YER currency

### Data Protection
- Hides sensitive fields (token) in model serialization
- Uses encrypted settings storage
- Logs all operations for audit trail

## Integration Points

### Yeah Money API
- OAuth 2.0 authentication
- RESTful API endpoints:
  - `/oauth/token` - Token generation
  - `/mt/v1/login` - Agent login
  - `/mt/v1/payoutInfo` - Remittance inquiry
  - `/mt/v1/payout` - Transaction processing

### OBDX Banking System
- Account validation
- Customer information retrieval
- Card information access
- Transaction processing

### Notification System
- Success/failure notifications
- Multi-language support
- Push notification delivery

## Performance Considerations

### Database Optimization
- Indexed fields: party_id, service_code_id, request_id, status
- JSON field queries optimized for Oracle and MySQL
- Pagination support for large datasets

### Caching
- Token caching to reduce API calls
- Settings caching for configuration data

### Error Recovery
- Automatic token refresh on 401 errors
- Retry mechanism for failed requests
- Graceful degradation for service unavailability
