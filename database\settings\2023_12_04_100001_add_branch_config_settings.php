<?php

use App\Models\CustomerType;
use Spa<PERSON>\LaravelSettings\Migrations\SettingsMigration;

class AddBranchConfigSettings extends SettingsMigration
{
    public function up(): void
    {
        $this->migrator->add('config.branchConfig',array (
            'values' => [
                "402",//sanaa
                "403",//aden
                "404",//hodeidah
                "405",//taiz
                "406",//mukalla
                "407",//taizStreet
                "408",//ibb
                "409",//rabatStreet
                "410",//rawda
                "411",//shoob
                "412",//alHuban
                "413",//fiftyStreet
                "414",//sheikh<PERSON><PERSON>man
                "415",//hada
                "416",//libyan
                "417",//sixtyStreet
                "418",//dhamar
                "419",//al<PERSON>bahi
                "420",//omran
                "421",//hajjah
                "422",//bayt<PERSON><PERSON><PERSON><PERSON>
                "423",//almuhafazaOffice
                "424",//sawalOffice
                "502",//sanaaIslamic
                "504",//taizStreetIslamic
                "505",//sadaIslamic
                "506"//marebIslamic
            ],
            'north' => [
                "402",//sanaa
                "404",//hodeidah
                "407",//taizStreet
                "408",//ibb
                "409",//rabatStreet
                "410",//rawda
                "411",//shoob
                "412",//alHuban
                "413",//fiftyStreet
                "415",//hada
                "416",//libyan
                "417",//sixtyStreet
                "418",//dhamar
                "419",//alAsbahi
                "420",//omran
                "421",//hajjah
                "422",//baytAlfaqih
                "423",//almuhafazaOffice
                "424",//sawalOffice
                "502",//sanaaIslamic
                "504",//taizStreetIslamic
                "505"//sadaIslamic
            ],
            'south' => [
                "403",//aden
                "405",//taiz
                "406",//mukalla
                "414",//sheikhOthman
                "506"//marebIslamic
            ],
            'sanaaBranches' => [
                "402",//sanaa
                "407",//taizStreet
                "409",//rabatStreet
                "410",//rawda
                "411",//shoob
                "413",//fiftyStreet
                "415",//hada
                "416",//libyan
                "417",//sixtyStreet
                "419",//alAsbahi
                "502",//sanaaIslamic
                "504"//taizStreetIslamic
            ],

        ));

    }

    public function down()
    {
        $this->migrator->delete('config.branchConfig');
    }
}
