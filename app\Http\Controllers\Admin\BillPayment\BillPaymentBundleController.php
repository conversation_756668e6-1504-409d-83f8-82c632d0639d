<?php

namespace App\Http\Controllers\Admin\BillPayment;
use App\Data\GeneralItemData;
use App\Http\Controllers\Controller;
use App\Models\BillPaymentFilter;
use App\Models\BillPaymentItem;
use App\Models\BillPaymentModelFilter;
use App\Models\BillPaymentBundle;
use App\Models\BillPaymentModelFilterOption;
use App\Models\BillPaymentService;
use Illuminate\Http\Request;
use Auth;

class BillPaymentBundleController extends Controller
{

    protected $status=[
        "0"=>"Unactive",
        "1"=>"Active",
    ];
    public function __construct(Request $request){$this->middleware('auth');}
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response|\Illuminate\Contracts\View\View
     */
    public function index(Request $request)
    {
        if (! Auth::user()->canAny(['bill_payment.bundle.*','bill_payment.bundle.list'])) abort(401);

        $filter=$request->all();

        $items=BillPaymentBundle::select('id','title','status','created_at')->orderBy('id','desc');


        if($request->filled('filter') ){
            $items=$items->whereHas('filters',function($query) use($request){
                $query->where('id',$request->filter);
            });
        }

        if($request->filled('service') ){
            $items=$items->whereHas('item',function($query) use($filter){
                $query->where('bill_payment_service_id',$filter['service']);
            });
        }
        if($request->filled('item') ){
            $items=$items->where('bill_payment_item_id',$filter['item']);
        }
        if($request->filled('status') ){
            $items=$items->where('status',$filter['status']);
        }
        if($request->filled('searchname'))
            $items=$items->where(function($query) use($filter){
                return $query->where('title->ar','like','%'.$filter['searchname'].'%')
                ->orWhere('title->en','like','%'.$filter['searchname'].'%');
            });

        $items=$items->paginate(15);

      //  GeneralItemData::collect($items->paginate(15));//->wrap('paginated_data');

      $filters=BillPaymentFilter::select('id','title')->where('status',1)->get();
      $services=BillPaymentService::select('id','title')->get();
      $sitems=BillPaymentItem::select('id','title')->get();

        return view('default.admin.bill_payment.bundle.index')
        ->with('items', $items)
        ->with('filters', $filters)
        ->with('services', $services)
        ->with('sitems', $sitems)
        ->with('status', $this->status)
        ->with('filter', $filter);
    }


    /**
     * Show the form for creating a resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function create()
    {
        if (! Auth::user()->canAny(['bill_payment.bundle.*','bill_payment.bundle.create'])) abort(401);

        return view('default.admin.bill_payment.bundle.view')
        ->with('status', $this->status);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\BillPaymentBundle  $bundle
     * @return \Illuminate\Contracts\View\View
     */
    public function show(BillPaymentBundle $bundle){
        if (! Auth::user()->canAny(['bill_payment.bundle.*','bill_payment.bundle.view','bill_payment.bundle.edit'])) return abort(401);


        return view('default.admin.bill_payment.bundle.view')
        ->with('item', $bundle);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\BillPaymentBundle  $bundle
     * @return \Illuminate\Http\Response | \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, BillPaymentBundle $bundle)
    {
        if (! Auth::user()->canAny(['bill_payment.bundle.*','bill_payment.bundle.edit'])) abort(401);
        //return response()->json($request->all());
        $this->validate($request,[
            'title.en' => 'required|max:500',
            'title.ar' => 'required|max:500',
            'status' => 'required|in:0,1',
        ]);
        return $this->save($request,$bundle);
    }

    public function save(Request $request, ?BillPaymentBundle $item=null){
        ini_set('memory_limit', '1024M');
        if(is_null($item)){
            $isNew=true;
            $item = new BillPaymentBundle;
        }

        $item->title = $request->title;
        $item->status = $request->status;
        $item->save();


        BillPaymentModelFilterOption::where('model_id', $item->id)
        ->where('model_type', BillPaymentBundle::class)
        ->delete();
        if($request->filled('filter') && count($request->filter)){
            $filterItems=[];
            foreach($request->filter as $key=>$values){
                foreach($values as $value){
                    $filterItems[]=[
                        "model_type"=>BillPaymentBundle::class,
                        "model_id"  =>$item->id,
                        "bill_payment_filter_option_id"=>$value,
                        "created_at"=>\Carbon\Carbon::now()->toDateTimeString()
                    ];
                }

            }
            if(!empty($filterItems)){
                collect($filterItems)->chunk(100)
                ->each(function ($chunked) {
                    BillPaymentModelFilterOption::insert($chunked->values()->toArray());
                });
            }
        }

        $item->load("item");
        BillPaymentService::cacheUpdate(["catalog:{$item->item->bill_payment_service_id}"]);

        if(isset($isNew)){
            return redirect(route("admin.billPayment.bundle.show",$item->id))
            ->with('success',__("Operation accomplished successfully"));
        }else{
            return back()->with('success',__("Operation accomplished successfully"));
        }
    }
    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id){
        if (! Auth::user()->canAny(['bill_payment.bundle.*','bill_payment.bundle.delete'])) return abort(401);

    }

}
