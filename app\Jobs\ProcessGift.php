<?php

namespace App\Jobs;

use App\Enums\TransactionStatusEnum;
use App\Models\Gift;
use App\Scopes\CustomerScope;
use App\Services\NotificationService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\Attributes\WithoutRelations;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Laravel\Horizon\Contracts\Silenced;
class ProcessGift implements ShouldQueue, Silenced
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $gifts=Gift::withoutGlobalScope(CustomerScope::class)->with(['transactions'=>function($query){
            return $query->with('sharing')
            ->where('status',1);
        }])
        ->with(['request'=>function($query){
            return $query->withoutGlobalScope(CustomerScope::class);
        }])
        ->whereHas('transactions',function($query){
            return $query->where('status', 1);
        })
        ->where('status',5)
        ->orderBy("id","ASC");


        if(env('DB_CONNECTION')=='oracle'){
            $gifts=$gifts->whereRaw("at_date<=TO_DATE(?,'YYYY-MM-DD HH24:MI')",[Carbon::now()->format("Y-m-d H:i")]);
        }else{
            $gifts=$gifts->where("at_date",Carbon::now()->format("Y-m-d H:i"));
        }
        $gifts=$gifts->get();

        foreach ($gifts as $gift) {
            foreach ($gift->transactions as $transaction) {
                $is_eid=Carbon::parse($gift->at_date)->betweenIncluded('2024-04-10', '2024-04-17');
                $smsMessage = sprintf(trans($is_eid?"gift_sms_message_new_eid":"gift_sms_message_new"),
                    html_entity_decode($transaction->payee->nickName, ENT_QUOTES, "UTF-8"),
                    html_entity_decode($gift->party_name, ENT_QUOTES, "UTF-8"),
                    $gift->type=='other'?$gift->remarks:trans("$gift->type"),
                    $transaction->amount->amount . " " . $transaction->amount->currency,
                    env('APP_URL').route('sharing',[$transaction->sharing->txn_token],false)
                );

                // $smsMessage = sprintf(trans("gift_sms_message"),
                //     html_entity_decode($gift->party_name, ENT_QUOTES, "UTF-8"),
                //     $gift->type=='other'?$gift->remarks:trans("$gift->type"),
                //     $transaction->amount->amount . " " . $transaction->amount->currency,
                //    env('APP_URL').route('sharing',[$transaction->sharing->txn_token],false)
                // );
                if(env('APP_ENV', 'production')!='local' || in_array($transaction->payee->transferValue,['771555999','779495900'])){
                    NotificationService::sendSMS([
                        "mobile" => $transaction->payee->transferValue,
                        "message" => $smsMessage
                    ]);
                }
            }
            $gift->status = 1;
            $gift->save();

            $giftRequest=$gift->request;
            if(!is_null($giftRequest)){
                $giftRequest->status=TransactionStatusEnum::COMPLETED->value;
                $giftRequest->save();
            }
        }
    }
}
