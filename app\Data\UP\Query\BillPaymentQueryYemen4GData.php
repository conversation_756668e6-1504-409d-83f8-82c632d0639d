<?php

namespace App\Data\UP\Query;

use App\Data\AccountIdData;
use App\Data\BaseNonNullableData;
use App\Data\BaseNonNullableDataCollection;
use App\Data\CurrencyAmountData;
use App\Data\NameData;
use App\Data\StatusData;
use App\Models\BillPaymentItem;
use Carbon\Carbon;
use Lang;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\DataCollection;


class BillPaymentQueryYemen4GData extends BillPaymentQueryBaseData
{
    const dataToShow=[
        "packageBalance",
        "billingNo",
        "issueDate",
        "expiryDate",
        "packageId",
        "message",
        "billNo",
        "dataBalance",
        "dueAmount",
        "lowestToPay"
    ];
    public function __construct(
        public ?StatusData $status=null,
        #[MapInputName('ExtData'),DataCollectionOf(BillPaymentQueryExtData::class)]
        public ?DataCollection $extData=null,
    ) {
    }

    public static function prepareForPipeline(array $properties) : array
    {
        $properties=parent::prepareForPipeline($properties);
        return $properties;
    }

    public function toAppResponse(): BillPaymentQueryResponseData
    {
        $parent=parent::toAppResponse();
        if(!is_null($parent)){
            return $parent;
        }
        $data=BillPaymentItem::
        with(['service' => function ($query) {
            $query->where('id', request()->service);
        }])
        ->with('bundles')
        ->where('bill_payment_service_id',request()->service)
        ->where('status',1)
        ->first();

        $items=[];
        $firstItem=$this->extData->first();

        $bundles=$data->bundles->filter(function ($bundle) use($firstItem) {
            return $bundle->payload->denAmount==$firstItem->dueAmount;
        });
        $filled=[
            "item"=>$data->id,
            "bundle"=>$bundles->first()->id,
            //"amount:amount"=>$firstItem->lowestToPay
        ];
        $config=[
            "bundle"=>[
                "v"=>$bundles->pluck('id')->toArray(),
                //"s"=>$bundles->pluck('id')->toArray()
            ],
        ];
        $items=[

            new BillPaymentQueryResponseItemData(
                item:new BillPaymentQueryResponseSubitemItemData(
                    title:new NameData(
                        ar:Lang::get('Remain', locale: 'ar'),
                        en:"Remain"
                    ),
                    subtitle: new NameData(
                        ar:$firstItem->dataBalance,
                        en:$firstItem->dataBalance
                    )
                ),
                subitem:new BillPaymentQueryResponseSubitemItemData(
                    title:new NameData(
                        ar:Lang::get('Balance', locale: 'ar'),
                        en:"Balance"
                    ),
                    subtitle: new NameData(
                        ar:$firstItem->dueAmount,
                        en:$firstItem->dueAmount
                    )
                )
            ),
            // new BillPaymentQueryResponseItemData(
            //     item:new BillPaymentQueryResponseSubitemItemData(
            //         title:new NameData(
            //             ar:Lang::get('Amount', locale: 'ar'),
            //             en:"Amount"
            //         ),
            //         subtitle: new NameData(
            //             ar:$firstItem->dueAmount,
            //             en:$firstItem->dueAmount
            //         )
            //     ),
                // subitem:new BillPaymentQueryResponseSubitemItemData(
                //     title:new NameData(
                //         ar:Lang::get('Expire date', locale: 'ar'),
                //         en:"Expire date"
                //     ),
                //     subtitle: new NameData(
                //         ar:$firstItem->expiryDate?->toDateTimeString(),
                //         en:$firstItem->expiryDate?->toDateTimeString()
                //     )
                // )
            // ),
            new BillPaymentQueryResponseItemData(
                item:new BillPaymentQueryResponseSubitemItemData(
                    title:new NameData(
                        ar:Lang::get('Expire date', locale: 'ar'),
                        en:"Expire date"
                    ),
                    subtitle: new NameData(
                        ar:$firstItem->expiryDate?->toDateTimeString(),
                        en:$firstItem->expiryDate?->toDateTimeString()
                    )
                ),
                subitem:new BillPaymentQueryResponseSubitemItemData(
                    type:'button',
                    title:new NameData(
                        ar:Lang::get('Bill Details', locale: 'ar'),
                        en:"Bill Details"
                    ),
                    items:BillPaymentQueryResponseSubitemItemData::collect($this->extData->map(function($item,$key){
                        return new BillPaymentQueryResponseSubitemItemData(
                            foregroundColor: "#ffffff",
                            backgroundColor: "#0069A7",
                            title:new NameData(
                                ar:Lang::get('Bill :item',['item'=>$key+1], locale: 'ar'),
                                en:Lang::get('Bill :item',['item'=>$key+1], locale: 'en')
                            ),
                            items:BillPaymentQueryResponseSubitemItemData::collect(collect($item)->filter(function($value,$key){
                                return in_array($key,self::dataToShow);
                            })->map(function($value,$key){
                                // if($value instanceof Carbon){
                                //     $value=$value?->toDateTimeString();
                                // }
                                return new BillPaymentQueryResponseSubitemItemData(
                                    title:new NameData(
                                        ar:Lang::get("$key", locale: 'ar'),
                                        en:Lang::get("$key", locale: 'en')
                                    ),
                                    subtitle:new NameData(
                                        ar:$value instanceof Carbon?$value->toDateTimeString():"$value",
                                        en:"$value"
                                    ),
                                );
                            })->values(),BaseNonNullableDataCollection::class)
                        );
                    }),BaseNonNullableDataCollection::class)
                )

            )

        ];
        return new BillPaymentQueryResponseData(
            status: $this->status,
            config:$config,
            filled:$filled,
            items: BillPaymentQueryResponseItemData::collect($items, BaseNonNullableDataCollection::class)
        );

    }
}
