<?php

namespace App\Enums;
enum GiftStatusEnum:int {
    case INIT=0;
    case SUCCESS=1;
    case CLAIM=2;
    case CLAIMBACK=3;
    case ERROR=4;
    case SCHEDULE=5;

    public static function findByValue(string $value):GiftStatusEnum|null {
        return collect(GiftStatusEnum::cases())->filter(function($item) use($value){
            return $item->value==$value;
        })->first();
    }
    public static function color(string $value):string|null {
        $colors=[
            '0'=>'#6c757d',
            '1'=>'#536de6',
            '2'=>'#10c469',
            '3'=>'#f9c851',
            '4'=>'#ff5b5b',
            '5'=>'#35b8e0',
        ];
        return $colors[$value];
    }
}