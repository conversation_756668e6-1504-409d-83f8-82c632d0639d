<?php

namespace App\Http\Controllers\Admin;
use App\Http\Controllers\Controller;
use App\Mail\OtpSent;
use App\Mail\PasswordSent;
use App\Models\Merchant;
use App\Models\UserInterface;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use App\Models\User;
use Auth,Validator;

use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class UserController extends Controller
{

    public function __construct(Request $request){$this->middleware('auth');}
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if (! Auth::user()->canAny(['management.user.*','management.user.list'])) return abort(401);
        $filter=$request->all();

        $users =User::with('roles')->select('id','name','email');

        // $users =User::with('roles')
        // ->select(DB::raw('max(id) as id'),'name',DB::raw("coalesce(email,'') as email"),'roles.id as role_id',
        //     DB::raw("group_concat(DISTINCT roles.name SEPARATOR ',') as role_name"))
        // ->leftJoin('model_has_roles', 'model_id', '=', 'id')
        // ->leftJoin('roles', 'roles.id', '=', 'model_has_roles.role_id')
        //->groupBy("id");
        //->orderby(DB::raw("coalesce(roles.id,'a')"),'asc');

        if (!Auth::user()->hasRole('developer')) {
            $users = $users
            ->whereNotIn('id', function ($query) {
                $query->select('model_id')
                    ->from('model_has_roles')
                    ->whereIn(
                        'role_id',
                        function ($query) {
                                $query->select('id')
                                    ->from('roles')
                                    ->where('name', 'developer');
                            }
                    );
            });
        }

        if(isset($filter['searchname']) && $filter['searchname']!=''){
            $users = $users->where(function ($query) use ($request) {
                $query->where('name', 'LIKE', "%{$request->searchname}%")
                    ->orWhere('email', 'LIKE', "%{$request->searchname}%")
                    ->orWhere('id', 'LIKE', "%{$request->searchname}%");
            });
        }
        $users =$users->paginate(25);//->toArray();

        return view('default.admin.user.list')
        ->with('items',$users)
        ->with('filter',$filter);
    }
    public function search(Request $request){
        $items=[];
    	if($request->ajax()){
            if($request->filled('q')){
                $items=User::select('id','name as text')
                ->where('name','like','%'.$request->q.'%')
                ->orWhere('email','like','%'.$request->q.'%')
                ->orWhere('id',(int)$request->q)
                ->orderBy('name','asc')
                ->skip((($request->page??1)-1)*31)
                ->take(31)
                ->get();
            }
    		return response()->json(['items'=>$items]);
    	}
    }
    /**
     * Show the form for creating a resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function create()
    {
        if (!Auth::user()->canAny(['management.user.*', 'management.user.create']))
            return abort(401);
        $user_status=array(
            -1 => 'محظور',
            0 => 'غير مصنف',
            1 => 'موثوق'
        );
        $roles = Role::where('id', '>=', min(Auth::user()->roles()->pluck('id')->toArray()));
        if (!Auth::user()->hasRole('developer')) {
            $roles = $roles->where('name', '!=', 'developer');
        }
        $roles = $roles->get()->pluck('name', 'id');
        return view('default.admin.user.view')
        ->with('user_status',$user_status)
        ->with('roles', $roles);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        if (!Auth::user()->canAny(['management.user.*', 'management.user.create']))
            return abort(401);
        $validator = Validator::make($request->all(), [
            'name' => 'required|max:191',
            'email' => 'required|unique:users'
        ]);

        $user = new User;
        $user->name=$request->name;
        $user->email=$request->email;
        if($request->filled('sendPassword')){
            $password=Controller::password();
        }else{
            $validator = Validator::make($request->all(), [
                'password'=>'required|min:6',
                'c_password'=>'required|min:6'
            ]);
            if ($validator->fails())
                return back()-> with('errors',$validator->errors());

            if($request->password!=$request->c_password)
                return back()-> with('error','كلمة المرور ليست متطابقة');

            $password=$request->password;

        }
        $user->password= Hash::make($password);
        $user->status=$request->status;
        $user->image=\Storage::url("account-placeholder.jpg");
        $user->save();

        $user->syncRoles($request->input('roles') ?? []);

        if($request->filled('sendPassword')){
            NotificationService::email($user->email,data:new PasswordSent($password));
        }


        return redirect(url("admin/user/$user->id"))-> with('success',__("Operation accomplished successfully"));
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id){
        if (!Auth::user()->canAny(['management.user.*', 'management.user.view', 'management.user.edit']))
            return abort(401);

        $item = User::select('id','name','email','status')->with('merchant')->find($id);
        if ($item->hasRole('developer') && !Auth::user()->hasRole('developer'))
            return abort(401);

        $user_roles = $item->roles->pluck('id')->toArray();
        if (count($user_roles) == 0)
            $user_roles[] = 900000;

        $min_role = min(Auth::user()->roles()->pluck('id')->toArray());
        if ($min_role > min($user_roles))
            return abort(401);

        $user_status = array(
            -1 => 'محظور',
            0 => 'غير مصنف',
            1 => 'موثوق'
        );
        $roles = Role::where('id', '>=', $min_role);
        if (!Auth::user()->hasRole('developer')) {
            $roles = $roles->where('name', '!=', 'developer');
        }
        $roles = $roles->get()->pluck('name', 'id');

        return view('default.admin.user.view')
        ->with('item',$item)
        ->with('user_status',$user_status)
        ->with('roles', $roles);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        if (!Auth::user()->canAny(['management.user.*', 'management.user.edit']))
            return abort(401);

        $validator = Validator::make($request->all(), [
            'name' => 'required|max:191',
            'email' => 'required'
        ]);
        if ($validator->fails())
            return back()-> with('errors',$validator->errors());

        $user = User::with('merchant')->find($id);

       //check if current user have permission greater than edited user
        $user_roles = $user->roles->pluck('id')->toArray();
        if (count($user_roles) == 0)
            $user_roles[] = 900000;

        if (min(Auth::user()->roles()->pluck('id')->toArray()) > min($user_roles))
            return abort(401);


        $user->email=$request->email;
        $user->name=$request->name;

        $password=null;
        if($request->filled('sendPassword')){
            $password=Controller::password();
        }else if($request->password!=null && $request->password!=''){
            $password=$request->password;
        }

        if(!is_null($password)){
            $user->password= Hash::make($password);
        }
        $user->status=$request->status;
        $user->save();

        if (Auth::user()->hasRole('developer') && $user->hasRole('تاجر')){
            $validator = Validator::make($request->all(), [
                'merchant.name' => 'required|max:191',
                'merchant.phone' => 'nullable',
                'merchant.account_id' => 'required',
                'merchant.service_name' => 'nullable',
                'merchant.schema' => 'nullable',
                'merchant.is_north' => 'required',
                'merchant.is_collector' => 'required'
            ]);
            if ($validator->fails())
                back()-> with('errors',$validator->errors());


            if(is_null($user->merchant)){
                Merchant::create($request->merchant+["user_id"=> $user->id]);
            }else{
                $merchant=$user->merchant;
                $merchant->name=$request->input('merchant.name');
                $merchant->phone=$request->input('merchant.phone');
                $merchant->account_id=$request->input('merchant.account_id');
                $merchant->service_name=$request->input('merchant.service_name');
                $merchant->schema=$request->input('merchant.schema');
                $merchant->is_north=$request->input('merchant.is_north');
                $merchant->is_collector=$request->input('merchant.is_collector');
                $merchant->save();
            }
        }

        $user->syncRoles($request->input('roles') ?? []);
        UserInterface::withCacheUpdate('sidemenu' . $user->id);

        if($request->filled('sendPassword')){
            NotificationService::email($user->email,data:new PasswordSent($password));
        }

        return  back()-> with('success','تم حفظ التعديلات ');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id){

        if (!Auth::user()->canAny(['management.user.*', 'management.user.edit']))
            return abort(401);

        $user = User::with('roles')->find($id);
        $user_roles = $user->roles->pluck('id')->toArray();
        if (count($user_roles) == 0)
            $user_roles[] = 900000;

        if (min(Auth::user()->roles()->pluck('id')->toArray()) > min($user_roles))
            return abort(401);

        $user->delete();
        return back()-> with('success','تم حذف  '.$user->name.' المستخدم :'.$user->email);
    }

}
