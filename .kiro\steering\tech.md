# Technology Stack

## Backend Framework
- **<PERSON>vel 11** - PHP web application framework
- **PHP 8.3** - Server-side scripting language

## Database
- **Oracle Database** - Primary database with OCI8 driver
- **Redis** - Caching, sessions, and queue management

## Frontend & Mobile
- **Flutter** - Mobile app framework (compiled to web)
- **Vue.js 2** - JavaScript framework for web components
- **Laravel Mix** - Asset compilation and bundling
- **Bootstrap 4** - CSS framework

## Key Libraries & Packages
- **Laravel Passport** - OAuth2 authentication
- **Laravel Horizon** - Queue monitoring and management
- **<PERSON>vel Telescope** - Application debugging and monitoring
- **<PERSON><PERSON> Permission** - Role and permission management
- **Spa<PERSON> Settings** - Application settings management
- **Intervention Image** - Image processing
- **TCPDF** - PDF generation
- **Firebase** - Push notifications and real-time features
- **Guzzle HTTP** - HTTP client for API calls

## Development Tools
- **PHPUnit** - Testing framework
- **<PERSON>vel Debugbar** - Development debugging
- **Swagger/OpenAPI** - API documentation

## Common Commands

### Development
```bash
# Start development server
php artisan serve

# Run asset compilation
npm run dev
npm run watch    # Watch for changes
npm run prod     # Production build

# Database operations
php artisan migrate
php artisan migrate:fresh --seed

# Clear caches
php artisan cache:clear
php artisan optimize:clear
```

### Queue Management
```bash
# Start Horizon (queue worker)
php artisan horizon

# Pause/resume queues
php artisan horizon:pause
php artisan horizon:continue
```

### Testing
```bash
# Run tests
php artisan test
vendor/bin/phpunit
```

### Deployment
```bash
# Production deployment (see deploy.sh)
php artisan down           # Maintenance mode
git pull                   # Update code
php artisan migrate --force
php artisan cache:clear
php artisan up             # Exit maintenance mode
```

## Environment Configuration
- Uses `.env` file for environment-specific settings
- Oracle database connection with custom service name
- Redis for caching and queue management
- Firebase credentials stored in `resources/keys/`
- SSL certificates and secure communications required