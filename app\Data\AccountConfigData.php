<?php

namespace App\Data;

use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Normalizers\ObjectNormalizer;
use Spatie\LaravelData\DataCollection;
class AccountConfigData extends Data
{
    public const utilityPayment = 'up';
    public const cardless ='cl';
    public const gift = 'gt';
    public const groupTransfer = 'gp';
    public const yeahMoney = 'ym';
    public const selfTransfer = 'st';
    public const internalTransfer = 'it';
    public const invoicePayment = 'in';
    public const domesticTransfer = 'dt';
    public const goldTransfer = 'gl';
    public const waseelTransfer = 'wt';
    public const payNowPayLater = 'pnpl';
    public const ykpPayment = 'ykbp';
    public const oilPayment = 'oilp';
    public const splitPayment = 'sp';


    public ?AccountFilterData $up;
    public ?AccountFilterData $cl;
    public ?AccountFilterData $gt;
    public ?AccountFilterData $gp;
    public ?AccountFilterData $ym;
    public ?AccountFilterData $st;
    public ?AccountFilterData $it;
    public ?AccountFilterData $in;
    public ?AccountFilterData $dt;
    public ?AccountFilterData $gl;

    public ?AccountFilterData $wt;
    public ?AccountFilterData $pnpl;
    public ?AccountFilterData $ykbp;
    public ?AccountFilterData $oilp;

    public ?AccountFilterData $sp;


}
