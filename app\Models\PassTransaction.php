<?php

namespace App\Models;

use App\Enums\InvoiceTransactionsTypeEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PassTransaction extends Model
{
    use HasFactory;

    protected $fillable = ["pass_id","type","status",'account_id','receiver_mobile','amount','fee','reference_id',"external_reference_id","payment_result",'remarks','extra'];
    protected $casts = [
        'account_id' => 'object',
        'amount' => 'object',
        'fee' => 'object',
        'extra' => 'object',
        'payment_result' => 'object',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];
    // protected $hidden = [
    //     'pass_id'
    // ];
    protected $table = 'pass_transactions';

    public function pass()
    {
        return $this->belongsTo('App\Models\Pass');
    }
    
}
