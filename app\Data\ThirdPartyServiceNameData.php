<?php

namespace App\Data;

use <PERSON><PERSON>\LaravelData\Data;
use Spatie\LaravelData\Normalizers\ObjectNormalizer;

class ThirdPartyServiceNameData extends Data
{
    public function __construct(
        public ?string $gift,
        public ?string $wasil,
        public ?string $wasilFee,
        public ?string $oil,
        public ?string $oilFee,
        public ?string $oilExternal,
        public ?string $wheel,
        public ?string $split,
        public ?int $is_test,
        public ?ThirdPartyServiceNameData $test_data
    ) {
    }
    public static function gift():string {
        $settings=app(\App\Settings\ThirdPartySettings::class)->serviceName;
        return $settings->is_test?$settings->test_data->gift:$settings->gift;
    }
    public static function wasil():string {
        $settings=app(\App\Settings\ThirdPartySettings::class)->serviceName;
        return $settings->is_test?$settings->test_data->wasil:$settings->wasil;
    }
    public static function wasilFee():string {
        $settings=app(\App\Settings\ThirdPartySettings::class)->serviceName;
        return $settings->is_test?$settings->test_data->wasilFee:$settings->wasilFee;
    }

    public static function oil():string {
        $settings=app(\App\Settings\ThirdPartySettings::class)->serviceName;
        return $settings->is_test?$settings->test_data->oil:$settings->oil;
    }
    public static function oilFee():string {
        $settings=app(\App\Settings\ThirdPartySettings::class)->serviceName;
        return $settings->is_test?$settings->test_data->oilFee:$settings->oilFee;
    }
    public static function oilExternal():string {
        $settings=app(\App\Settings\ThirdPartySettings::class)->serviceName;
        return $settings->is_test?$settings->test_data->oilExternal:$settings->oilExternal;
    }

    public static function wheel():string {
        $settings=app(\App\Settings\ThirdPartySettings::class)->serviceName;
        return $settings->is_test?$settings->test_data->wheel:$settings->wheel;
    }
    public static function split():string {
        $settings=app(\App\Settings\ThirdPartySettings::class)->serviceName;
        return $settings->is_test?$settings->test_data->split:$settings->split;
    }
}
