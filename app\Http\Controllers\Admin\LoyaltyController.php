<?php

namespace App\Http\Controllers\Admin;
use App\Http\Controllers\Controller;
use App\Models\CustomerLoyalty;
use App\Models\CustomerType;
use App\Models\Loyalty;
use App\Models\PartyVerify;
use App\Models\Service;
use App\Scopes\CustomerScope;
use App\Scopes\UsernameScope;
use App\Services\LoyaltyService;
use App\Services\OBDX\AdminService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Auth;



class LoyaltyController extends Controller
{

    public function __construct(Request $request){$this->middleware('auth');}

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response|\Illuminate\Contracts\View\View
     */
    public function index(Request $request)
    {
        if (! Auth::user()->canAny(['loyalty.*','loyalty.view','loyalty.list'])) return abort(401);

        $filter=$request->all();

        $items=Loyalty::withoutGlobalScope(CustomerScope::class)
        ->with('user')
        ->orderBy('created_at','desc');

        $items=$items->paginate(15);

        return view('default.admin.loyalty.index')
        ->with('items', $items)
        ->with('filter', $filter);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        if (! Auth::user()->canAny(['loyalty.*','loyalty.create']))
            return abort(401);

        $this->validate($request,[
            'loyalty_card_id' => 'required',
            'points' => 'required|numeric',
            'note' => 'required|string|max:500',
            'party_id' => 'required',
        ]);



        $partyVerify=PartyVerify::withoutGlobalScope(CustomerScope::class)
        ->withoutGlobalScope(UsernameScope::class)
        ->where('party_id',$request->party_id)->first();
        if(is_null($partyVerify)){
            return back()->with('error',__("No party found!"));
        }

        $object=new \stdClass();
        $customerInfo = AdminService::customerInfo($object,[
            "partyId"=>$partyVerify->party_id,
            "userType"=>'retailuser'
        ]);

        if(is_null($customerInfo) || !is_object($customerInfo)){
            return back()->with('error',__("No party found!"));
        }

        $userInfo= AdminService::userInfo($object,$customerInfo->username);
        if(is_null($userInfo) || !is_object($userInfo)){
            return back()->with('error',__("No party found!"));
        }

        $object=new \stdClass();
        $object->id=$userInfo->partyId->value;
        $object->name=html_entity_decode($userInfo->firstName)." ".html_entity_decode($userInfo->middleName??"")." ".html_entity_decode($userInfo->lastName);
        $object->email= html_entity_decode($userInfo->emailId);
        $object->phone= $userInfo->mobileNumber;

        $identifier=LoyaltyService::getCustomerIdentifier($object);
        if(is_null($identifier)){
            return back()->with('error',__("Error in getting customer identifier!"));
        }

        $object=new \stdClass();
        $object->note=$request->note;
        $object->points= $request->points;
        $object->cardId= $request->loyalty_card_id;
        $object->memberId= $identifier;
        $result=LoyaltyService::addPoints( $object);

        if( $result->status->message->code=="0"){
            $loyalty=Loyalty::create([
                "loyalty_card_id"=>$request->loyalty_card_id,
                "points"=>$request->points,
                "note"=>$request->note,
                "party_id"=>$userInfo->partyId->value,
                "user_id"=>auth()->user()->id,
                "result"=>$result->getAdditionalData()["response"]
            ]);
            return back()->with('success',__("Seccussfully added points!"));
        }

        return back()->with('error',__($result->status->message->title));
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Loyalty $loyalty
     * @return ?\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function show(Loyalty $loyalty){
        return response(view('default.admin.loyalty.model')
        ->with('json',json_decode(json_encode($loyalty->result)))/*,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED*/);
    }

    public function cards(Request $request){
        if(! Auth::user()->canAny(['loyalty.*','loyalty.create'])) return abort(401);

        $object=new \stdClass();
        $result=LoyaltyService::getCards( $object);
        return response()->json(['items'=>collect($result)->map(function($element){
            return [
                "id"=>$element->unique_identifier,
                "text"=>$element->head->ar_SA??$element->head->en_US,
            ];
        })]);

        // $items=[];
    	// if($request->ajax()){
        //     if($request->filled('q')){

        //     }
    	// 	return response()->json(['items'=>$items]);
    	// }

    }
}
