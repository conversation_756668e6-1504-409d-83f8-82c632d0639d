<?php

namespace App\Data;

use App\Data\Classes\BranchData;
use App\Data\Classes\ProductData;
use Illuminate\Support\Collection;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Data;

class CreditCardData extends Data
{
    const masterProductCodes=[
        "MC001",
        "MC002",
        "MC003"
    ];
    const visaProductCodes=[
        "V001",
        "V002",
        "V003",
        "V004"
    ];
    const productCodes=[
        "MC001"=>"MascterCard Standard",
        "MC002"=>"MasterCard Gold",
        "MC003"=>"MasterCard Platinum",
        "V001"=>"Visa Classic",
        "V002"=>"Visa Gold",
        "V003"=>"Visa Platinum",
        "V004"=>"Visa Infinite",
    ];
    const statusNames=[
        "B"=>"Blocked",
        "E"=>"Expired Card",
        "I"=>"InActive",
        "A"=>"Active",
        "C"=>"Closed",
        "F"=>"Pickup Fraud",
        "L"=>"Pickup Lost",
        "S"=>"Pickup Stolen",
        "T"=>"Temporarily Blocked",
    ];
    #[MapInputName('name_on_card')]
    public ?string $name;
    //#[MapInputName('primary_card')]
    public string $type;
    ##[MapInputName('card_renewal_dt')]
    public ?string $expiryDate;
    #[MapInputName('card_no')]
    public ?string $cardNumber;
    public ?string $product;
    public ?string $provider;

    public ?string $statusName;
    public ?int $status;
    public function with():array {
        return [
            'walletCurrency'=>"USD",
            'classification'=>"CSC",
            "functions"=>[]
        ];
    }
    public static function prepareForPipeline(array $properties) : array
    {
        $properties['type']= $properties["primary_card"]=="Y"?"Primary":"Supplementary";
        $properties['expiryDate']= substr($properties["card_renewal_dt"]??"",2, 2).substr($properties["card_renewal_dt"]??"",5, 2);
        $properties['product']= static::productCodes[$properties["card_product"]]??"";
        $properties['provider']= in_array($properties["card_product"]??"",static::visaProductCodes)?"visa":(in_array($properties["card_product"]??"",static::masterProductCodes)?"mastercard":"");
        $properties['statusName']= static::statusNames[$properties["card_status"]]??"";
        $properties['status']= $properties["card_status"]=="A"?1:0;

        return $properties;
    }

}
