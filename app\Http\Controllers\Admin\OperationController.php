<?php

namespace App\Http\Controllers\Admin;
use App\Data\Pass\PassCreateRequestData;
use App\Enums\InvoiceTransactionsTypeEnum;
use App\Enums\TransactionStatusEnum;
use App\Http\Controllers\Controller;
use App\Jobs\ProcessClaimedTimeoutWasil;
use App\Jobs\ProcessGift;
use App\Jobs\ProcessResolveWasil;
use App\Jobs\ProcessUnreceivedWasil;
use App\Jobs\ProcessUsedWheel;
use App\Models\AgentTransaction;
use App\Models\Cardless;
use App\Models\Gift;
use App\Models\Gold;
use App\Models\Harvest;
use App\Models\Invoice;
use App\Models\OilPayment;
use App\Models\OilRegion;
use App\Models\OpenAccount;
use App\Models\Pass;
use App\Models\Registration;
use App\Models\Transfer;
use App\Models\WheelTransaction;
use App\Models\YeahMoney;
use App\Scopes\CustomerScope;
use App\Services\PassService;
use App\Models\User;
use Carbon\Carbon;
use DB;
use Illuminate\Http\Request;
use Auth;


class OperationController extends Controller
{

    public function __construct(Request $request){$this->middleware('auth');}

    public function index(Request $request){
        return abort(404);
    }
    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Service  $service
     * @return \Illuminate\Contracts\View\View
     */
    public function show(Request $request,$type){



        $hiddenParameters=[];

        if (!$request->filled('fromDate')) {
            $request->query->add(['fromDate'=>date('Y-m-d')]);
        }
        if (!$request->filled('toDate')) {
            $request->query->add(['toDate'=>date('Y-m-d')]);
        }
        $filter=$request->all();

        $start=Carbon::parse( $filter['fromDate'])->startOfDay()->toDateTimeString();
        $end=Carbon::parse($filter['toDate'])->endOfDay()->toDateTimeString();


        $partyId="party_id";
        $typeColumn="type";
        switch($type){
            case 'cardless':
                if (! Auth::user()->canAny(['operation.cardless.*','operation.cardless.list'])) return abort(401);
                $items=Cardless::select('id','payment_id','device_key','external_reference_id','expiry_date','status','created_at','updated_at')
                ->whereRaw("created_at between {$this->getDateByConnection('?','YYYY-MM-DD HH24:MI:SS')} and {$this->getDateByConnection('?','YYYY-MM-DD HH24:MI:SS')}",[$start,$end])
                ->orderBy("created_at","desc");

                $partyId="device_key";
                break;
            case 'agent_transaction':
                if (! Auth::user()->canAny(['operation.agent_transaction.*','operation.agent_transaction.list'])) return abort(401);
                $items=AgentTransaction::select('id','party_id','type','reference_id','external_reference_id','amount->amount as amount','amount->currency as currency','status','created_at','updated_at')
                ->whereRaw("created_at between {$this->getDateByConnection('?','YYYY-MM-DD HH24:MI:SS')} and {$this->getDateByConnection('?','YYYY-MM-DD HH24:MI:SS')}",[$start,$end])
                ->orderBy("created_at","desc");
                break;
            case 'gift':
                if (! Auth::user()->canAny(['operation.gift.*','operation.gift.list'])) return abort(401);
                $items=Gift::withoutGlobalScope(CustomerScope::class)->select('id','party_id','party_name','type','remarks','amount->amount as amount','amount->currency as currency','status','created_at','updated_at')
                ->whereRaw("created_at between {$this->getDateByConnection('?','YYYY-MM-DD HH24:MI:SS')} and {$this->getDateByConnection('?','YYYY-MM-DD HH24:MI:SS')}",[$start,$end])
                ->orderBy("created_at","desc");
                if($request->filled('status')){
                    $items=$items->whereHas('transactions',function($query) use($filter){
                        return $query->where('status',$filter['status']);
                    });
                }
                break;
            case 'harvest':
                if (! Auth::user()->canAny(['operation.harvest.*','operation.harvest.list'])) return abort(401);
                $items=Harvest::withoutGlobalScope(CustomerScope::class)->select('id','party_id','service_code_id','request_id','amount->amount as amount','amount->currency as currency','status','created_at','updated_at')
                ->whereRaw("created_at between {$this->getDateByConnection('?','YYYY-MM-DD HH24:MI:SS')} and {$this->getDateByConnection('?','YYYY-MM-DD HH24:MI:SS')}",[$start,$end])
                ->orderBy("created_at","desc");
                break;
            case 'invoice':
                if (! Auth::user()->canAny(['operation.invoice.*','operation.invoice.list'])) return abort(401);
                $items=Invoice::select('id','txn_token','amount->amount as amount','amount->currency as currency','reference_id','external_reference_id','initiator_id','initiator_type','status','created_at','updated_at')
                ->whereRaw("created_at between {$this->getDateByConnection('?','YYYY-MM-DD HH24:MI:SS')} and {$this->getDateByConnection('?','YYYY-MM-DD HH24:MI:SS')}",[$start,$end])
                ->orderBy("created_at","desc");

                if(isset($filter['searchname']) && $filter['searchname']!=''){
                    $items=$items->where(function($query) use($partyId,$filter){
                        $query
                        ->where('txn_token',$filter['searchname'])
                        ->orWhere('initiator_type',$filter['searchname'])
                        ->orWhere('external_reference_id',$filter['searchname']);
                        if(is_numeric($filter['searchname'])){
                            $query->orWhere('id',$filter['searchname'])
                            ->orWhere('initiator_id',$filter['searchname']);
                        }
                    });
                }

                $partyId="initiator_id";
                $hiddenParameters=Invoice::$hiddenParameters;
                break;
            case 'open_account':
                if (! Auth::user()->canAny(['operation.open_account.*','operation.open_account.list'])) return abort(401);
                $items=OpenAccount::select('id','party_id','reference_id','branch_id','currency_id','product_id','account_id','opened_account_id','status','created_at','updated_at')
                ->whereRaw("created_at between {$this->getDateByConnection('?','YYYY-MM-DD HH24:MI:SS')} and {$this->getDateByConnection('?','YYYY-MM-DD HH24:MI:SS')}",[$start,$end])
                ->orderBy("created_at","desc");
                break;
            case 'yeah_money':
                if (! Auth::user()->canAny(['operation.yeah_money.*','operation.yeah_money.list'])) return abort(401);
                $items=YeahMoney::withoutGlobalScope(CustomerScope::class)->select('id','party_id','external_reference_id','created_at','updated_at')
                ->whereRaw("created_at between {$this->getDateByConnection('?','YYYY-MM-DD HH24:MI:SS')} and {$this->getDateByConnection('?','YYYY-MM-DD HH24:MI:SS')}",[$start,$end])
                ->orderBy("created_at","desc");
                break;
            case 'gold':
                if (! Auth::user()->canAny(['operation.gold.*','operation.gold.list'])) return abort(401);
                $items=Gold::select('id','party_id','amount->amount as amount','amount->currency as currency','status','created_at','updated_at')
                ->whereRaw("created_at between {$this->getDateByConnection('?','YYYY-MM-DD HH24:MI:SS')} and {$this->getDateByConnection('?','YYYY-MM-DD HH24:MI:SS')}",[$start,$end])
                ->whereNull('manual_type')
                ->orderBy("created_at","desc");
                break;
            case 'wasil':
                if (! Auth::user()->canAny(['operation.wasil.*','operation.wasil.list'])) return abort(401);
                $items=Pass::with(['transaction'=>function($query){
                    return $query->select('pass_id','account_id->value as account',
                    'amount->amount as amount','amount->currency as currency',
                    'reference_id',DB::raw("coalesce(cast(receiver_mobile as varchar2(255)),'') as receiver_mobile"),
                  //  DB::raw("coalesce( json_value(extra, '$.\"sender_account_no\"'),'') as sender_mobile"),
                    'extra->sender_account_no as sender_mobile',
                    'extra->mpt as tracking_code','extra->financial_reference as financial_reference');
                }])->leftJoinSub(User::select('id as user_id','name'),'user',function($join){
                    $join->on('user.user_id','=','resolved_by');
                })->select('id','party_id','type','status','resolved','name as resolved_by','created_at','updated_at')
                ->whereRaw("created_at between {$this->getDateByConnection('?','YYYY-MM-DD HH24:MI:SS')} and {$this->getDateByConnection('?','YYYY-MM-DD HH24:MI:SS')}",[$start,$end])
                ->orderBy("created_at","desc");

                if($request->filled('amount')){
                    $items=$items->whereHas('transactions',function($query) use($request){
                        return $query->where('amount->amount',$request->amount);
                    });
                }
                if($request->filled('defected')){
                    $items=$items->where('status',TransactionStatusEnum::COMPLETED->value)
                    ->where('type',InvoiceTransactionsTypeEnum::Refund->value)
                    ->whereDoesntHave('transaction',function($query){
                        return $query->where('status', TransactionStatusEnum::COMPLETED->value)
                        ->where('type',InvoiceTransactionsTypeEnum::Refund->value);
                    });
                }


                if(isset($filter['searchname']) && $filter['searchname']!=''){
                    $items=$items->where(function($query) use($partyId,$filter){
                        $query
                        ->where("$partyId",$filter['searchname'])
                        ->orWhereHas('transactions',function($query) use($filter){
                            return $query
                            ->where('reference_id',$filter['searchname'])
                            ->orWhere(DB::raw("cast(receiver_mobile as varchar2(255))"),$filter['searchname'])
                            ->orWhere('extra->mpt',$filter['searchname'])
                            ->orWhere('extra->financial_reference',$filter['searchname'])
                            ->orWhere('extra->sender_account_no',$filter['searchname']);
                        });
                        if(is_numeric($filter['searchname'])){
                            $query->orWhere('id',$filter['searchname']);
                        }
                    });
                }

                break;
            case 'registration':
                if (! Auth::user()->canAny(['operation.registration.*','operation.registration.list'])) return abort(401);
                $items=Registration::select('id',"type","status",'reference_id','created_at','updated_at')
                ->whereRaw("created_at between {$this->getDateByConnection('?','YYYY-MM-DD HH24:MI:SS')} and {$this->getDateByConnection('?','YYYY-MM-DD HH24:MI:SS')}",[$start,$end])
                ->orderBy("created_at","desc");
                $partyId="device_key";
                break;
            case 'transfer':
                if (! Auth::user()->canAny(['operation.transfer.*','operation.transfer.list'])) return abort(401);
                $items=Transfer::select('id','reference_id','party_id','type','payload->amount->amount as amount','payload->amount->currency as currency','payload->debitAccountId->value as account','payload->receiverNumber as receiver_number','payload->receiverName as receiver_name','created_at','updated_at')
                ->whereRaw("created_at between {$this->getDateByConnection('?','YYYY-MM-DD HH24:MI:SS')} and {$this->getDateByConnection('?','YYYY-MM-DD HH24:MI:SS')}",[$start,$end])
                ->orderBy("created_at","desc");

                if(isset($filter['searchname']) && $filter['searchname']!=''){
                    $items=$items->where(function($query) use($partyId,$filter){
                        $query
                        ->where("$partyId",$filter['searchname'])
                        ->orWhere('payload->debitAccountId->value',$filter['searchname'])
                        ->orWhere('payload->receiverNumber',$filter['searchname']);
                        if(is_numeric($filter['searchname'])){
                            $query->orWhere('id',$filter['searchname']);
                        }
                    });
                }
                break;
            case 'oil':
                if (! Auth::user()->canAny(['operation.oil.*','operation.oil.list'])) return abort(401);
                $items=OilPayment::select('id','party_id','reference_id','external_reference_id','transaction_id','status','type','region_name','customer_account_id->value as account','amount->amount as amount','fee->amount as fee','created_at','updated_at')
                ->leftJoinSub(OilRegion::select('id as region_id','name->ar as region_name'),'region',function($join){
                    $join->on('region_id','=','oil_region_id');
                })
                ->whereRaw("created_at between {$this->getDateByConnection('?','YYYY-MM-DD HH24:MI:SS')} and {$this->getDateByConnection('?','YYYY-MM-DD HH24:MI:SS')}",[$start,$end])
                ->orderBy("created_at","desc");

                if(isset($filter['searchname']) && $filter['searchname']!=''){
                    $items=$items->where(function($query) use($partyId,$filter){
                        $query
                        ->where("$partyId",$filter['searchname'])
                        ->orWhere('customer_account_id->value',$filter['searchname'])
                        ->orWhere('reference_id',$filter['searchname'])
                        ->orWhere('external_reference_id',$filter['searchname'])
                        ->orWhere('transaction_id',$filter['searchname']);
                        if(is_numeric($filter['searchname'])){
                            $query->orWhere('id',$filter['searchname']);
                        }
                    });
                }
                break;
                case 'wheel':
                    if (! Auth::user()->canAny(['operation.wheel.*','operation.wheel.list'])) return abort(401);
                    $typeColumn="wheel_item->type";

                    $items=WheelTransaction::select('id','transaction_id','status','party_id','party_name','account_id->value as account','wheel_item->value as amount','wheel_item->type as type','resolved','name as resolved_by','created_at','updated_at')
                    ->leftJoinSub(User::select('id as user_id','name'),'user',function($join){
                        $join->on('user.user_id','=','resolved_by');
                    })
                    ->whereNotNull('wheel_item')
                    ->whereRaw("created_at between {$this->getDateByConnection('?','YYYY-MM-DD HH24:MI:SS')} and {$this->getDateByConnection('?','YYYY-MM-DD HH24:MI:SS')}",[$start,$end])
                    ->orderBy("created_at","desc");

                    if($request->filled('defected')){
                        $items=$items->where('status',TransactionStatusEnum::INIT->value)
                        ->whereNotNull('account_id->value');
                    }

                    if(isset($filter['searchname']) && $filter['searchname']!=''){
                        $items=$items->where(function($query) use($partyId,$filter){
                            $query
                            ->where("$partyId",$filter['searchname'])
                            ->orWhere('account_id->value',$filter['searchname'])
                            ->orWhere('transaction_id',$filter['searchname']);
                            if(is_numeric($filter['searchname'])){
                                $query->orWhere('id',$filter['searchname']);
                            }
                        });
                    }
                    break;
        }

        if(!isset($items)){
            return abort(404);
        }
        if(in_array($type,["wasil",'harvest','invoice','gold','transfer',"oil","wheel"]) && !$request->filled('defected')){
            if($request->filled('status')){
                $items=$items->where('status',$filter['status']);
            }
            if($request->filled('type')){
                $items=$items->where($typeColumn,$filter['type']);
            }
        }
        if(in_array($type,["yeah_money"])){
            if($request->filled('status')){
                $items=$items->where('check_status',$filter['status']);
            }
        }


        if(!in_array($type,["wasil","transfer","invoice"]) && isset($filter['searchname']) && $filter['searchname']!=''){
            $items=$items->where(function($query) use($partyId,$filter,$type){
                $query->where("$partyId",$filter['searchname']);
                if($type=="harvest"){
                    $query->orWhere('request_id',$filter['searchname']);
                }
                if(is_numeric($filter['searchname'])){
                    $query->orWhere('id',$filter['searchname']);
                }
            });
        }

        $items=$items->paginate(15);
        if(!auth()->user()->hasRole('developer')){
            $item=$this->hideParameters($items,$hiddenParameters);
        }
        return view('default.admin.operation.index', compact('type','items','filter'));
        // ->with('type', $type)
        // ->with('items', $items)
        // ->with('filter', $filter);
    }

    /**
     * Display the specified resource.
     *
     * @param  string $type
     * @param  string $id
     * @return \Illuminate\Contracts\View\View
     */
    public function details(Request $request,$type,$id){

        $hiddenParameters=[];
        switch($type){
            case 'cardless':
                if (! Auth::user()->canAny(['operation.cardless.*','operation.cardless.view','operation.cardless.edit'])) return abort(401);
                $item=Cardless::with("logs");
                $hiddenParameters=Cardless::$hiddenParameters;
                break;
            case 'agent_transaction':
                if (! Auth::user()->canAny(['operation.agent_transaction.*','operation.agent_transaction.view','operation.agent_transaction.edit'])) return abort(401);
                $item=AgentTransaction::with("logs");
                break;
            case 'gift':
                if (! Auth::user()->canAny(['operation.gift.*','operation.gift.view','operation.gift.edit'])) return abort(401);
                $item=Gift::withoutGlobalScope(CustomerScope::class)->with("logs");
                break;
            case 'harvest':
                if (! Auth::user()->canAny(['operation.harvest.*','operation.harvest.view','operation.harvest.edit'])) return abort(401);
                $item=Harvest::withoutGlobalScope(CustomerScope::class)->with("logs");
                $hiddenParameters=Harvest::$hiddenParameters;
                break;
            case 'invoice':
                if (! Auth::user()->canAny(['operation.invoice.*','operation.invoice.view','operation.invoice.edit'])) return abort(401);
                $item=Invoice::with("logs");
                $hiddenParameters=Invoice::$hiddenParameters;
                break;
            case 'open_account':
                if (! Auth::user()->canAny(['operation.open_account.*','operation.open_account.view','operation.open_account.edit'])) return abort(401);
                $item=OpenAccount::with("logs");
                break;
            case 'yeah_money':
                if (! Auth::user()->canAny(['operation.yeah_money.*','operation.yeah_money.view','operation.yeah_money.edit'])) return abort(401);
                $item=YeahMoney::withoutGlobalScope(CustomerScope::class)->with("logs");
                break;
            case 'gold':
                if (! Auth::user()->canAny(['operation.gold.*','operation.gold.view','operation.gold.edit'])) return abort(401);
                $item=Gold::with("logs")->whereNull('manual_type');
                break;
            case 'wasil':
                if (! Auth::user()->canAny(['operation.wasil.*','operation.wasil.view','operation.wasil.edit'])) return abort(401);
                $item=Pass::with("logs")->with(['resolvedUser'=>function($query){
                    return $query->select('id','name');
                }]);
                break;
            case 'registration':
                if (! Auth::user()->canAny(['operation.registration.*','operation.registration.view','operation.registration.edit'])) return abort(401);
                $item=Registration::with("logs");
                break;
            case 'transfer':
                if (! Auth::user()->canAny(['operation.transfer.*','operation.transfer.view','operation.transfer.edit'])) return abort(401);
                $item=Transfer::with("logs");
                break;
            case 'oil':
                if (! Auth::user()->canAny(['operation.oil.*','operation.oil.view','operation.oil.edit'])) return abort(401);
                $item=OilPayment::with("logs");
                break;
            case 'wheel':
                if (! Auth::user()->canAny(['operation.wheel.*','operation.wheel.view','operation.wheel.edit'])) return abort(401);
                $item=WheelTransaction::with("logs");
                break;
        }
        if(!isset($item)){
            return abort(404);
        }

        $item=$item->find($id);

        if(!auth()->user()->hasRole('developer')){
            $item=$this->hideParameters($item,$hiddenParameters);
        }
        return view('default.admin.operation.view')
        ->with('type', $type)
        ->with('item', $item);
    }

    /**
     * Display the specified resource.
     *
     * @param  string $type
     * @param  string $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function actions(Request $request,$type,$action){

        switch($type){
            case 'wasil':
                if (! Auth::user()->canAny(['operation.wasil.*','operation.wasil.sync_expired'])) return abort(401);
                if($action=="sync_expired"){
                    ProcessUnreceivedWasil::dispatchSync();
                }
                break;
            case 'gift':
                if (! Auth::user()->canAny(['operation.gift.*','operation.gift.sync_scheduled'])) return abort(401);
                if($action=="sync_scheduled"){
                    ProcessGift::dispatchSync();
                }
                break;
        }

        return abort(\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED)->with('success',__("Operation accomplished successfully"));
    }

    /**
     * Display the specified resource.
     *
     * @param  string $type
     * @param  string $id
     * @return ?\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function process(Request $request,$type,$action,$id){

        switch($type){
            case 'wasil':
                if($action=="status"){
                    if (! Auth::user()->canAny(['operation.wasil.*','operation.wasil.status'])) return abort(401);
                    $item=Pass::with('transactions')->where("id",$id)->first();
                    $transaction=$item->transactions
                    ->where('status',$item->status)
                    ->where('type',$item->type)
                    ->first();

                    if(is_null($transaction) && $item->status==TransactionStatusEnum::COMPLETED->value){
                        $transaction=$item->transactions
                        ->where('type',$item->type)
                        ->first();
                        // $transaction->status=TransactionStatusEnum::PENDING->value;
                        // $transaction->save();
                    }

                    $requestData=PassCreateRequestData::from([
                        'reference_id'          => $transaction->reference_id,
                        'receiver_mobile'       => $transaction->receiver_mobile,
                        "amount"                => $transaction->amount,
                        "external_reference_id" => $transaction->extra?->transaction_reference??$transaction->extra?->operation_reference,
                        "operation_reference"   => $transaction->extra?->operation_reference??null,
                    ]);
                    $response=PassService::findByReference($requestData);
                    $extra=$response->getAdditionalData()['extra']??null;
                    $actions=[];
                    if($item->type==InvoiceTransactionsTypeEnum::Claim->value){
                        if(in_array( $item->status,[TransactionStatusEnum::ERROR->value]) && $item->resolved!=1){
                            if (Auth::user()->canAny(['operation.wasil.*','operation.wasil.resolve'])){
                                $actions[]=[
                                    "name"=>"Auto Resolve",
                                    "url"=>"/admin/operation/process/$type/resolve/$id",
                                    "class"=>"btn-danger"
                                ];
                            }
                            if (Auth::user()->canAny(['operation.wasil.*','operation.wasil.menual_resolve'])){
                                $actions[]=[
                                    "name"=>"Menual Resolve",
                                    "url"=>"/admin/operation/process/$type/menualResolve/$id",
                                    "class"=>"btn-primary"
                                ];
                            }
                        }
                    }else if($item->type==InvoiceTransactionsTypeEnum::Payment->value && $item->resolved!=1){
                        if(in_array( $item->status,[TransactionStatusEnum::ERROR->value]) && $response->status->message->code=="0" && isset($extra->result_code)
                            &&  $transaction->payment_result?->amount?->status==InvoiceTransactionsTypeEnum::Reverse->value
                        ){
                            if (Auth::user()->canAny(['operation.wasil.*','operation.wasil.fix'])){
                                $actions[]=[
                                    "name"=>"Fix",
                                    "url"=>"/admin/operation/process/$type/fix/$id",
                                    "class"=>"btn-primary"
                                ];
                            }
                        }else if($item->resolved!=1){
                            if(in_array( $item->status,[TransactionStatusEnum::ERROR->value])){
                                if (Auth::user()->canAny(['operation.wasil.*','operation.wasil.menual_resolve'])){
                                    $actions[]=[
                                        "name"=>"Menual Resolve",
                                        "url"=>"/admin/operation/process/$type/menualResolve/$id",
                                        "class"=>"btn-primary"
                                    ];
                                }
                            }else if(in_array( $item->status,[TransactionStatusEnum::PENDING->value])){
                                if (Auth::user()->canAny(['operation.wasil.*','operation.wasil.resolve'])){
                                    $actions[]=[
                                        "name"=>"Auto Resolve",
                                        "url"=>"/admin/operation/process/$type/resolve/$id",
                                        "class"=>"btn-danger"
                                    ];
                                }
                                if (Auth::user()->canAny(['operation.wasil.*','operation.wasil.menual_resolve'])){
                                    $actions[]=[
                                        "name"=>"Menual Resolve",
                                        "url"=>"/admin/operation/process/$type/menualResolve/$id",
                                        "class"=>"btn-primary"
                                    ];
                                }
                            }
                        }
                    }else if($item->type==InvoiceTransactionsTypeEnum::Refund->value && $item->resolved!=1){
                        if(in_array( $item->status,[TransactionStatusEnum::ERROR->value]) && $response->status->message->code=="0" && isset($extra->result_code)){
                            if (Auth::user()->canAny(['operation.wasil.*','operation.wasil.resolve'])){
                                $actions[]=[
                                    "name"=>"Auto Resolve",
                                    "url"=>"/admin/operation/process/$type/resolve/$id",
                                    "class"=>"btn-danger"
                                ];
                            }
                            if (Auth::user()->canAny(['operation.wasil.*','operation.wasil.menual_resolve'])){
                                $actions[]=[
                                    "name"=>"Menual Resolve",
                                    "url"=>"/admin/operation/process/$type/menualResolve/$id",
                                    "class"=>"btn-primary"
                                ];
                            }
                        }

                       // && $response->status->message->code=="DIGX_SWITCH_WASIL_NOT_FOUND_001"
                    }

                    return response(view('default.admin.operation.model')
                    ->with('json',json_decode($response->toJson()))
                    ->with('actions', $actions)/*,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED*/);
                }else if(in_array($action,["resolve","fix"])){
                    if($action=="fix"){
                        if (! Auth::user()->canAny(['operation.wasil.*','operation.wasil.fix'])) return abort(401);
                    }else{
                        if (! Auth::user()->canAny(['operation.wasil.*','operation.wasil.resolve'])) return abort(401);
                    }
                    $item=Pass::with('transactions')->where("id",$id)->first();
                    $transaction=$item->transactions
                    ->where('status',$item->status)
                    ->where('type',$item->type)
                    ->first();

                    $requestData=PassCreateRequestData::from([
                        'reference_id'          => $transaction->reference_id,
                        'receiver_mobile'       => $transaction->receiver_mobile,
                        "amount"                => $transaction->amount,
                        "external_reference_id" => $transaction->extra?->transaction_reference??$transaction->extra?->operation_reference,
                    ]);

                    if($item->type==InvoiceTransactionsTypeEnum::Claim->value){
                        ProcessClaimedTimeoutWasil::dispatchSync(auth()->user(),$requestData,$item,$transaction);
                    }else if(in_array($item->type,[InvoiceTransactionsTypeEnum::Payment->value,$item->type,InvoiceTransactionsTypeEnum::Refund->value])){
                        ProcessResolveWasil::dispatchSync(auth()->user(),$requestData,$item,$transaction);
                    }

                    return back();

                }else if($action=="menualResolve"){
                    if (! Auth::user()->canAny(['operation.wasil.*','operation.wasil.menual_resolve'])) return abort(401);
                    $item=Pass::with('transactions')->where("id",$id)->first();
                    $transaction=$item->transactions
                    ->where('status',$item->status)
                    ->where('type',$item->type)
                    ->first();

                    if(!in_array( $item->status,[TransactionStatusEnum::ERROR->value,TransactionStatusEnum::PENDING->value])){
                        return back()->with('error',__("You can't resolve this transaction!"));
                    }

                    $item->resolved=1;
                    $item->resolved_by=auth()->user()->id;
                    $item->save();

                    return back();
                }
                break;
            case 'wheel':
                if($action=="status"){
                    if (! Auth::user()->canAny(['operation.wheel.*','operation.wheel.status'])) return abort(401);
                    $item=WheelTransaction::withoutGlobalScope(CustomerScope::class)->find($id);
                    $actions=[];
                    if($item->status==TransactionStatusEnum::INIT->value && isset($item->account_id->value)){
                        if($item->resolved!=1){
                            if (Auth::user()->canAny(['operation.wheel.*','operation.wheel.resolve'])){
                                $actions[]=[
                                    "name"=>"Auto Resolve",
                                    "url"=>"/admin/operation/process/$type/resolve/$id",
                                    "class"=>"btn-danger"
                                ];
                            }
                            if (Auth::user()->canAny(['operation.wheel.*','operation.wheel.fix'])){
                                $actions[]=[
                                    "name"=>"Fix",
                                    "url"=>"/admin/operation/process/$type/fix/$id",
                                    "class"=>"btn-primary"
                                ];
                            }
                        }
                    }else if($item->resolved!=1 && $item->status==TransactionStatusEnum::INIT->value && !isset($item->account_id->value) && Carbon::parse($item->created_at)->addMinutes(1)->isBefore(Carbon::now()) ){
                        if (Auth::user()->canAny(['operation.wheel.*','operation.wheel.fix'])){
                            $actions[]=[
                                "name"=>"Fix",
                                "url"=>"/admin/operation/process/$type/fix/$id",
                                "class"=>"btn-primary"
                            ];
                        }
                    }else if($item->status==TransactionStatusEnum::ERROR->value && $item->resolved!=1){
                        if (Auth::user()->canAny(['operation.wheel.*','operation.wheel.resolve'])){
                            $actions[]=[
                                "name"=>"Auto Resolve",
                                "url"=>"/admin/operation/process/$type/resolve/$id",
                                "class"=>"btn-danger"
                            ];
                        }
                        if (Auth::user()->canAny(['operation.wheel.*','operation.wheel.fix'])){
                            $actions[]=[
                                "name"=>"Fix",
                                "url"=>"/admin/operation/process/$type/fix/$id",
                                "class"=>"btn-primary"
                            ];
                        }
                    }else if($item->status==TransactionStatusEnum::PENDING->value && $item->resolved!=1){
                        if (Auth::user()->canAny(['operation.wheel.*','operation.wheel.resolve'])){
                            $actions[]=[
                                "name"=>"Auto Resolve",
                                "url"=>"/admin/operation/process/$type/resolve/$id",
                                "class"=>"btn-danger"
                            ];
                        }
                    }

                    return response(view('default.admin.operation.model')
                    ->with('json',json_decode($item->toJson()))
                    ->with('actions', $actions)/*,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED*/);
                }else if(in_array($action,["resolve","fix"])){
                    if($action=="fix"){
                        if (! Auth::user()->canAny(['operation.wheel.*','operation.wheel.fix'])) return abort(401);
                    }else{
                        if (! Auth::user()->canAny(['operation.wheel.*','operation.wheel.resolve'])) return abort(401);
                    }
                    $item=WheelTransaction::withoutGlobalScope(CustomerScope::class)->find($id);
                    if( $item->resolved!=1){
                        if($action=="fix" && (
                                $item->status==TransactionStatusEnum::ERROR->value  ||
                                ($item->status==TransactionStatusEnum::INIT->value && isset($item->account_id->value))||
                                ($item->status==TransactionStatusEnum::INIT->value && !isset($item->account_id->value) && Carbon::parse($item->created_at)->addMinutes(1)->isBefore(Carbon::now()) )
                            )){
                            $item->status=TransactionStatusEnum::ERROR->value;
                            $item->resolved=1;
                            $item->resolved_by=auth()->user()->id;
                            $item->save();
                            WheelTransaction::create([
                                "note"=>$request->note,
                                "party_id"=>$item->party_id,
                                "party_name"=>$item->party_name,
                                "user_id"=>auth()->user()->id,
                                "status"=>TransactionStatusEnum::COMPLETED->value
                            ]);
                        }else if($action=="resolve" && (
                            $item->status==TransactionStatusEnum::ERROR->value||
                            $item->status==TransactionStatusEnum::PENDING->value||
                            ($item->status==TransactionStatusEnum::INIT->value && isset($item->account_id->value))
                        )){
                            ProcessUsedWheel::dispatchSync($item);
                            $item=WheelTransaction::withoutGlobalScope(CustomerScope::class)->find($id);
                            if($item->status==TransactionStatusEnum::COMPLETED->value){
                                $item->resolved=1;
                                $item->resolved_by=auth()->user()->id;
                                $item->save();
                            }
                        }
                    }
                    return back();
                }
                break;
        }

    }

}
