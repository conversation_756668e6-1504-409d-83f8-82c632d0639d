<?php

namespace App\Data;

use Spa<PERSON>\LaravelData\Data;

class ManualHarvestResponseData extends Data
{
    public function __construct(
        public string $id,
        public string $trackingCode,
        public string $networkType,
        public string $status,
        public string $statusText,
        public ?AccountIdData $targetAccountId = null,
        public ?CurrencyAmountData $remittanceCurrency = null,
        public ?string $customerNotes = null,
        public ?string $adminNotes = null,
        public ?string $externalReferenceId = null,
        public ?string $processedBy = null,
        public ?string $processedAt = null,
        public ?string $createdAt = null,
    ) {}

    public static function fromModel($manualHarvest): self
    {
        return new self(
            id: (string) $manualHarvest->id,
            trackingCode: $manualHarvest->tracking_code,
            networkType: $manualHarvest->network_type ?? 'Unknown',
            status: (string) $manualHarvest->status,
            statusText: $manualHarvest->status_text,
            targetAccountId: $manualHarvest->target_account_id ? AccountIdData::from($manualHarvest->target_account_id) : null,
            remittanceCurrency: $manualHarvest->remittance_currency ? CurrencyAmountData::from($manualHarvest->remittance_currency) : null,
            customerNotes: $manualHarvest->customer_notes,
            adminNotes: $manualHarvest->admin_notes,
            externalReferenceId: $manualHarvest->external_reference_id,
            processedBy: $manualHarvest->processed_by,
            processedAt: $manualHarvest->processed_at?->format('Y-m-d H:i:s'),
            createdAt: $manualHarvest->created_at?->format('Y-m-d H:i:s'),
        );
    }
}
