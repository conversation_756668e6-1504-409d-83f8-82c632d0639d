<?php

namespace App\Jobs;

use App\Data\Pass\PassCreateRequestData;
use App\Data\ThirdPartyServiceNameData;
use App\Enums\InvoiceTransactionsTypeEnum;
use App\Enums\TransactionStatusEnum;
use App\LogItem;
use App\Models\Gift;
use App\Models\Pass;
use App\Models\PassTransaction;
use App\Scopes\CustomerScope;
use App\Services\FlexService;
use App\Services\NotificationService;
use App\Services\PassService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\Attributes\WithoutRelations;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ProcessUnreceivedWasil implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // $wasils=Pass::withoutGlobalScope(CustomerScope::class)
        // ->with(['transactions'=>function($query){
        //     return $query->where('status',TransactionStatusEnum::COMPLETED->value);
        // }])
        // ->where('status',TransactionStatusEnum::COMPLETED->value)
        // ->where('type', InvoiceTransactionsTypeEnum::Payment->value)
        // ->where('received', 0)
        // ->whereHas('transactions',function($query){
        //     return $query->where('status',TransactionStatusEnum::COMPLETED->value);
        // })
        // ->orderBy("id","ASC");


        // if(env('DB_CONNECTION')=='oracle'){
        //     $wasils=$wasils->whereRaw("created_at<=TO_DATE(?,'YYYY-MM-DD HH24:MI:SS')",[Carbon::now()->subHours(119)->toDateTimeString()]);
        // }else{
        //     $wasils=$wasils->where("created_at",'<=',Carbon::now()->subHours(119)->toDateTimeString());
        // }
        // $wasils=$wasils->get();

        $requestData=PassCreateRequestData::from([
            'reference_id'          =>uniqid()
        ]);

        $result=PassService::expired($requestData);
        $transactions=$result->getAdditionalData()["transactions"];
        if(count($transactions)>0){
            $transactionReferences=collect($transactions)->map(function ($element,int $key){
                return $element->transaction_reference;
            })->filter()->unique()->toArray();

            $wasils=Pass::withoutGlobalScope(CustomerScope::class)
            ->with(['transactions'=>function($query){
                return $query->where('status',TransactionStatusEnum::COMPLETED->value);
            }])
            ->where('status',TransactionStatusEnum::COMPLETED->value)
            ->where('type', InvoiceTransactionsTypeEnum::Payment->value)
            //->where('received', 0)
            ->whereHas('transactions',function($query)use($transactionReferences){
                return $query->where('status',TransactionStatusEnum::COMPLETED->value)->whereNotNull('extra')->whereIn('extra->transaction_reference',$transactionReferences);
            })
            ->orderBy("id","ASC");

            $wasils=$wasils->get();
         //abort(response()->json($wasils,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED));
           // return;
            foreach ($wasils as $wasil) {
                $this->destroy($wasil);
            }
        }
    }


    public function destroy(Pass $pass)
    {

        $valid=false;
        foreach( $pass->transactions as $transaction){
            if($transaction->status==TransactionStatusEnum::COMPLETED->value){
                if($transaction->type==InvoiceTransactionsTypeEnum::Payment->value){
                    $valid=true;
                }
                if($transaction->type==InvoiceTransactionsTypeEnum::Refund->value){
                    $valid=false;
                    break;
                }
            }
        }

        if(!$valid){
            return;
        }

        LogItem::store($pass);

        $transaction=$pass->transactions->where('status',TransactionStatusEnum::COMPLETED->value)
        ->where('type',InvoiceTransactionsTypeEnum::Payment->value)
        ->first();

        $verifyResult=PassService::verify(PassCreateRequestData::from([
            "reference_id"=> uniqid(),
            "external_reference_id"=> $transaction->extra->transaction_reference,
            "row_version" => $transaction->extra->operation_reference??$transaction->extra->transaction_reference,
        ]));

        if($verifyResult->status->message->code!="0" || ($verifyResult->getAdditionalData()['verifyStatus']??"")!=InvoiceTransactionsTypeEnum::Payment->value){
            return;
        }

        $details=json_decode(json_encode([
            "message"    => "Expired",
            "mpt"=>$transaction->extra->mpt
        ]));
        $details->verify=$verifyResult->getAdditionalData()['extra'];

        $passTransaction=PassTransaction::create([
            "pass_id"           =>$pass->id,
            "type"              =>InvoiceTransactionsTypeEnum::Refund->value,
            "status"            =>TransactionStatusEnum::INIT->value,
            "account_id"        =>$transaction->account_id,
            "receiver_mobile"   =>$transaction->receiver_mobile,
            "amount"            =>$transaction->amount,
            "fee"               =>$transaction->fee,
            "reference_id"      =>uniqid(),
            "remarks"           =>$transaction->remarks,
            "extra"=>$details,
        ]);


        $requestData=PassCreateRequestData::from([
            "reference_id"=> $passTransaction->reference_id,
            "external_reference_id"=> $transaction->extra->transaction_reference,
            "row_version" => $transaction->extra->row_version,
            //"operation_reference" => $transaction->extra->operation_reference??$transaction->extra->transaction_reference,
        ]);
        $result=PassService::cancel($requestData);
        if($result->status->message->code=="0"){
            $pass->status=TransactionStatusEnum::COMPLETED->value;
            $pass->type=InvoiceTransactionsTypeEnum::Refund->value;
            $pass->save();

            $extra=$result->getAdditionalData()["extra"];
            if(!isset($transaction->extra->operation_reference)){
                $oldExtra=$transaction->extra;
                $oldExtra->operation_reference=$extra->operation_reference;
                $transaction->extra=$oldExtra;
                $transaction->save();
            }

            $passTransaction->extra= collect($passTransaction->extra)
            ->merge($extra);//$result->getAdditionalData()["extra"];
            $passTransaction->external_reference_id= $passTransaction->extra->financial_reference;
            $passTransaction->status=TransactionStatusEnum::COMPLETED->value;
            $passTransaction->save();

            /** Reverse to user account */
            $paymentResult=$transaction->payment_result;
            $object=new \stdClass();
            $object->reference_id   = $paymentResult->amount->referenceId;
            $object->service_name   =  ThirdPartyServiceNameData::wasil();
            $object->account_id     = $transaction->account_id->value;

            $reverseResult=FlexService::reverseToAccount($object);
            if($reverseResult->status->message->code=="0"){
                $paymentResult->amount->status=InvoiceTransactionsTypeEnum::Reverse->value;
                $passTransaction->payment_result= $paymentResult;
                $passTransaction->save();
            }
            // if(isset($paymentResult->fee)){
            //     $object=new \stdClass();
            //     $object->reference_id   = $paymentResult->fee->referenceId;
            //     $object->service_name   = ThirdPartyServiceNameData::wasilFee();
            //     $object->account_id     = $transaction->account_id->value;

            //     $reverseResult=FlexService::reverseToAccount($object);
            //     if($reverseResult->status->message->code=="0"){
            //         $paymentResult->fee->status=InvoiceTransactionsTypeEnum::Reverse->value;
            //         $passTransaction->payment_result= $paymentResult;
            //         $passTransaction->save();
            //     }
            // }
            NotificationService::sendMessagesToParty([
                [
                    'title'=>__("Cancel remittance"),
                    'body'=>sprintf(__("Successfully cancel remittance #[%s] through [%s] service"),
                        ($transaction->extra?->mpt??""),
                        __("Wasil")
                    ),
                    'type'=>'operation',
                ]
            ],$pass->party_id);
            return;
        // }else if($result->status->message->code=="DIGX_SWITCH_WASIL_TIMEOUT"){
        //     $pass->status=TransactionStatusEnum::PENDING->value;
        //     $pass->type=InvoiceTransactionsTypeEnum::Refund->value;
        //     $pass->save();

        //     $passTransaction->status=TransactionStatusEnum::PENDING->value;
        //     $passTransaction->save();
        }else{
            $pass->status=TransactionStatusEnum::ERROR->value;
            $pass->type=InvoiceTransactionsTypeEnum::Refund->value;
            $pass->save();

            $passTransaction->status=TransactionStatusEnum::ERROR->value;
            $passTransaction->save();
        }
        return;
    }

}
