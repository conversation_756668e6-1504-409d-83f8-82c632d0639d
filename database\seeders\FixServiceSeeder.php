<?php
namespace Database\Seeders;

//use App\Models\User;

use App\Models\CustomerType;
use App\Models\ServiceCustomerType;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class FixServiceSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        $services=\App\Models\Service::select('id','status')->get();
        foreach ($services as $service) {
            ServiceCustomerType::where('service_id',$service->id)->update([
                'status'=>$service->status
            ]);
        }
        $services=ServiceCustomerType::where('customer_type_id',CustomerType::RETAIL)
        // ->whereNotIn('service_id',function($quary){
        //     return $quary->select('service_id')->from('service_customer_types')->where('customer_type_id',$idGuest);
        // })
        ->get();

        $idGuest=CustomerType::create([
            "id"=>CustomerType::GUEST,
            "name" => "Guest",
            "topic" => "guest",
            "status" => 1
        ])->id;

        foreach ($services as $service) {
            $service_pages[]=[
                "service_id"=>$service->service_id,
                "customer_type_id"=>$idGuest,
                "page_service_id"=>$service->page_service_id,
                "status"=>2,
                'status_type'=>'U'
            ];
        }
        if(!empty($service_pages)){
            ServiceCustomerType::insert($service_pages);
        }


        $idUnauthenticated=CustomerType::create([
            "id"=>CustomerType::UNAUTHENTICATED,
            "name" => "Unauthenticated",
            "topic" => "unauthenticated",
            "status" => 1
        ])->id;

        $idBusiness=CustomerType::create([
            "id"=>CustomerType::BUSINESS,
            "name" => "Business",
            "topic" => "business",
            "status" => 1
        ])->id;

        $service_pages=[];
        foreach ($services as $service) {
            $service_pages[]=[
                "service_id"=>$service->service_id,
                "customer_type_id"=>$idUnauthenticated,
                "page_service_id"=>$service->page_service_id,
                "status"=>$service->status,
            ];
            $service_pages[]=[
                "service_id"=>$service->service_id,
                "customer_type_id"=>$idBusiness,
                "page_service_id"=>$service->page_service_id,
                "status"=>$service->status,
            ];
        }
        if(!empty($service_pages)){
            ServiceCustomerType::insert($service_pages);
        }
    }
}
