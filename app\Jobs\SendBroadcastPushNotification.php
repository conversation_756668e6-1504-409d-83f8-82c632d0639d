<?php

namespace App\Jobs;

use App\Models\PartyVerify;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\MessageTarget;
use Kreait\Firebase\Messaging\SendReport;
use App\Models\Notification;
use Laravel\Horizon\Contracts\Silenced;

class SendBroadcastPushNotification implements ShouldQueue, Silenced
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     * @param array $data
     * @param string $topic
     * @return void
     */
    public function __construct(
        public array $data,
        public string $topic
    ){
        //
    }

    /* Get the tags that should be assigned to the job.
    *
    * @return array<int, string>
    */
    public function tags(): array
    {
        $tags=['push','multicast'];
        $tags[]='topic:'.$this->topic??'';
        return $tags;
    }
    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try{

            $to="{$this->topic}_";
            $to.=  env('APP_FLAG', 'banky.commercial')=='banky.islamic'?'islamic_':'';

            $toOld= $to.(env('APP_ENV', 'production')=='production'?'bankylite':'general');
            $toNew= $to.(env('APP_ENV', 'production')=='production'?'commercial':'general');

            $fields = array(
                'topic'  =>$toOld,
                'notification'      => array(
                    'body'  =>  $this->data['body']??'',
                    'title' =>  $this->data['title'],
                    'id'    =>  $this->data['extra_id']??'',
                    'type'  =>  $this->data['type']??'',
                    'color' =>  '#'.env('APP_COLOR'),
                )
            );
            if(isset($data['image'])){
                $fields["notification"]["photo"]=$this->data['image'];
                $fields["notification"]["image"]=$this->data['image'];
            }

            if(isset($this->data['service_id'])){
                $fields["notification"]["service_id"]=$this->data['service_id'];
            }
            if(isset($this->data['url'])){
                $fields["notification"]["url"]=$this->data['url'];
            }

            switch($fields['notification']['type']){
                case 'general':
                    $fields['data']=$fields['notification'];
                break;
                case 'update':
                    $fields['data']=$fields['notification'];
                break;
                default:
                break;
            }
            $message = CloudMessage::fromArray($fields)
            ->withChangedTarget(MessageTarget::CONDITION,"'$toOld' in topics || '$toNew' in topics");

            $messaging = app('firebase.messaging');
            $result=$messaging->send($message);

            if(isset($data['notificationId'])){
                $notification=Notification::find($this->data['notificationId']);
                $notification->result=$result;
                $notification->save();
            }
            return $result;
        }catch(\Exception $e){
           \Log::error("[Notifications]: send");
        }
    }
}
