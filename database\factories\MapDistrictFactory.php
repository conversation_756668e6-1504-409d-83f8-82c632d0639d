<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\MapDistrict;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<\App\Models\MapDistrict>
 */
final class MapDistrictFactory extends Factory
{
    /**
    * The name of the factory's corresponding model.
    *
    * @var string
    */
    protected $model = MapDistrict::class;

    /**
    * Define the model's default state.
    *
    * @return array
    */
    public function definition(): array
    {
        return [
            'city_id' => \App\Models\MapCity::factory(),
        ];
    }
}
