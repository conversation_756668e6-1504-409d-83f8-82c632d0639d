<?php

namespace App\Http\Controllers\Digx\Split;

use App\Data\AccountConfigData;
use App\Data\AccountData;
use App\Data\AccountIdData;
use App\Data\CurrencyAmountData;
use App\Data\GeneralResponseData;
use App\Data\PaymentResultData;
use App\Data\ReceiptData;
use App\Data\ThirdPartyServiceNameData;
use App\Enums\CurrencyTypeEnum;
use App\Enums\InvoiceTransactionsTypeEnum;
use App\Enums\ServiceTagEnum;
use App\Jobs\ProcessSplitPayment;
use App\Models\SplitPaymentTransaction;
use App\Scopes\UsernameScope;
use App\Enums\TransactionStatusEnum;
use App\Helpers\JsonCamel\JsonCamelHelperFacade;
use App\Http\Controllers\Controller;
use App\LogItem;
use App\Scopes\CustomerScope;

use App\Services\NotificationService;
use App\Services\OBDX\CustomerService;
use App\Traits\AuthorizesServices;
use Illuminate\Http\Request;

class RequestController extends Controller
{
    use AuthorizesServices;

    protected function getServiceTags(): array{
        return [
            ServiceTagEnum::SPLIT_PAYMENT
        ];
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */

    public function index(Request $request)
    {
        $validator=validator()->make($request->all(),[
            'from'=>"required|numeric",
            'limit'=>"required|numeric",
            'status'=>"nullable|in:".join(',',TransactionStatusEnum::values()),
            'text'=>"nullable"
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-SPLIT",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_SPLIT_101",
                        "type"    => "ERROR"
                    ]
                ]
            ]),400);

        }

        $items=SplitPaymentTransaction::list(
            from:$request->from??0,
            limit:$request->limit??20,
            filter: $request->text??'',
            status:$request->status??null,
        );
        return JsonCamelHelperFacade::json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])
        ->additional([
            "transactions"=>$items->toArray()
        ])->transform());
    }

    /**
     * Display the specified resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\SplitPaymentTransaction  $transaction
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, SplitPaymentTransaction $transaction)
    {
        $errorResult=GeneralResponseData::from([
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "STORE-SPLIT",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "DIGX_SWITCH_SPLIT_101",
                    "type"    => "ERROR"
                ]
            ]
        ]);

        $validator=validator()->make($request->all(),[
            'debitAccountId.displayValue'=>"required",
            'debitAccountId.value'=>"required|max:20|min:20"
        ]);

        if($validator->fails()){
            $errorResult->status->message->title = join("\n",$validator->errors()->all());
            return response()->json($errorResult,400);

        }
        $transaction->load(['splitPayment'=>function($query){
            return $query->withoutGlobalScope(CustomerScope::class);
        }]);

        if($transaction->splitPayment->status != TransactionStatusEnum::INIT->value || $transaction->splitPayment->type!=InvoiceTransactionsTypeEnum::Payment->value ||
            $transaction->status != TransactionStatusEnum::INIT->value || $transaction->type!=InvoiceTransactionsTypeEnum::Payment->value){
                $errorResult->status->message->title = __("Transaction not found!");
                return response()->json($errorResult,400);
        }

        $creditAccountId=AccountIdData::from($transaction->splitPayment->credit_account_id);
        $valid=false;
        $message="You don't have the correct account!";
        $result=CustomerService::account($request,$request->input("debitAccountId.value"));
        if($result instanceof AccountData){
            $account=$result;
            $valid=$account->status=="ACTIVE" && $account->isCash() && $account->currencyId==$transaction->amount->currency&&
            $account->allowedService(AccountConfigData::splitPayment)&&
            (
                ($account->currencyId==CurrencyTypeEnum::YER->value && $account->isNorth() && !is_null($transaction->splitPayment->bank_code)) ||
                ($account->currencyId==CurrencyTypeEnum::YER->value && $account->isNorth()==$creditAccountId->isNorth()) ||
                $account->currencyId!=CurrencyTypeEnum::YER->value
            );
        }

        if(!$valid){
            $errorResult->status->message->title = __( $message);
            return response()->json($errorResult,400);
        }

        $transaction->debit_account_id=$request->debitAccountId;
        $transaction->save();

        return JsonCamelHelperFacade::json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "INI-SPLIT",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])->additional([
            "paymentId"=>$transaction->id
        ])->transform());
    }

    /**
     * Display the specified resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        $splitPaymentTransaction=SplitPaymentTransaction::show(id:$id);
        if(is_null($splitPaymentTransaction)){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "SHOW-SPLIT",
                    "message"   => [
                        "title"   => __("Transaction not found!"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_SPLIT_101",
                        "type"    => "ERROR"
                    ]
                ]
            ]));

        }

        return JsonCamelHelperFacade::json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])
        ->additional([
            "transferDetails"=>$splitPaymentTransaction,
        ])->transform());
    }
    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\SplitPaymentTransaction  $transaction
     * @return ?\Illuminate\Http\JsonResponse
     */
    public function confirm(Request $request, SplitPaymentTransaction $transaction)
    {
        $errorResult=GeneralResponseData::from([
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "STORE-SPLIT",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "DIGX_SWITCH_SPLIT_101",
                    "type"    => "ERROR"
                ]
            ]
        ]);

        $transaction->load(['splitPayment'=>function($query){
            return $query->withoutGlobalScope(CustomerScope::class)
            ->with(['party'=>function($query){
                return $query->withoutGlobalScope(UsernameScope::class);
            }]);
        }]);

        if($transaction->splitPayment->status != TransactionStatusEnum::INIT->value || $transaction->splitPayment->type!=InvoiceTransactionsTypeEnum::Payment->value ||
        $transaction->status != TransactionStatusEnum::INIT->value || $transaction->type!=InvoiceTransactionsTypeEnum::Payment->value){
            $errorResult->status->message->title = __("Transaction not found!");
            return response()->json($errorResult,400);
        }

        LogItem::store($transaction->splitPayment);

        $object = new \stdClass();
        $object->journals= [
            "genericPayee" =>[
                "nickName" =>'',
                "accountName" =>'temp',
                "accountNumber" => is_null($transaction->splitPayment->bank_code)?
                    $transaction->splitPayment->credit_account_id->value:
                    ThirdPartyServiceNameData::split(),
                "transferMode"=> 'ACC'
            ],
            "genericPayout" =>[
                "amount" =>CurrencyAmountData::from([
                    "amount"=>$transaction->amount->amount+($transaction->fee?->amount??0),
                    "currency"=>$transaction->amount->currency
                ])->toArray(),
                "purpose" =>'FAML',
                "purposeText" => null,
                "debitAccountId" => $transaction->debit_account_id,
                "remarks"=>sprintf(trans("Split payment for [%s]"),
                $transaction->splitPayment->remarks
                ),
            ],
            "paymentType" => 'INTERNALFT'
        ];

        $result=CustomerService::internalTransefer($object);
        if ($result->status->message->code == "0") {
            $transaction->status=TransactionStatusEnum::COMPLETED->value;
            $transaction->type=InvoiceTransactionsTypeEnum::Payment->value;
            $payment =new PaymentResultData(
                $result->getAdditionalData()["referenceId"],
                $result->getAdditionalData()["externalReferenceId"],
                InvoiceTransactionsTypeEnum::Payment->value
            );
            $transaction->payment_result=[
                "payment"=>$payment
            ];
            $transaction->save();
            NotificationService::sendMessagesToParty([
                [
                    'title'=>__("Split payment"),
                    'body'=>sprintf(__("Your friend [%s] complete the payment!"),
                    auth()->user()->name
                    ),
                    'type'=>'operation',
                ]
            ],$transaction->splitPayment->party_id);

            ProcessSplitPayment::dispatch($transaction->splitPayment)->onQueue('critical');

            return response()->json(
                GeneralResponseData::from($result->toArray())
                ->additional([
                    'externalReferenceId'=>$payment->externalReferenceId,
                    'receipt'=> $this->getReceiptData($transaction)
                ])
            );
        }
        return response()->json($result);
    }

    public function receipt(Request $request,SplitPaymentTransaction $transaction)
    {
        $validator=validator()->make($request->all(),[
            'showAccountInfo' => 'Nullable|numeric|in:0,1',
            'showAccountNumber' => 'Nullable|numeric|in:0,1',
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "INDEX-TRANSFER-FUND",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_TRANSFER_001",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }
        $this->generateReceipt($this->getReceiptData($transaction));
    }
    protected function getReceiptData(SplitPaymentTransaction $transaction): ReceiptData
    {
        // Check if 'posts' relationship is loaded
        if (!$transaction->relationLoaded('splitPayment')) {
            $transaction->load(['splitPayment'=>function($query){
                return $query->withoutGlobalScope(CustomerScope::class)
                ->with(['party'=>function($query){
                    return $query->withoutGlobalScope(UsernameScope::class);
                }]);
            }]);
        }
        $splitPayment=$transaction->splitPayment;
        if(is_null($splitPayment->bank_code)){
            if (!$splitPayment->relationLoaded('party')) {
                $splitPayment->load(['party'=>function($query){
                    return $query->withoutGlobalScope(UsernameScope::class);
                }]);
            }
            $beneficiary=$splitPayment->party->name;
        }else{
            $beneficiary=$splitPayment->credit_account_id->displayValue;
        }
        $beneficiary.="\n{$splitPayment->credit_account_id->value}";
        return ReceiptData::from([
            "id"=> $transaction->id,
            "date"=> date_format(date_create($transaction->created_at), "Y-m-d H:i:s"),
            "title"=> __("Split payment"),
            "beneficiary"=> $beneficiary,
            "statement"=> "",
            "details"=> [
                "referenceId"=> $transaction->payment_result?->payment?->externalReferenceId??"",
                "debitAccountId"=> $transaction->debit_account_id??null,
                "remarks"=>$splitPayment->remarks,
                "amount"=>$transaction->amount,
                "fee"=>$transaction->fee,
            ]
        ]);
    }
}
