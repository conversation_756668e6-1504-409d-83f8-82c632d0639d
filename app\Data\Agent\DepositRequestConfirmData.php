<?php

namespace App\Data\Agent;

use App\Data\AccountIdData;
use App\Data\CurrencyAmountData;
use Spa<PERSON>\LaravelData\Attributes\Validation\Nullable;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Data;

class DepositRequestConfirmData extends DepositRequestBaseData
{
   #[Nullable]
    public ?CurrencyAmountData $fee;

    #[Nullable]
    public ?string $firstName;
    #[Nullable]
    public ?string $secondName;
    #[Nullable]
    public ?string $thirdName;
    #[Nullable]
    public ?string $lastName;

    public function fullName():string {
       return "{$this->firstName} {$this->secondName} {$this->thirdName} {$this->lastName}";
    }

   // public static function ()
}