<?php

namespace App\Http\Middleware;

use Closure;
use Symfony\Component\HttpFoundation\Cookie;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Contracts\Cookie\QueueingFactory as CookieJar;

class DecodeQueuedCookies{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string[]  ...$guards
     * @return mixed
     *
     * @throws \Illuminate\Auth\AuthenticationException
     */
    public function handle($request, Closure $next, ...$guards)
    {
        $response=$next($request);
        return $this->fixCookies($response);
    }

    /**
     * Encrypt the cookies on an outgoing response.
     *
     * @param  \Symfony\Component\HttpFoundation\Response  $response
     * @return \Symfony\Component\HttpFoundation\Response
     */
    protected function fixCookies(Response $response)
    {
        try {
            foreach ($response->headers->getCookies() as $cookie) {
                setrawcookie($cookie->getName(), preg_replace('/[\s,;]+/', '', $cookie->getValue()),0, "/",'',false,true);
            }
            $response->headers->remove('Set-Cookie');
            //\Log::critical(json_encode($credentials));
        } catch (\Exception $e) {

        }
        //return $response;

        return $response;
    }

}
