<?php

namespace App\Http\Controllers\Digx;
use App\Data\AccountData;
use App\Data\AccountIdData;
use App\Data\AppConfigData;
use App\Data\CurrencyAmountData;
use App\Data\GeneralResponseData;
use App\Data\ServiceItemData;
use App\Data\StatusData;
use App\Enums\CurrencyTypeEnum;
use App\Enums\LimitTypeEnum;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Digx\Business\OilPaymentController;
use App\Http\Controllers\Digx\Gift\SendController;
use App\Http\Controllers\Digx\Gold\GoldController;
use App\LogItem;
use App\Models\AppVersionHistory;
use App\Models\CustomerType;
use App\Models\ExchangeRate;
use App\Models\News;
use App\Models\OpenAccount;
use App\Models\Party;
use App\Models\Story;
use App\Scopes\CustomerScope;
use App\Scopes\UsernameScope;
use App\Services\AnaService;
use App\Services\FlexService;
use App\Services\LoanService;
use App\Services\LoyaltyService;
use App\Services\OBDX\AdminService;
use App\Services\OBDX\CustomerService;
use App\Services\VirtualCardService;
use App\Traits\NotificationCount;
use Crypt;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use App\Models\Bundle;
use App\Services\OBDX\LoginService;
use App\Services\OBDX\UtilsService;
use Spatie\LaravelData\DataCollection;
use Symfony\Component\HttpFoundation\Cookie;
use Symfony\Component\HttpFoundation\Response;

class RestApiController extends Controller
{
    use NotificationCount;

    public function privacy(Request $request)
    {
        $view=\File::get(base_path( "/resources/mocks/utils/privacy.html"));
        if(!(($request->header('appVersion')??0)>181)){
            $view.='
            <script type="text/javascript">
            window.onscroll = function(ev) {
                if ((window.innerHeight + window.scrollY) >= document.body.offsetHeight) {
                    window.FLUTTER_CHANNEL.postMessage("end of scroll");
                }
            };
            </script>
        ';
        }
        return \Blade::render($view);

    }
     /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response|null
     */
    public function login()
    {
        return LoginService::login();
    }
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function resendOtp($referenceNo)
    {
        return LoginService::resend($referenceNo);
    }

    public function news(Request $request)
    {
        $user=auth()->user();

        $news= News::select( "id",
            "title as name",
            "description",
            "image as icon",
            "url",
            "source_name",
            "source_icon",
            'created_at as date'
        )->where('status',1)
        ->where('type',$request->type)
        ->skip( $request->from??0)
        ->take($request->limit??20)
        ->orderBy('created_at','DESC')
        ->get();

        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "INDEX-GOLD",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])->additional([
            'news'=>$news
        ]));
    }
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function exchangeRates()
    {
        $exchangeRates=ExchangeRate::
            select("area")
            ->with(["rates"=>function($query){
                return $query->select("area","type","currency1",'currency2','buy_rate','sell_rate','buy_difference','sell_difference')
                ->whereRaw("created_at=(select max(created_AT) from DIGX_LITE_EXCHANGE_RATES d
                    where d.currency1 = DIGX_LITE_EXCHANGE_RATES.currency1 and d.area=DIGX_LITE_EXCHANGE_RATES.area)")
                ->groupBy("area")
                ->groupBy("type")
                ->groupBy("currency1")
                ->groupBy("currency2")
                ->groupBy("buy_rate")
                ->groupBy("sell_rate")
                ->groupBy("buy_difference")
                ->groupBy("sell_difference")
                ->orderBy('currency1');
            }])
            // where("created_AT",function($query){
            //     $query;
            // })
            // whereRaw("created_at=(select max(created_AT) from DIGX_LITE_EXCHANGE_RATES d
            //     where d.currency1 = DIGX_LITE_EXCHANGE_RATES.currency1 and d.branch=DIGX_LITE_EXCHANGE_RATES.branch)")
            ->whereNotNull('area')
            ->groupBy("area")
            ->get();
        return response()->json(GeneralResponseData::from(array(
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ))->additional([
            'exchangeRates'=>$exchangeRates
        ]));
    }
    public function getBundles(Request $request,$result_type='json'){
        $response = Http::
        fake([
            "https://ubp.yk-bank.com:3443/api/tp/v1/catalog?usr=banlyconv" => Http::response(Bundle::fakeBundles, 200, [])
        ])
            ->get("https://ubp.yk-bank.com:3443/api/tp/v1/catalog",[
                "usr"=>"banlyconv"
            ]);
        return response()->json($response->object());
    }

    public function home(Request $request,$result_type='json'){
        if(auth('digx')->check()){
            auth()->shouldUse('digx');
            $customer=CustomerService::details($request);

            $user=$request->user();

            $userSignature=$user->id;
            if(isset($user->customerRole) &&  in_array($user->customerRole??'',CustomerType::getCorporateRoles())){
                $userSignature.=':'.$user->username;
            }
            $customer->userProfile->userSignature= Crypt::encrypt($userSignature);

            $party=Party::withGlobalScope('CustomerScope',new CustomerScope)
            ->withGlobalScope('UsernameScope',new UsernameScope)
            ->first();
            if(!is_null($party) ){
                $customer->userProfile->image= $party->image;
            }else{
                $party=[
                    "party_id"=>$user->id,
                    "name"=>$user->name,
                ];
                if(str_contains($userSignature,':')){
                    $party["username"]=$user->username;
                }
                $party =Party::create(
                    $party
                );
            }
            $customer->userProfile->offline= $party->offline;

        }else{
            $customer=new \stdClass();
            $customer->status=StatusData::from([
                "result"    => "Success",
                "contextID" => "",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => ""
                ]
            ]);
            $customer->userProfile=new \stdClass();
            $customer->userProfile->customerType= CustomerType::UNAUTHENTICATED;
        }

        $customer->config=app(\App\Settings\ConfigSettings::class)->appConfig
        ->toApp($customer->userProfile->customerType,$request->header('appVersion'),$customer->userProfile->customerRole??'default');

        if($request->filled('cacheSignature') && ($request->cacheSignature??'-1')!=$customer->config->cacheSignature){
            $services=\App\Models\Service::with(['items'=>function($query) use($customer,$request){
                $query->with(['serviceCustomerType'=>function($query) use($customer){
                    $query->select('customer_type_id','service_id','service_customer_types.status','status_type')
                    ->where('customer_type_id',$customer->userProfile->customerType)
                    ->whereNull('page_service_id');
                    if(isset($customer->userProfile->customerRole)){
                        $query->whereHas('customerRole', function ($query) use($customer) {
                            return $query->where('role_identifer',$customer->userProfile->customerRole);
                        });
                    }
                    if(env('APP_ENV', 'production')=='production'){
                        $query->where('service_customer_types.status','<>',0);
                    }
                    return $query;
                }])
                ->whereNotNull('service_id');
                // if(env('APP_ENV', 'production')=='production'){
                //     $query->where('status','<>',0);
                // }

                // $query->whereHas('customerTypes', function ($query) use($customer) {
                //     return $query->where('id',$customer->userProfile->customerType)->whereNull('page_service_id');
                // });
                if(($request->header('appVersion')??0)<148){
                    $query->where(function ($query)  {
                        return $query->where('app_version', '<>',148)
                            ->orWhereNull('app_version');
                    });
                }
                return $query->orderBy('sort','asc');
            }])
            ->with(['serviceCustomerType'=>function($query) use($customer){
                $query->select('customer_type_id','service_id','service_customer_types.status','status_type')
                ->where('customer_type_id',$customer->userProfile->customerType)
                ->whereNull('page_service_id');

                if(isset($customer->userProfile->customerRole)){
                    $query->whereHas('customerRole', function ($query) use($customer) {
                        return $query->where('role_identifer',$customer->userProfile->customerRole);
                    });
                }

                if(env('APP_ENV', 'production')=='production'){
                    $query->where('service_customer_types.status','<>',0);
                }
                return $query;
            }])
            ->whereNull('service_id')

            // ->whereHas('customerTypes', function ($query) use($customer) {
            //     return $query->where('id',$customer->userProfile->customerType)->whereNull('page_service_id');
            // })
            ->orderBy('sort','asc');
            // if(env('APP_ENV', 'production')=='production'){
            //     $services= $services->where('status','<>',0);
            // }
            if(($request->header('appVersion')??0)<148){
                $services= $services->where(function ($query)  {
                    return $query->where('app_version', '<>',148)
                        ->orWhereNull('app_version');
                });
            }
            $customer->services=$services->get();
            $customer->services=ServiceItemData::get(ServiceItemData::collect($customer->services->toArray(),DataCollection::class));
            //->whereNotNull('status')->toArray();

            // if(($request->header('appVersion')??0)<148){

            // }

            if($customer->config->cacheSignature==''){
                $appVersion=$customer->config->appVersion;
                $customerType=$customer->userProfile->customerType;
                $customerRole=$customer->userProfile->customerRole??'default';
                //Log::info($appVersion);
                //Log::info($request->header('appVersion'));

                $cacheSignature=md5(json_encode($customer->services));
                $settings=app(\App\Settings\ConfigSettings::class);
                $appConfig=$settings->appConfig;
                if(!isset($appConfig->cacheSignature["$customerType"]) || !is_array($appConfig->cacheSignature["$customerType"])){
                    $appConfig->cacheSignature["$customerType"]=AppConfigData::defaultCacheSignature["$customerType"];
                }

                if(is_array($appConfig->cacheSignature["$customerType"]["$appVersion"])){
                    $appConfig->cacheSignature["$customerType"]["$appVersion"]["$customerRole"]=$cacheSignature;
                }else{
                    $appConfig->cacheSignature["$customerType"]["$appVersion"]=[
                        "$customerRole"=>$cacheSignature
                    ];
                }
                $settings->appConfig=$appConfig;
                $settings->save();

                $appConfig=$customer->config;
                $appConfig->cacheSignature=$cacheSignature;
                $customer->config=$appConfig;
            }


        }

        $areas=['lowAreas','aden'];
        if(auth('digx')->check()){
            $result=CustomerService::accounts($request);

            if(!($result instanceof GeneralResponseData)){
                $accounts=$result;
                $customer->accounts=$accounts;

                $areaBranches=app(\App\Settings\ConfigSettings::class)->exchangeAreaConfig->areaBranches;
                $areas=[];

                $haveNorthAccount=false;
                $account=$accounts
                //->where('status',"ACTIVE")
                ->filter(function (AccountData $element,int $key) use($areaBranches,&$areas, &$haveNorthAccount){
                    $area[]=collect($areaBranches)->filter(function ($branches,string $key) use($element) {
                        return in_array($element->branchId,$branches);
                    })->keys()->first();
                    if(!is_null($area)){
                        $areas[]= $area;
                    }
                    if($element->status=="ACTIVE" && $element->isNorth() && $element->isCash()){
                        $haveNorthAccount=true;
                    }
                    // \Log::info("creditCard:".$element->id->value." - ".$element->isCreditCard());
                    //only creditCard accounts.
                    return $element->status=="ACTIVE" && $element->isCreditCard();
                })->first();

                if(count($accounts)){
                    app(\App\Settings\WheelSettings::class)->wheelConfig
                    ->toApp($customer->config,$haveNorthAccount);
                }

                if(($request->header('appVersion')??0)<175 && !is_null($account)){
                    $customer->creditCards=$this->creditCards($account->id->value,$request,"array");
                }

                $account=$accounts
                    ->filter(function (AccountData $element,int $key) use($request) {
                        return $element->id->value==$request->account;
                    })->first();
                if(!is_null($account)){
                    $trans=CustomerService::history($request,$account->id->value);
                    if(!($trans instanceof GeneralResponseData)){
                        $customer->items=$trans;
                    }

                }

                if(($request->header('appVersion')??0)>159){
                    $customer->notification=$this->getNotificationCount();
                }

            }

            if(($request->header('appVersion')??0)<175){
                $customer->virtualCards=$this->virtualCards($request,"array");
            }

            $originalCookie = Cookie::fromString('JSESSIONID='.$request->cookie("JSESSIONID"));
        }


        $customer->exchangeRates= ExchangeRate::getAppHomeExchangeRate($areas);


        if(($request->header('appVersion')??0)>182){
            $customer->appVersionNewUpdates= AppVersionHistory::getNewUpdatesAvailable();
            $customer->appVersionHistories= AppVersionHistory::getWhatsNew();
            $customer->story= Story::getAvailableStory();
        }

        $response= response()->json($customer,200);
        if(isset($originalCookie)){
            $response=$response->withCookie($originalCookie);
        }

        // if(auth()->user()->id=='0183415'){
        //     return response()->json($customer,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED)->withCookie($originalCookie);
        // }
        return $response;
    }
    public function subHome(Request $request,$id){
        $customerType=CustomerType::UNAUTHENTICATED;
        if(auth('digx')->check()){
            auth()->shouldUse('digx');
            $customerType=$request->user()->customerType;
            // if(isset($request->user()->role)){
            //     $customerRole=$request->user()->role;
            // }

        }

        $services=[];
        if(!is_null($id) && $id!="null" && $id!="NULL"){
            if($request->more??0){
                $services=\App\Models\Service::with(['items'=>function($query) use($customerType,$id){
                    return $query->with(['serviceCustomerType'=>function($query) use($customerType,$id){
                        $query->select('customer_type_id','service_id','service_customer_types.status','status_type')
                        ->where('customer_type_id',$customerType)
                        ->whereNull('page_service_id');

                        if(isset(auth()->user()->customerRole)){
                            $query->whereHas('customerRole', function ($query)  {
                                return $query->where('role_identifer',auth()->user()->customerRole);
                            });
                        }

                        if(env('APP_ENV', 'production')=='production'){
                            $query->where('service_customer_types.status','<>',0);
                        }
                        return $query;
                    }])
                    ->whereNotNull('service_id')
                    ->orderBy('sort','asc');
                }])
                ->with(['serviceCustomerType'=>function($query) use($customerType,$id){
                    $query->select('customer_type_id','service_id','service_customer_types.status','status_type')
                    ->where('customer_type_id',$customerType)
                    ->whereNull('page_service_id');

                    if(isset(auth()->user()->customerRole)){
                        $query->whereHas('customerRole', function ($query)  {
                            return $query->where('role_identifer',auth()->user()->customerRole);
                        });
                    }

                    if(env('APP_ENV', 'production')=='production'){
                        $query->where('service_customer_types.status','<>',0);
                    }
                    return $query;
                }])
                // ->where('service_id',$id)
                // ->whereNull('type','item')
                ->whereNull('service_id')
                ->where('type','section')
                ->where('id',$id)
                ->orderBy('sort','asc');
            }else{
                $services=\App\Models\Service::with(['items'=>function($query) use($customerType,$id){
                    return $query->with(['serviceCustomerType'=>function($query) use($customerType,$id){
                       $query->select('customer_type_id','service_id','service_customer_types.status','status_type')
                        ->where('customer_type_id',$customerType)
                        ->where('page_service_id',$id);

                        if(isset(auth()->user()->customerRole)){
                            $query->whereHas('customerRole', function ($query)  {
                                return $query->where('role_identifer',auth()->user()->customerRole);
                            });
                        }

                        if(env('APP_ENV', 'production')=='production'){
                            $query->where('service_customer_types.status','<>',0);
                        }
                        return $query;
                    }])
                    ->whereNotNull('service_id')
                    ->orderBy('sort','asc');
                }])
                ->with(['serviceCustomerType'=>function($query) use($customerType,$id){
                    $query->select('customer_type_id','service_id','service_customer_types.status','status_type')
                    ->where('customer_type_id',$customerType)
                    ->where('page_service_id',$id);

                    if(isset(auth()->user()->customerRole)){
                        $query->whereHas('customerRole', function ($query)  {
                            return $query->where('role_identifer',auth()->user()->customerRole);
                        });
                    }

                    if(env('APP_ENV', 'production')=='production'){
                        $query->where('service_customer_types.status','<>',0);
                    }
                    return $query;
                }])
                ->whereNull('service_id')
                ->orderBy('sort','asc');
            }
            $services=$services->get();
            $services=ServiceItemData::get(ServiceItemData::collect($services->toArray(),DataCollection::class));
        }



        return response()->json(GeneralResponseData::from(array(
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ))->additional([
            // 'offers'=>GeneralItemData::collect([
            //     GeneralItemData::from([
            //         "id"=>"1",
            //         "name"  =>[
            //             "ar"=>"عروض علاء الدين",
            //             "en"=>"عروض علاء الدين",
            //         ],
            //         "icon"=>"https://img.freepik.com/free-vector/magical-golden-text-effect-editable-fairy-tale-text-style_314614-384.jpg",
            //     ])
            // ]),
            'services'=>$services,
        ]));
    }
    public function me(Request $request,$resultType='json'){
        $customer=CustomerService::details($request);
        //return $this->verifyUser($request,$customer);
        return response()->json($customer);
    }

    public function changePassword(Request $request,$resultType='json'){
        return LoginService::changePassword();
    }
    public function changeCredentials(Request $request,$resultType='json'){
        return LoginService::changeCredentials();
    }
    public function virtualCards(Request $request,$resultType='json'){
        $user=$request->user()->userProfile;
        $object=new \stdClass();
        $object->partyId=$user->partyId->value;
        $object->phone=$request->user()->phone;

        $result=VirtualCardService::getCards($object);
        if($resultType=="array"){
            return isset($result->getAdditionalData()["virtualCards"])?
                $result->getAdditionalData()["virtualCards"]:[];
        }
        return response()->json($result);
    }
    protected function creditCards(string $account,Request $request,$resultType='json'){
        $user=$request->user()->userProfile;
        $accoutId=AccountIdData::from(["value"=>$account]);
        // if($user->partyId->value!=$accoutId->partyId()){
        //     return response()->json(GeneralResponseData::from([
        //         'status'=>[
        //             "result"    => "ERROR",
        //             "contextID" => "CREDIT-CARD",
        //             "message"   => [
        //                 "title"   => __("You don't have a permission!"),
        //                 "detail"  => "",
        //                 "code"    => "DIGX_SWITCH_CARDLESS_CREDITCARD_101",
        //                 "type"    => "ERROR"
        //             ]
        //          ]
        //     ]));
        // }
        $object=new \stdClass();
        $object->account_id= $accoutId->value;
        $result=FlexService::getCustomerCreditCardInfo($object);
        if($resultType=="array"){
            return isset($result->getAdditionalData()["creditCards"])?
                $result->getAdditionalData()["creditCards"]:[];
        }
        return response()->json($result);
    }
    public function openAccountCurrency(string $account,Request $request){
        $validator=validator()->make($request->all(),[
            'currency'=>"required|max:3|min:3|in:USD,YER,SAR,EUR,21G,24G",
            'product'=>"nullable|max:4|min:4|in:PNPL"
        ]);
        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "ACTIVATE-ACCOUNT-CURRENCY",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_OPEN_CURRENCY_ACCOUNT_001",
                        "type"    => "ERROR"
                    ]
                ]
            ]));
        }

        $user=$request->user()->userProfile;
        $accoutId=AccountIdData::from(["value"=>$account]);

        $valid=false;
        $message="Can't load your accounts!";
        $result=CustomerService::accounts($request);
        if(!($result instanceof GeneralResponseData)){
            $accounts=$result;
            $account=$accounts
                ->filter(function (AccountData $element,int $key) use($accoutId){
                    //only creditCard accounts.
                    return /*$element->status=="ACTIVE" &&*/ $element->isCash()
                        && $element->id->value==$accoutId->value /*&& $element->productId()=="SDCA"*/;
                })->first();
            if(is_null($account)){
                $message="You don't have a permission!";
            }else{
                if( in_array($request->currency,[CurrencyTypeEnum::G21->value,CurrencyTypeEnum::G24->value])){
                    $accountGold=$accounts
                    ->filter(function (AccountData $element,int $key) use($request){
                        //only creditCard accounts.
                        return $element->currencyId==$request->currency;
                    })->first();
                    if(!is_null($accountGold)){
                        $message="You already have this currency account!";
                    }else{
                        $valid=true;
                    }
                }else{
                    if(!in_array($request->currency,$account->openCurrencies)){
                        $message="You already have this currency account!";
                    }else{
                        $valid=true;
                    }
                }
            }
        }
        if(!$valid){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "ACTIVATE-ACCOUNT-CURRENCY",
                    "message"   => [
                        "title"   => __($message),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_OPEN_CURRENCY_ACCOUNT_002",
                        "type"    => "ERROR"
                    ]
                ]
            ]));
        }

        // if( in_array($request->currency,[\App\Enums\CurrencyTypeEnum::G21->value,\App\Enums\CurrencyTypeEnum::G24->value]) && $account->currencyId!="YER"){
        //     $object=new \stdClass();
        //     $object->referenceId=round(microtime(true)*1000);
        //     $object->partyId=$user->partyId->value;
        //     $object->branchId=$account->branchId;
        //     $object->currencyId="YER";
        //     $object->productId=$account->allowedProductsToOpen()[0];
        //     $object->accountId= $account->id->value;

        //     $result=FlexService::activateNewCurrencyAccount($object);
        //     if($result->status->message->code!="0"){
        //         return response()->json($result);
        //     }
        // }


        $products=$account->allowedProductsToOpen($request->currency);
        if(!empty($products) && $request->filled("product") && in_array($request->product,$products)){
            $product=$request->product;
        }else if(count($products)==1 && !$request->filled("product")){
            $product=collect($products)->first();
        }else{
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "ACTIVATE-ACCOUNT-CURRENCY",
                    "message"   => [
                        "title"   => sprintf(__("You can't open this type of currency from this account class!"),$request->currency,$account->id->value),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_OPEN_CURRENCY_ACCOUNT_004",
                        "type"    => "ERROR"
                    ]
                ]
            ]));
        }

        $object=new \stdClass();
        $object->referenceId=round(microtime(true)*1000);
        $object->partyId=$user->partyId->value;
        $object->branchId=$account->branchId;
        $object->currencyId=$request->currency;
        $object->productId=$product;
        $object->accountId= $account->id->value;

        $openAccount=OpenAccount::where('party_id',$user->partyId->value)
        ->where('branch_id',$object->branchId)
        ->where('currency_id',$object->currencyId)
        ->where('product_id',$object->productId)
        ->where('status','<>',0)
        ->first();

        if(!is_null($openAccount) && $user->partyId->value!="0183415"){
            LogItem::store($openAccount);

            $openAccount->status=-1;
            $openAccount->save();

            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "ACTIVATE-ACCOUNT-CURRENCY",
                    "message"   => [
                        "title"   => sprintf(__("You already have account with this currency, please contact customer services to activate it. the account number is"),$openAccount->opened_account_id),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_OPEN_CURRENCY_ACCOUNT_005",
                        "type"    => "ERROR"
                    ]
                ]
            ]));
        }

        $openAccount=OpenAccount::create([
            'party_id'      =>$object->partyId,
            "reference_id"  =>$object->referenceId,
            'branch_id'=>$object->branchId,
            'currency_id'=>$object->currencyId,
            'product_id'=>$object->productId,
            'account_id'=>$object->accountId
        ]);
        LogItem::store($openAccount);

        $result=FlexService::activateNewCurrencyAccount($object);
        if($result->status->message->code=="0"){
            $openAccount->opened_account_id= $result->getAdditionalData()["openedAccountId"];
            $openAccount->status=1;
            $openAccount->save();
            if($product=="PNPL"){
                return $this->requestPNPLAccount($account);
            }
            return response()->json($result);
        }
        return response()->json($result,Response::HTTP_NOT_IMPLEMENTED);

    }
    protected function requestPNPLAccount(AccountData $account){
        //$result=$this->openAccountCurrency($account,$request,"array");
        //if($result->status->message->code=="0"){
            $user= auth()->user();
            //$accoutId=AccountIdData::from(["value"=>$account]);

            // $object=new \stdClass();
            // $object->partyId=$user->id;
            // $result =LoanService::create($object);
            // if(isset($result->getAdditionalData()["activities"])){
            //     $activity= collect($result->getAdditionalData()["activities"])
            //     ->where("activity_id",*********)
            //     ->first();
            //     $sector= collect($result->getAdditionalData()["sectors"])
            //     ->where("sector_code","COM")
            //     ->first();
            //     $product= collect($result->getAdditionalData()["products"])
            //     ->where("product_code","PNPL")
            //     ->first();
                $pnplConfig=app(\App\Settings\ConfigSettings::class)->pnplConfig;
                $object=new \stdClass();
                $object->customer_no = $user->id;
                $object->product_code = $pnplConfig->product;
                $object->amount = $pnplConfig->amount->amount;
                $object->currency = $pnplConfig->amount->currency;
                $object->sector_code = $pnplConfig->sector;
                $object->activity_id = $pnplConfig->activity;
                $object->activity_name = $user->name;
                $object->activity_phone = $user->phone;
                $object->activity_address = html_entity_decode($user->userProfile?->address?->line1??"");
                $object->closest_branch_id = $account->branchId;
                $object->payment_reason = "";

                $result =LoanService::request($object);
                if(in_array($result->status->message->code,["0"])){
                    $result->status->message->title=__("Buy now pay later account has been successfully opened and your request to feed the account has been sent. You will be notified when your request is approved.");
                    return response()->json($result);
                }
                if(in_array($result->status->message->code,["33","31"])){
                    return response()->json($result);
                }
                return response()->json($result,Response::HTTP_NOT_IMPLEMENTED);
            // }
            // return response()->json($result,Response::HTTP_NOT_IMPLEMENTED);


        //}

        //return response()->json($result,Response::HTTP_NOT_IMPLEMENTED);

    }
    public function activateDormantAccount(string $account,Request $request){
        $user=$request->user()->userProfile;
        $accoutId=AccountIdData::from(["value"=>$account]);

        $valid=false;
        $message="Can't load your accounts!";
        $appConfig=app(\App\Settings\ConfigSettings::class)->appConfig;
        switch($appConfig->dormantActivateType){
            case 'none':
                $message="The activation process not available right now!";
                break;
            case 'local':
                $valid=true;
                break;
            case 'ana':
                if(!$request->filled("authId")){
                    $message="The activation process missing some required params, please check your params & try again!";
                }else{
                    $valid=true;
                }
                break;
        }

        if($valid){
            $valid=false;
            $message="Can't load your accounts!";
            $result=CustomerService::accounts($request);
            if(!($result instanceof GeneralResponseData)){
                $accounts=$result;
                $account=$accounts
                    ->filter(function (AccountData $element,int $key) use($accoutId){
                        return $element->status=="DORMANT" && ($element->isCash() || $element->isGold())
                            && $element->id->value==$accoutId->value /*&& $element->productId()=="SDCA"*/;
                    })->first();

                if(is_null($account)){
                    $message="You don't have a permission!";
                }else{
                    // if( $account->balance->amount>10000){
                    //     $message=__("You can't activate account that have amount more than:").' 10000';
                    // }else{
                        $valid=true;
                    //}
                }
            }
        }

        if(!$valid){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "ACTIVATE-DORMANT-ACCOUNT",
                    "message"   => [
                        "title"   => __($message),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_ACTIVATE_DORMANT_ACCOUNT_001",
                        "type"    => "ERROR"
                    ]
                ]
            ]));
        }

        $object=new \stdClass();
        $object->referenceId=round(microtime(true)*1000);
        $object->authId= $request->authId??null;

        switch($appConfig->dormantActivateType){
            case 'ana':
                $object->authId= $request->authId??null;
                $result=AnaService::updateKYC($object);
                if($result->status->message->code!="0"){
                    return response()->json(GeneralResponseData::from($result->toArray()),Response::HTTP_NOT_IMPLEMENTED);
                }
                break;
        }
        $object->accountId= $account->id->value;

        $result=FlexService::activateDormantAccount($object);
        if($result->status->message->code=="0"){
            return response()->json($result);
        }
        return response()->json($result,Response::HTTP_NOT_IMPLEMENTED);
    }
    public function exchangeRate(Request $request,$resultType='json'){
        $validator=validator()->make($request->all(),[
            'debitAccountId.value'=>"required|max:20|min:20",
            'creditAccountId.value'=>"required|max:20|min:20",
            'amount.amount'=>"required|numeric",
            'amount.currency'=>"required"
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
               'status'=>[
                    "result"    => "SUCCESSFUL",
                    "contextID" => "INDEX-GOLD",
                    "message"   => [
                        "title"   => "",
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_GOLD_HOME_100",
                        "type"    => "INFO"
                    ]
                ]
            ]));
        }

        $debitAccountId=AccountIdData::from($request->input("debitAccountId"));
        $creditAccountId=AccountIdData::from($request->input("creditAccountId"));
        $amount=CurrencyAmountData::from($request->input("amount"));

        $exchangeRate= UtilsService::exchange(
            $amount,
            $debitAccountId,
            $creditAccountId
        );
        return response()->json($exchangeRate);
    }

    public function limits(Request $request,$resultType='json'){
        $limits=[];
        $trxLimit=[];
        $limitType=LimitTypeEnum::find($request->type);
        if(!is_null( $limitType)){
            GoldController::limits($limitType);
            SendController::limits($limitType);
            \App\Http\Controllers\Digx\Pass\SendController::limits($limitType);
            OilPaymentController::limits($limitType);

            $assignedLimits=CustomerService::assignedLimits($limitType);
            if(!is_null($assignedLimits) && isset($assignedLimits->limits)){
                foreach ($assignedLimits->limits as $item) {
                    if(isset($item->periodicity)){
                        $limitUtilization=CustomerService::limitUtilization("{$item->limitType}#{$item->periodicity}",$limitType);
                        $limits[]=[
                            "period"=>$item->periodicity,
                            "maxAmount"=>$item->maxAmount,
                            "maxCount"=>$item->maxCount,
                            "amount"=>$limitUtilization->amount??CurrencyAmountData::from([
                                "amount"=>0,
                                "currency"=>CurrencyTypeEnum::YER->value,
                            ]),
                            "count"=>$limitUtilization->count??0,
                        ];
                    }else if($item->limitType=="TXN" && isset($item->amountRange->minTransaction) && isset($item->amountRange->maxTransaction)){
                        $trxLimit=[
                            "minAmount"=>$item->amountRange->minTransaction,
                            "maxAmount"=>$item->amountRange->maxTransaction,
                        ];
                    }
                }
            }
        }
        $trxLimit["limits"]=$limits;
        //return $this->verifyUser($request,$customer);
        return response()->json(GeneralResponseData::from(array(
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ))->additional($trxLimit));
    }
    public function customerTransfer(Request $request,$resultType='json'){
        $result=CustomerService::customerTransfer($request);
        if(isset($result->status->message->code) && $result->status->message->code=="0" && isset($result->accounts)){
            $object=new \stdClass();
            $customerInfo = AdminService::customerInfo($object,[
                "partyId"=>$request->customerNo,
                "userType"=>'retailuser'
            ]);

            if(!is_null($customerInfo) && is_object($customerInfo)){
                $result->party=[
                    "personalDetails"=>[
                        "fullName"=>Controller::maskString(html_entity_decode($customerInfo->firstName),4).
                        " ".Controller::maskString("00000").
                        " ".Controller::maskString(html_entity_decode($customerInfo->lastName),0,4)
                    ]
                ];
            }


        }


        return response()->json( $result);
    }
    public function loyaltyToken(Request $request){
        $user=auth()->user();
        $object=new \stdClass();
        $object->name=$user->name;
        $object->email= $user->email;
        $object->phone= $user->phone;

        $token=LoyaltyService::getCustomerToken($object);
        if(!is_null($token)){
            return response()->json(GeneralResponseData::from(array(
                'status'=>[
                    "result"    => "SUCCESSFUL",
                    "contextID" => "",
                    "message"   => [
                        "title"   => "",
                        "detail"  => "",
                        "code"    => "0",
                        "type"    => "INFO"
                    ]
                ]
            ))->additional([
                'token'=>$token
            ]));

        }
        return response()->json(GeneralResponseData::from(array(
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "",
                "message"   => [
                    "title"   => __("This service not available right now! please try again later."),
                    "detail"  => "",
                    "code"    => "DIGX_LOYALTY_001",
                    "type"    => "ERROR"
                ]
            ]
        )));
    }
    // public function batch2(Request $request){
    //     \Log::info($request->getContent());
    //     $dataList=explode($request->header('boundary'),$request->getContent());
    //     foreach ($dataList as $data) {
    //         $dataList=explode($request->header('boundary'),$request->getContent());
    //     }
    //     $headers=$request->headers->all();
    //     $filtered =  collect($headers)->except([
    //         'host',
    //         'cookie',
    //         'content-length',
    //         "user-agent",
    //         "postman-token",
    //         "content-type",
    //         //"x-original-url"
    //     ]);
    //     return \App\Helpers\ProxyHelperFacade::CreateProxy($request)
    //     // add a header before sending the request
    //     ->withHeaders($filtered->all())
    //     // add a Bearer token (this is useful for the client not to have the token, and from the intermediary proxy we add it.
    //     //->withToken('eyJhbGcLPbNA...')
    //     //Maintain the query of the url.
    //     ->withOptions([
    //         'cookies' => \GuzzleHttp\Cookie\CookieJar::fromArray($request->cookies->all(),env('OBDX_URL')),
    //         'verify' => false,
    //     ])
    //     ->preserveQuery(true)
    //     ->toUrl("{$_ENV['OBDX_HTTP']}://{$_ENV['OBDX_URL']}:{$_ENV['OBDX_PORT']}/digx/batch");
    // }
    public function batch(Request $request){

        $headers=$request->headers->all();
        $filtered =  collect($headers)->except([
            'host',
            'cookie',
            'content-length',
            "user-agent",
            "postman-token",
            "content-type",
            //"x-original-url"
        ]);
        return \App\Helpers\ProxyHelperFacade::CreateProxy($request)
            // add a header before sending the request
            ->withHeaders($filtered->all())
            // add a Bearer token (this is useful for the client not to have the token, and from the intermediary proxy we add it.
            //->withToken('eyJhbGcLPbNA...')
            //Maintain the query of the url.
            ->withOptions([
                'cookies' => \GuzzleHttp\Cookie\CookieJar::fromArray($request->cookies->all(),env('OBDX_URL')),
                'verify' => false,
            ])
            ->preserveQuery(true)
            ->toUrl("{$_ENV['OBDX_HTTP']}://{$_ENV['OBDX_URL']}:{$_ENV['OBDX_PORT']}/digx/batch");
    }

    public function redirectHandler(String $token,Request $request){
        $date=\Carbon\Carbon::now()->subHours(24)->toDateTimeString();
        //$sharing=\App\Models\Sharing::where("txn_token",$token)->whereDate("created_at",'>=',DB::raw("timestamp '".$date."'"))->first();
        $sharing=\App\Models\Sharing::where("txn_token",$token)->first();

        if(!is_null($sharing)){
            return $this->_redirectHandler("transfer/{$sharing->txn_token}","transfer?txnToken={$sharing->txn_token}");
            // switch($sharing->type){
            //     case 'transfer':
            //         return $this->_redirectHandler("$sharing->type/{$sharing->txn_token}","$sharing->type?txnToken={$sharing->txn_token}");
            //     case 'account':
            //         return $this->_redirectHandler("$sharing->type/{$sharing->txn_token}","$sharing->type?txnToken={$sharing->txn_token}");

            // }
        }
        return abort(Response::HTTP_NOT_FOUND,"Transaction expired or not found!");
    }
    private function _redirectHandler(String $urlAndroid,String $urlIOS){
        $bundle=env('APP_BUNDLE');
        //Detect special conditions devices
        $iPod = stripos($_SERVER['HTTP_USER_AGENT'], "iPod");
        $iPhone = stripos($_SERVER['HTTP_USER_AGENT'], "iPhone");
        $iPad = stripos($_SERVER['HTTP_USER_AGENT'], "iPad");
        $Android = stripos($_SERVER['HTTP_USER_AGENT'], "Android") || stripos($_SERVER['HTTP_USER_AGENT'], "Linux");
        $webOS = stripos($_SERVER['HTTP_USER_AGENT'], "webOS");

        $schema = $bundle=="com.ykb.bankylite"?"bankylite":"ibankylite";
        //do something with this information
        if ($iPod || $iPhone || $iPad) {
            return redirect("$schema://$urlIOS");
            //browser reported as an iPhone/iPod touch -- do something here
        } else if ($Android) {
            // \Log::info("intent://$urlAndroid#Intent;scheme=bankylite;package=com.ykb.bankylite;end");
            // return redirect("intent://$urlAndroid#Intent;scheme=bankylite;package=com.ykb.bankylite;end");

            return redirect("market://details?id=$bundle&url=$schema://$urlAndroid#Intent");
            //bankylite://transfer/98a5975c-ecd9-46f4-a8ec-5fb03aa7fd4d#Intent;scheme=bankylite;package=com.ykb.bankylite;end
            //browser reported as an Android device -- do something here
        } else {
            return redirect("https://play.google.com/store/apps/details?id=$bundle");
            // return redirect("/app/$token");
            //browser reported as a webOS device -- do something here
        }
    }

}
