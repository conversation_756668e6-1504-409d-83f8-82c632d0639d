<?php

namespace App\Jobs;

use App\Data\Pass\PassCreateRequestData;
use App\Data\PaymentResultData;
use App\Data\ThirdPartyServiceNameData;
use App\Enums\InvoiceTransactionsTypeEnum;
use App\Enums\TransactionStatusEnum;
use App\LogItem;
use App\Models\Gift;
use App\Models\Pass;
use App\Models\PassTransaction;
use App\Scopes\CustomerScope;
use App\Services\FlexService;
use App\Services\NotificationService;
use App\Services\PassService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\Attributes\WithoutRelations;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ProcessResolveWasil implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

       /**
     * Create a new job instance.
     * @param mixed $user
     * @param PassCreateRequestData $requestData
     * @param Pass $pass
     * @param Pass $passTransaction
     * @return void
     */
    public function __construct(
        public $user,
        public PassCreateRequestData $requestData,
        public Pass $pass,
        public PassTransaction $passTransaction
    ){
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {

        if(!in_array($this->pass->type,[InvoiceTransactionsTypeEnum::Payment->value,InvoiceTransactionsTypeEnum::Refund->value])){
            return;
        }

        $response=PassService::findByReference($this->requestData);
        $verifyResult=$response->getAdditionalData()['extra']??null;

        if($this->pass->type==InvoiceTransactionsTypeEnum::Payment->value){
            if($this->pass->status==TransactionStatusEnum::ERROR->value){
                if($response->status->message->code=="0" && isset($verifyResult->result_code)
                    &&  $this->passTransaction->payment_result?->amount?->status==InvoiceTransactionsTypeEnum::Reverse->value
                ){
                    LogItem::store($this->pass); 
                    $details=json_decode(json_encode([
                        "message"    => "Expired",
                        "mpt"=>$verifyResult->mpt
                    ]));
                    $details->verify=$verifyResult;
            
                    if(is_null($this->passTransaction->extra)){
                        $this->passTransaction->extra=$verifyResult;
                        $this->passTransaction->save();
                    }
                    
                    $passTransaction=PassTransaction::create([
                        "pass_id"           =>$this->pass->id,
                        "type"              =>InvoiceTransactionsTypeEnum::Refund->value,
                        "status"            =>TransactionStatusEnum::COMPLETED->value,
                        "account_id"        =>$this->passTransaction->account_id,
                        "receiver_mobile"   =>$this->passTransaction->receiver_mobile,
                        "amount"            =>$this->passTransaction->amount,
                        "fee"               =>$this->passTransaction->fee,
                        "reference_id"      =>uniqid(),
                        "remarks"           =>$this->passTransaction->remarks,
                        "extra"=>$details,
                    ]);
                    $passTransaction->payment_result=$this->passTransaction->payment_result;
            
                    $requestData=PassCreateRequestData::from([
                        "reference_id"=> $passTransaction->reference_id,
                        "external_reference_id"=> $verifyResult->transaction_reference,
                        "row_version" => $verifyResult->row_version,
                        //"operation_reference" =>$verifyResult->transaction_reference,
                    ]);
                    $result=PassService::cancel($requestData);
    
                   
    
                    if($result->status->message->code=="0"){
                        $this->pass->refresh();
                        if($this->pass->status!=TransactionStatusEnum::ERROR->value ||  $this->pass->resolved==1){
                            return;
                        }
                        if(!is_null($this->user)){
                            $this->pass->resolved_by=$this->user->id;
                        }
                        $this->pass->resolved=1;
                        $this->pass->save();
                
    
                        //$this->pass->status=TransactionStatusEnum::COMPLETED->value;
                        //$this->pass->type=InvoiceTransactionsTypeEnum::Refund->value;
                        //$this->pass->save();
                        
                        $extra=$result->getAdditionalData()["extra"];
    
                        if(!isset($this->passTransaction->extra->operation_reference)){
                            $oldExtra=$this->passTransaction->extra;
                            $oldExtra->operation_reference=$extra->operation_reference;
                            $this->passTransaction->extra=$oldExtra;
                            $this->passTransaction->save();
                        }
                     
                        $passTransaction->extra= collect($passTransaction->extra)
                        ->merge($extra);//$result->getAdditionalData()["extra"];
                        $passTransaction->external_reference_id= $passTransaction->extra->financial_reference;
                        $passTransaction->status=TransactionStatusEnum::COMPLETED->value;
                        $passTransaction->save();
                    }
    
                }else if($response->status->message->code=="DIGX_SWITCH_WASIL_NOT_FOUND_001"){
                    LogItem::store($this->pass);
    
                    $this->pass->refresh();
                    if($this->pass->status!=TransactionStatusEnum::ERROR->value ||  $this->pass->resolved==1){
                        return;
                    }
                    
                    if(!is_null($this->user)){
                        $this->pass->resolved_by=$this->user->id;
                    }
                    $this->pass->resolved=1;
                    $this->pass->save(); 
                } 
            }else if($this->pass->status==TransactionStatusEnum::PENDING->value){
                if($response->status->message->code=="DIGX_SWITCH_WASIL_NOT_FOUND_001"){
                    LogItem::store($this->pass);
                    $this->pass->refresh();
                    if($this->pass->status!=TransactionStatusEnum::PENDING->value ||  $this->pass->resolved==1){
                        return;
                    }
                    
                    if(!is_null($this->user)){
                        $this->pass->resolved_by=$this->user->id;
                    }
                    //$this->pass->status=TransactionStatusEnum::ERROR->value;
                    //$this->pass->type=InvoiceTransactionsTypeEnum::Refund->value;
                    $this->pass->resolved=1;
                    $this->pass->save();
    
                    $paymentResult=$this->passTransaction->payment_result;
                    $object=new \stdClass();
                    $object->reference_id   = $paymentResult->amount->referenceId;
                    $object->service_name   =  ThirdPartyServiceNameData::wasil();
                    $object->account_id     = $this->passTransaction->account_id->value;
            
                    $reverseResult=FlexService::reverseToAccount($object);
                    if($reverseResult->status->message->code=="0"){
                        $paymentResult->amount->status=InvoiceTransactionsTypeEnum::Reverse->value;
                        $this->passTransaction->payment_result= $paymentResult;
                        $this->passTransaction->save();
                    }
                    if(isset($paymentResult->fee)){
                        $object=new \stdClass();
                        $object->reference_id   = $paymentResult->fee->referenceId;
                        $object->service_name   = ThirdPartyServiceNameData::wasilFee();
                        $object->account_id     = $this->passTransaction->account_id->value;
                
                        $reverseResult=FlexService::reverseToAccount($object);
                        if($reverseResult->status->message->code=="0"){
                            $paymentResult->fee->status=InvoiceTransactionsTypeEnum::Reverse->value;
                            $this->passTransaction->payment_result= $paymentResult;
                            $this->passTransaction->save();
                        }
                    }
    
                  
                }
            }
        }else if($this->pass->type==InvoiceTransactionsTypeEnum::Refund->value){
            if($this->pass->status==TransactionStatusEnum::ERROR->value){
                if($response->status->message->code=="0" && isset($verifyResult->result_code)
                    &&  is_null($this->passTransaction->payment_result) && isset($verifyResult->cancel_reference) && !empty($verifyResult->cancel_reference??"")
                ){
                    LogItem::store($this->pass);
                    $this->pass->refresh();
                    if($this->pass->status!=TransactionStatusEnum::ERROR->value ||  $this->pass->resolved==1){
                        return;
                    }
                    
                    $transaction=$this->pass->transactions
                    ->where('status',TransactionStatusEnum::COMPLETED->value)
                    ->where('type',InvoiceTransactionsTypeEnum::Payment->value)
                    ->first();

                    if(is_null($transaction)){
                        return;
                    }

                    if(!is_null($this->user)){
                        $this->pass->resolved_by=$this->user->id;
                    }
                    $this->pass->resolved=1;
                    $this->pass->save();
                    
                    $extra=$verifyResult;
                    if(!isset($transaction->extra->operation_reference) && isset($extra->operation_reference)){
                        $oldExtra=$transaction->extra;
                        $oldExtra->operation_reference=$extra->operation_reference;
                        $transaction->extra=$oldExtra;
                        $transaction->save();
                    }

                    $this->pass->status=TransactionStatusEnum::COMPLETED->value;
                    $this->pass->save();

                    $this->passTransaction->extra= collect($this->passTransaction->extra)
                    ->merge($extra);//$result->getAdditionalData()["extra"];
                    $this->passTransaction->external_reference_id= $this->passTransaction->extra->financial_reference;
                    $this->passTransaction->status=TransactionStatusEnum::COMPLETED->value;
                    $this->passTransaction->save();

                    /** Reverse to user account */
                    $paymentResult=$transaction->payment_result;
                    $object=new \stdClass();
                    $object->reference_id   = $paymentResult->amount->referenceId;
                    $object->service_name   =  ThirdPartyServiceNameData::wasil();
                    $object->account_id     = $transaction->account_id->value;
            
                    $reverseResult=FlexService::reverseToAccount($object);
                    if($reverseResult->status->message->code=="0"){
                        $paymentResult->amount->status=InvoiceTransactionsTypeEnum::Reverse->value;
                        $this->passTransaction->payment_result= $paymentResult;
                        $this->passTransaction->save();
                    }
    
                }
            }
        }
    }
}
