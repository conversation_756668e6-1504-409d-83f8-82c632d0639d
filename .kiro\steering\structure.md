# Project Structure

## Laravel Application Structure

### Core Application (`app/`)
```
app/
├── Console/           # Artisan commands
├── Data/             # Data Transfer Objects (Spatie Laravel Data)
├── Enums/            # PHP enums for constants
├── Exceptions/       # Custom exception handlers
├── Guards/           # Authentication guards
├── Helpers/          # Helper classes and utilities
├── Http/             # Controllers, middleware, requests
├── Jobs/             # Queue jobs
├── Listeners/        # Event listeners
├── Mail/             # Mail classes
├── Models/           # Eloquent models
├── Providers/        # Service providers
├── Scopes/           # Query scopes
├── Services/         # Business logic services
├── Settings/         # Application settings (Spatie)
├── Telescope/        # Telescope customizations
├── Traits/           # Reusable traits
├── CacheModel.php    # Base model with caching
├── Client.php        # Passport client extension
└── LogItem.php       # Logging model
```

### Configuration (`config/`)
- Standard Laravel config files
- Custom configs: `general.php`, `firebase.php`, `tcpdf.php`
- Database prefix: `DIGX_LITE_` for Oracle tables

### Database (`database/`)
```
database/
├── factories/        # Model factories
├── migrations/       # Database migrations
├── seeders/          # Database seeders
└── settings/         # Settings-related migrations
```

### Frontend Assets (`resources/`)
```
resources/
├── js/               # JavaScript/Vue.js components
├── lang/             # Multi-language files
├── mocks/            # Mock data for development
└── views/            # Blade templates
```

### Public Assets (`public/`)
```
public/
├── assets/           # Compiled assets
├── css/              # Stylesheets
├── icons/            # App icons
├── themes/           # Theme-specific assets
├── vendor/           # Third-party assets
├── flutter.js        # Flutter web runtime
├── main.dart.js      # Compiled Flutter app
└── manifest.json     # PWA manifest
```

### API Documentation (`docs/`)
```
docs/
└── OpenApi/          # OpenAPI/Swagger documentation
```

## Naming Conventions

### Models
- Use singular PascalCase: `User`, `BankAccount`, `Transaction`
- Extend `CacheModel` for automatic cache invalidation
- Use UUIDs for primary keys where applicable

### Controllers
- Use PascalCase with `Controller` suffix: `UserController`, `TransactionController`
- Follow RESTful conventions for method names

### Database
- Table names: snake_case with `DIGX_LITE_` prefix
- Column names: snake_case
- Foreign keys: `{table}_id` format

### Files & Directories
- Use PascalCase for class files
- Use kebab-case for view files
- Use snake_case for migration files

## Architecture Patterns

### Service Layer
- Business logic should be in Service classes (`app/Services/`)
- Controllers should be thin, delegating to services

### Repository Pattern
- Use for complex database operations
- Abstract database queries from business logic

### Event-Driven Architecture
- Use Laravel Events and Listeners for decoupled operations
- Queue heavy operations using Jobs

### Caching Strategy
- Extend `CacheModel` for automatic cache management
- Use Redis for application caching
- Cache invalidation on model updates

## Security Considerations
- All API endpoints require authentication (Passport)
- Use role-based permissions (Spatie Permission)
- Validate all inputs using Form Requests
- Sanitize data for Oracle database compatibility
- Use HTTPS in production environments

## Testing Structure
```
tests/
├── Feature/          # Integration tests
├── Unit/             # Unit tests
├── CreatesApplication.php
└── TestCase.php
```