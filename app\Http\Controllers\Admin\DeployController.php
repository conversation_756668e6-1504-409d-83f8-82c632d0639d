<?php

namespace App\Http\Controllers\Admin;
use App\Data\GeneralResponseData;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Auth;

use Symfony\Component\Process\Process;

class DeployController extends Controller
{

    public function __construct(Request $request){
        $this->middleware('auth.basic');
    }

    public function deploy(Request $request)
    {

        // $githubPayload = $request->getContent();
        // $githubHash = $request->header('X-Hub-Signature');
        // $localToken = config('app.deploy_secret');
        // $localHash = 'sha1=' . hash_hmac('sha1', $githubPayload, $localToken, false);
        //if (hash_equals($githubHash, $localHash)) {
            if (Auth::user()->hasRole('developer')) {
                $this->_deploy($request);
            }else if (auth()->user()->hasRole('Deploy')) {
                $payload = $request->json()->all();
                $eventType = $request->header('X-Gitlab-Event');

                // Handle Merge Request Events
                if ($eventType === 'Merge Request Hook') {
                    $sourceBranch = $payload['object_attributes']['source_branch'];
                    $targetBranch = $payload['object_attributes']['target_branch'];
                    $state = $payload['object_attributes']['state']; // "opened", "merged", "closed"

                    if ($targetBranch === 'master') {
                        if ($state === 'merged') {
                            \Log::info("Merge request from 'test' to 'master' was merged.");

                            // Perform actions after merge (e.g., deploy, notify, etc.)
                            $this->_deploy($request);
                            return;
                        }
                    }
                }

                // Handle Push Events (Commits)
                if ($eventType === 'Push Hook') {
                    $branch = str_replace('refs/heads/', '', $payload['ref']); // Extract branch name

                    if ($branch === 'master') {
                        $commits = $payload['commits'] ?? [];
                        foreach ($commits as $commit) {
                            \Log::info("New commit to master: " . $commit['message']);
                        }
                        // Perform actions on commit (e.g., deploy, notify, etc.)
                        $this->_deploy($request);
                        return;
                    }
                }

            }
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "",
                    "message"   => [
                        "title"   => "",
                        "detail"  => "",
                        "code"    => "0",
                        "type"    => "UNDIFIND"
                    ]
                ]
            ]));
        //}
    }

    protected function _deploy(Request $request)
    {

        ini_set('max_execution_time',300);
        $root_path = base_path();
        $process = Process::fromShellCommandline('cd ' . $root_path . '; bash deploy.sh');
        $process->setTimeout(3600)->run(function ($type, $buffer) {
            echo $buffer;
        });
    }

    public function generateOpenApi(){
        if (Auth::user()->hasRole(['developer','Deploy'])) {
             //require("vendor/autoload.php");
        define(constant_name: "API_HOST", value:env('APP_URL'));
        $openapi = \OpenApi\Generator::scan([base_path('docs/OpenApi')],[
            'logger' => \Illuminate\Support\Facades\Log::getLogger(), // Laravel logger
            // 'config' => [
            //     'documentations'=> [
            //         /// ... other configs

            //             /*
            //              * Absolute paths to directory containing the swagger annotations are stored.
            //             */
            //             'annotations' => [
            //                 base_path('app/Swagger'), // You should add this line to work
            //                 base_path('app/'),
            //             ],
            //             ]

            // ],
            // 'logger' => null,
            // 'validate' => true,
            // 'version' => null,
        ]);
        //return response()->json($openapi->toJson());
        //header('Content-Type: application/x-yaml');
        $openapi->saveAs(base_path("/openapi.yaml"));
        return response()->json([
            "success"
        ]);
        //return response()->download(public_path("/openapi.yaml"));
        //'logger' => \Illuminate\Support\Facades\Log::getLogger(), // Laravel logger
        }
    }
}
