<?php

namespace App\Enums;
enum LimitTypeEnum:string {
    case SELF="PC_F_SELF";
    case INTERNAL="PC_F_IT";
    case DOMESTIC="PC_F_DOM_NEFT";
    case INTERNATIONAL="PC_F_INTRNL";
    case YEAHMONEY="PC_F_YEAH";
    case GOLD_SELF="PC_F_SELF_GOLD";
    case GOLD_BUY="PC_F_BUY_GOLD";
    case GOLD_SELL="PC_F_SELL_GOLD";
    case GOLD_INTERNAL="PC_F_IT_GOLD";
    case GIFT="PC_F_GIFT";
    case WASIL="PC_F_WASIL";
    case OIL_PAYMENT="PC_F_OIL_PAYMENT";

    //case PC_F_BPT="PC_F_BPT";

    public static function values():array {
        return collect(LimitTypeEnum::cases())->pluck("value")->toArray();
    }
    public static function find(string $value):LimitTypeEnum|null {
        return collect(LimitTypeEnum::cases())->filter(function($item) use($value){
            return $item->value==$value;
        })->first();
    }

    public static function list():array {
        return collect(LimitTypeEnum::cases())->map(function($element){
            return [
            "name"=>__($element->name),
            "value"=>$element->value
        ];
        })->pluck("name","value")->toArray();
        //return collect(LimitTypeEnum::cases())->pluck("name","value")->toArray();
    }
}
