<?php

use <PERSON><PERSON>\LaravelSettings\Migrations\SettingsMigration;

class AddServiceNameThirdPartySettings extends SettingsMigration
{
    public function up(): void
    {
        $this->migrator->addEncrypted('thirdParty.serviceName',array (
            'gift' => 'eid',
            'wasil' => 'WASEL',
            'wasilFee' => 'WASEL',
            'is_test' => '0',
            'test_data' => array (
                'gift' => 'bass',
                'wasil' => 'bass',
                'wasilFee' => 'bass',
            )
        ));
        
    }
    public function down()
    {
        $this->migrator->delete('thirdParty.serviceName');
    }
}