<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\InvoiceTransaction;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<\App\Models\InvoiceTransaction>
 */
final class InvoiceTransactionFactory extends Factory
{
    /**
    * The name of the factory's corresponding model.
    *
    * @var string
    */
    protected $model = InvoiceTransaction::class;

    /**
    * Define the model's default state.
    *
    * @return array
    */
    public function definition(): array
    {
        $transaction=\App\Models\GiftTransaction::factory()->create();
        return [
            'invoice_id' => \App\Models\Invoice::factory(),
            'type' => fake()->randomElement(\App\Enums\InvoiceTransactionsTypeEnum::values()),
            'status' => fake()->randomElement(\App\Enums\TransactionStatusEnum::values()),
            'amount' => fake()->optional()->text,
            'reference_id' => fake()->optional()->word,
            'external_reference_id' => fake()->optional()->word,
            'remarks' => fake()->optional()->text,
            'gift_id' => $transaction->gift_id,
            'gift_transaction_id' => $transaction->id,
        ];
    }
}
