<?php
namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        $this->call(MapServicesTableSeeder::class);
        $this->call(MapCitiesTableSeeder::class);
        $this->call(MapDistrictsTableSeeder::class);
        $this->call(MapEntriesTableSeeder::class);
        $this->call(HarvestServiceCodeTableSeeder::class);
        $this->call(MarchentsTableSeeder::class);
        $this->call(ServicesTableSeeder::class);
        $this->call(UsersTableSeeder::class);
        $this->call(OilRegionsTableSeeder::class);
        $this->call(PermissionsTableSeeder::class);
        $this->call(RolesTableSeeder::class);
        $this->call(ModelHasPermissionsTableSeeder::class);
        $this->call(ModelHasRolesTableSeeder::class);
        $this->call(RoleHasPermissionsTableSeeder::class);
        $this->call(UserInterfacesTableSeeder::class);
        $this->call(FixServiceSeeder::class);
    }
}
