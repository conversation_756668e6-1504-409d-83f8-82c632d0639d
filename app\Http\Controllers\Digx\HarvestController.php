<?php

namespace App\Http\Controllers\Digx;

use App\Data\AccountIdData;
use App\Data\Classes\BranchData;
use App\Data\CurrencyAmountData;
use App\Data\GeneralResponseData;
use App\Data\ReceiptData;
use App\Data\TokenData;
use App\Data\AccountData;
use App\Data\ManualHarvestRequestData;
use App\Data\ManualHarvestResponseData;

use App\Enums\ServiceTagEnum;
use App\Http\Controllers\Controller;
use App\LogItem;
use App\Models\Harvest;
use App\Models\HarvestServiceCode;
use App\Models\ManualHarvest;
use App\Services\NotificationService;
use App\Traits\AuthorizesServices;
use DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class HarvestController extends Controller
{
    use AuthorizesServices;

    protected $settings;

    public function __construct(Request $request)
    {
        //$this->middleware('digx');
        $this->settings=app(\App\Settings\ThirdPartySettings::class)->yeahMoney;
        if($this->settings->is_test){
            $this->settings=$this->settings->test_data;
        }
        parent::__construct();
    }
    protected function getServiceTags(): array{
        return [
            ServiceTagEnum::CLAIM_MONEY
        ];
    }

    public function getAccessToken($forceUpdate=false)
    {
        $status=200;
        $token=$this->settings->token;
        if($forceUpdate){
            $token->access_token="";
            $this->settings->token=$token;
        }
        if(!isset($token->access_token)|| is_null($token->access_token)||empty($token->access_token)){
            $response = Http::timeout(10)
            ->withBasicAuth($this->settings->client_id, $this->settings->client_secret)

            ->asForm()
            ->post("{$this->settings->url}/oauth/token",[
                "grant_type"=>"client_credentials"
            ]);

            if ($response->failed()) {
                return $response;
            }
            $status=$response->status();
            $response=$response->object();

            if(isset($response->access_token)){
                $this->settings->token=TokenData::from($response);
                $settings=app(\App\Settings\ThirdPartySettings::class);
                $yeahMoney=$settings->yeahMoney;
                if($yeahMoney->is_test){
                    $test_data=$yeahMoney->test_data;
                    $test_data->token=$this->settings->token;
                    $yeahMoney->test_data=$test_data;
                }else{
                    $yeahMoney->token=$this->settings->token;
                }
                $settings->yeahMoney=$yeahMoney;
                $settings->save();
            }
           // dd($response);

        }
        return $status;
    }
    /**
     * Display a listing of the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $user=$request->user()->userProfile;

        $creditAccountId=DB::raw("json_query(data,'$.creditAccountId') as credit_account_id");
        if(env('DB_CONNECTION')!='oracle'){
            $creditAccountId="data->creditAccountId as credit_account_id";
        }

        $harvests=Harvest::select("harvests.id","data->trackingCode as tracking_code","data->senderPhone as sender_phone","sender_info->Sender_Full_Name as sender_name",
            "amount",$creditAccountId,"received_data->Payout_Trx_Id as reference_id",
            "name","harvests.status","harvests.created_at as date")
        ->leftJoin("harvest_service_codes","harvest_service_codes.id","=","service_code_id")
        ->where("harvests.status",1)
        ->skip( $request->from)
        ->take($request->limit)
        ->orderBy("harvests.id","DESC")
        ->get();
        return response()->json($harvests);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $serviceCodes=HarvestServiceCode::where("status",1)->get(["id","name"]);
        return response()->json($serviceCodes);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator=validator()->make($request->all(),[
            "trackingCode"=> "required",
            'amount.amount'=>"nullable|numeric",
            'amount.currency'=>"nullable|max:3|min:3",
            "senderPhone"=> "nullable|max:9|min:9",
            'branchId'=>"required|max:3|min:3",
            "serviceCode"=> "required|numeric",
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-HARVEST",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_HARVEST_100",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        $user=$request->user()->userProfile;
        $requestData=\App\Data\HarvestRequestData::from($request->all());
        // if(env('APP_ENV', 'production')!='production'){
        //     $test=collect(Harvest::list)->where("Utn Frmtd",$requestData->trackingCode)->first();
        //     \Log::info($test);
        //     $test=\App\Data\HarvestRequestData::from($test);
        //     //$requestData->trackingCode="401707304";
        //     $requestData->firstName=$test->firstName;
        //     $requestData->secondName=$test->secondName;
        //     $requestData->thirdName=$test->thirdName;
        //     $requestData->lastName=$test->lastName;
        //     $requestData->senderPhone=$test->senderPhone;
        //     // if($user->partyId->value=="0183415"){
        //     //     $requestData->amount->currency="USD";
        //     // }
        // }else
        {
            $middleNames=explode(" ",html_entity_decode(trim($user->middleName)));
            $requestData->firstName=html_entity_decode($user->firstName);
            $requestData->secondName=collect($middleNames)->first();
            array_shift($middleNames);
            $requestData->thirdName=join(' ',$middleNames);
            $requestData->lastName=html_entity_decode($user->lastName);
        }

        $serviceCode=HarvestServiceCode::where("status",1)->where("id",$requestData->serviceCode)->first();
        if(is_null($serviceCode)){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-HARVEST-SERVICE-CODE",
                    "message"   => [
                        "title"   => "Service code not found!",
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_HARVEST_004",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        $status=$this->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-HARVEST-REMOTE - $status - ".($request->retry??-1),
                    "message"   => [
                        "title"   => "Can't access reverse server, please connect help desk to fix problem!",
                        "detail"  => "Can't access reverse server, please connect help desk to fix problem!",
                        "code"    => "DIGX_SWITCH_001",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        $timestamp=round(microtime(true)*1000);

        $agent=$this->settings->agent_info;
        $agent['User_Info']['Agent_User_Name']=$requestData->fullName();
        $agent['User_Info']['Language']=app()->getLocale();
        $agent['Branch_Code']=Harvest::branchMapping[$requestData->branchId];

        //Start login process
        $response = Http::withToken($this->settings->token->access_token)
        ->withHeaders([
            "Trx_Ref_Id"=>$timestamp
        ])
        ->post(
            "{$this->settings->url}/mt/v1/login",
            [
                "Agent_Info" => $agent
            ]
        );
        if($response->status()==401){
            $request->retry=($request->retry??-1)+1;
            return static::store($request);
        }

        $authResult=$response->object();
        if($response->failed() || $response->status()!=200 || ($authResult->Result_Code??-1)!=0){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "[YEAH_MONEY_SERVER_LOGIN] - ".($authResult->Result_Code??-1),
                    "message"   => [
                        "title"   => "Something wrong please connect customer support for help!",
                        "detail"  => "Something wrong please connect customer support for help!",
                        "code"    => "DIGX_SWITCH_HARVEST_003",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }
        $token=$response->header("Trx_Token");

        // Start getting user ID card info.
        $partyInfo=\App\Services\OBDX\CustomerService::party($request);
        if(!($partyInfo instanceof GeneralResponseData)){
            $personalDetails=$partyInfo->personalDetails;
            if(isset($personalDetails->firstName)){
                $middleNames=explode(" ",html_entity_decode(trim($personalDetails->middleName)));
                $requestData->firstName=html_entity_decode($personalDetails->firstName);
                $requestData->secondName=collect($middleNames)->first();
                array_shift($middleNames);
                $requestData->thirdName=join(' ',$middleNames);
                $requestData->lastName=html_entity_decode($personalDetails->lastName);
            }else{
                $middleNames=explode(" ",html_entity_decode(trim($personalDetails->fullName)));
                if(count($middleNames)==2){
                    $requestData->firstName=array_shift($middleNames);
                    $requestData->secondName="";
                    $requestData->thirdName="";
                    $requestData->lastName=array_shift($middleNames);
                }else if(count($middleNames)==3){
                    $requestData->firstName=array_shift($middleNames);
                    $requestData->secondName=array_shift($middleNames);
                    $requestData->thirdName="";
                    $requestData->lastName=array_shift($middleNames);
                }else if(count($middleNames)>=4){
                    $requestData->firstName=array_shift($middleNames);
                    $requestData->secondName=array_shift($middleNames);
                    $requestData->thirdName=array_shift($middleNames);
                    $requestData->lastName=join(' ',$middleNames);
                }
            }
            $requestData->fullName=html_entity_decode($personalDetails->fullName);
        }

        // Start getting Payout Info.
        $response = Http::withToken($this->settings->token->access_token)
        ->withHeaders([
            "Trx_Token"     =>str_replace("Bearer ","",$token),
            "Service_Code"  =>$serviceCode->code,
            "Trx_Ref_Id"    =>$timestamp
        ])
        ->post(
            "{$this->settings->url}/mt/v1/payoutInfo",
            [
                "Product_Code"          => "PULL_2_ACB",
                "Unique_Tracking_Code"  => $requestData->trackingCode,
                "Payin_Data" => $requestData->payoutInfoParams()
            ]
        );

        $result=$response->object();
        if($response->failed() || $response->status()!=200 || ($result->Result_Code??-1)!=0){
            // Yeah Money processing failed - offer manual processing option
            $networkType = ManualHarvest::determineNetworkType($requestData->trackingCode);

            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "MANUAL_PROCESSING_REQUIRED",
                    "contextID" => "[YEAH_MONEY_SERVER_ENQUIRY] - ".($result->Result_Code??-1),
                    "message"   => [
                        "title"   => "Automatic processing not available for this remittance",
                        "detail"  => "This remittance appears to be from {$networkType} which requires manual processing. Please provide additional information to create a manual processing request.",
                        "code"    => "DIGX_SWITCH_HARVEST_MANUAL_REQUIRED",
                        "type"    => "INFO"
                    ]
                 ]
            ])->additional([
                'manualProcessingRequired' => true,
                'trackingCode' => $requestData->trackingCode,
                'detectedNetwork' => $networkType,
                'requiredFields' => [
                    'targetAccountId' => 'Account ID where funds should be deposited',
                    'remittanceCurrency' => 'Expected currency and amount (if known)',
                    'customerNotes' => 'Any additional information about the remittance'
                ],
                'nextStep' => 'Submit manual processing request via POST /digx/harvest/manual'
            ]));
        }else if($result->Result_Code==0 && isset($result->Payout_Trx_Id)){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "[YEAH_MONEY_SERVER_ENQUIRY] - {$result->Result_Code} - {$result->Result_Desc}",
                    "message"   => [
                        "title"   => "This remittance already paid!",
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_HARVEST_005",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }
        $token=$response->header("Trx_Token");

        $senderInfo=$result->Sender_Info;
        $senderInfo->Agent_Name=$result->Payin_Info->Payin_Agent_Name;
        //Create init model
        $harvest=new Harvest();
        LogItem::store($harvest);

        $harvest->service_code_id= $serviceCode->id;
        $harvest->data=$requestData->toArray();
        $harvest->request_id=$timestamp;
        $harvest->token=$token;
        $harvest->sender_info=$senderInfo;
        $harvest->receiver_info=$result->Receiver_Info;
        $harvest->amount=CurrencyAmountData::from([
            "amount"    =>$result->Settllement_Info->Payout_Amount,
            "currency"  =>$result->Settllement_Info->Payout_Currency_Code,
        ])->toArray();

        $harvest->save();

        return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "SUCCESSFUL",
                    "contextID" => "",
                    "message"   => [
                        "title"   => "",
                        "detail"  => "",
                        "code"    => "0",
                        "type"    => "INFO"
                    ]
                ],
            ])->additional([
                "harvest"=>[
                    'id'=>$harvest->id,
                    'amount'=>$harvest->amount,
                    'sender_name'=>$harvest->sender_info->Sender_Full_Name,
                    'receiver_name'=>$harvest->receiver_info->Receiver_Full_Name,
                ]
            ])
        );
    }


    public function receipt(Request $request,Harvest $harvest)
    {
        $this->generateReceipt( $this->getReceiptData($harvest));
    }
    protected function getReceiptData(Harvest $harvest): ReceiptData
    {
        $title=__("Claim money").' '.__("through") ." (".__($harvest->service->name).")";
        $sender=$harvest->sender_info->Sender_Full_Name;
        if(!is_null($harvest->data->senderPhone)){
            $sender.=" - (".$harvest->data->senderPhone.")";

        }
        return ReceiptData::from([
            "id"=> $harvest->id,
            "date"=> date_format(date_create($harvest->created_at), "Y-m-d H:i:s"),
            "title"=>$title,
            "sender"=> $sender,
            "statement"=> __("Claim money").' '.__("through") ." (".__($harvest->sender_info->Agent_Name??$harvest->service->name).")",
            "details"=> [
                "debitAccountId"=> $harvest->data->creditAccountId ,
                "remittanceId"=> $harvest->data->trackingCode,
                "amount"=>$harvest->amount
            ]
        ]);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Harvest  $harvest
     * @return \Illuminate\Http\Response
     */
    public function show(Harvest $harvest)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Harvest  $harvest
     * @return \Illuminate\Http\Response
     */
    public function edit(Harvest $harvest)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Harvest  $harvest
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, Harvest $harvest)
    {
        $validator=validator()->make($request->all(),[
            'creditAccountId.value'=>"required|max:20|min:20"
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "CONFIRM-HARVEST",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_HARVEST_100",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        if( $harvest->status!=0){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "CONFIRM-HARVEST",
                    "message"   => [
                        "title"   => __("This transaction already expired"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_HARVEST_101",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        LogItem::store($harvest);

        $user=$request->user()->userProfile;
        $requestData=\App\Data\HarvestRequestData::from($harvest->data);
        $requestData->creditAccountId=AccountIdData::from($request->creditAccountId);

        $valid=false;
        $message="You don't have the correct account!";
        $result=\App\Services\OBDX\CustomerService::account($request,$requestData->creditAccountId->value);
        if($result instanceof AccountData){
            $account=$result;
            $valid=$account->status=="ACTIVE" && $account->isCash()
                && $account->currencyId==$harvest->amount->currency
                && (($harvest->amount->currency==\App\Enums\CurrencyTypeEnum::YER->value &&  $account->isNorth()==in_array($requestData->branchId??"",BranchData::north()))
                    ||$harvest->amount->currency!=\App\Enums\CurrencyTypeEnum::YER->value);
        }

        if(!$valid){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-HARVEST",
                    "message"   => [
                        "title"   => __($message),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_HARVEST_102",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }


        $status=$this->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-HARVEST-REMOTE - $status - ".($request->retry??-1),
                    "message"   => [
                        "title"   => "Can't access reverse server, please connect help desk to fix problem!",
                        "detail"  => "Can't access reverse server, please connect help desk to fix problem!",
                        "code"    => "DIGX_SWITCH_001",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        $timestamp=round(microtime(true)*1000);

        // Start getting user ID card info.
        $resultCardInfo=\App\Services\OBDX\CustomerService::cardInfo($request);
        if($resultCardInfo->status->message->code!="0"){
            return response()->json($resultCardInfo);
        }
        $userCardInfo=$resultCardInfo->getAdditionalData()["cardInfo"];

        //Start harvest process.
        $response = Http::timeout(60)
        ->withToken($this->settings->token->access_token)
        ->withHeaders([
            "Trx_Token"     =>$harvest->token,
            "Service_Code"  =>$harvest->service->code,
            "Trx_Ref_Id"    =>$harvest->request_id
        ])
        ->post(
            "{$this->settings->url}/mt/v1/payout",
            [
                "Payout_Info" => [
                    "Product_Code"              => "PULL_2_ACB",
                    "PayIn_Method"              => "CSH",
                    "Unique_Tracking_Code"      => $requestData->trackingCode,
                    "Payout_Agent_Notes"        => "Banky lite claim remittance",
                    "Payout_Agent_Extra_Info"   => ""
                ],
                "Receiver_Info" => [
                    "Receiver_Id"               => $harvest->receiver_info->Receiver_Id??null,
                    "Receiver_Type"             => $harvest->receiver_info->Receiver_Type,
                    "Receiver_First_Name"       => $harvest->receiver_info->Receiver_First_Name??$requestData->firstName,
                    "Receiver_Second_Name"      => $harvest->receiver_info->Receiver_Second_Name??$requestData->secondName,
                    "Receiver_Third_Name"       => $harvest->receiver_info->Receiver_Third_Name??$requestData->thirdName,
                    "Receiver_Fourth_Name"      => $harvest->receiver_info->Receiver_Fourth_Name??"",
                    "Receiver_Surname"          => $harvest->receiver_info->Receiver_Surname??$requestData->lastName,
                    "Receiver_Full_Name"        => $harvest->receiver_info->Receiver_Full_Name??$requestData->fullName(),
                    "Receiver_Nationality_Code" => $harvest->receiver_info->Receiver_Nationality_Code?? "YEM",
                    "Receiver_Gender_Code"      => $harvest->receiver_info->Receiver_Gender_Code??$userCardInfo->gender??"",
                    "Receiver_Account_No"       => $requestData->creditAccountId->value,
                    "Receiver_Mobile"           => $harvest->receiver_info->Receiver_Mobile??html_entity_decode($user->phoneNumber->value),

                    "Receiver_Home_Address"     => html_entity_decode(join(",",collect($user->address)->only(['line1','line2','line3','line4','city','state'])->values()->all())),
                    "Receiver_Country_Code"     => $user->address->country??"YE",
                    "Receiver_Id_Country_Code"  => $userCardInfo->nationality??"YE",
                    "Receiver_Id_Type_Code"     => $userCardInfo->idType??"",
                    "Receiver_Id_Number"        => $userCardInfo->idNumber??"",
                    "Receiver_Id_Issue_Date"    => isset($userCardInfo->idExpiryDate)?\Carbon\Carbon::createFromFormat("Y-m-d",$userCardInfo->idExpiryDate)->subYears(10)->addDays(1)->format("d/m/Y"):"",//"1/23/2022 8:43:05 PM" data('s',strtotime($userCardInfo->idExpiryDate,"-10 year")),
                    "Receiver_Id_Expiry_Date"   => isset($userCardInfo->idExpiryDate)?\Carbon\Carbon::createFromFormat("Y-m-d",$userCardInfo->idExpiryDate)->format("d/m/Y"):"",
                    "Receiver_Id_Issue_Place"   => html_entity_decode($userCardInfo->idIssuePlace??""),
                    "Receiver_Id_Birth_Date"    => \Carbon\Carbon::createFromFormat("Y-m-d g:i:s",str_replace("T"," ",$user->dateOfBirth))->format("d/m/Y"),
                    "Receiver_Id_Birth_Place"   => "",

                    "Receiver_Email"            => $user->emailId->value,

                    //"Receiver_Fourth_Name" => $result->Receiver_Info->Receiver_Surname??"",
                    // "Receiver_Relation_Code" => "",
                    // "Receiver_Title" => "",
                    // "Receiver_Rprsnt_Id" => "",
                    // "Receiver_Rprsnt_Name" => "",
                    // "Receiver_Rprsnt_Mobile" => "",
                    // "Receiver_Post_Office_Box" => "",
                    // "Receiver_Governorate_Code" => "",
                    // "Receiver_District_Code" => "",
                    // "Receiver_Location_Code" => "",
                    // "Receiver_Work_Address" => "",
                    // "Receiver_Zipcode" => "",
                    // //"Receiver_Mobile" => $user->phoneNumber->value,
                    // "Receiver_Other_Details" => "",
                    // "Receiver_Is_Notifiedby_Email" => "",
                    // "Receiver_Is_Notifiedby_Sms" => "",
                    // //"Receiver_Id_Type_Country_Code" => $userCardInfo->nationality,

                    // "Receiver_Employer_Name" => "",
                    // "Receiver_Employer_Phone" => "",
                    // "Receiver_Employer_Address" => "",
                    // "Receiver_Source_Of_Income" => ""
                ]
             ]
        );
        $result=$response->object();

        $harvest->received_data= $result;
        $harvest->data= $requestData->toArray();

        if($response->successful() && $response->status()==200 && ($result->Result_Code??-1)==0){
            $harvest->status= 1;
            $harvest->save();
            //$msg="Your request under review, you will get notification after request reviewed.";
            //if($serviceCode->process==1){
                $msg="Remittance successfully harvest to your account.";
                NotificationService::sendMessagesToParty([
                    [
                        'title'=>__("Collect money"),
                        'body'=>sprintf(__("Successfully collect remittance #[%s] sended by [%s] service"),
                            $requestData->trackingCode,
                            __("Yeah Money")
                        ),
                        'type'=>'operation',
                    ]
                ]);
            //}
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "SUCCESSFUL",
                    "contextID" => "",
                    "message"   => [
                        "title"   => $msg,
                        "detail"  => $msg,
                        "code"    => "0",
                        "type"    => "INFO"
                    ]
                ]
            ])->additional([
                'externalReferenceId' => $result->Payout_Trx_Id??"",
                'receipt'=>$this->getReceiptData($harvest)
            ]));
        }
        $harvest->status= -1;
        $harvest->save();
        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "[YEAH_MONEY_SERVER_PAYOUT] - ".($result->Result_Code??-1),
                "message"   => [
                    "title"   => $result->Result_Desc??"Something wrong please connect customer support for help!",
                    "detail"  => "",
                    "code"    => "DIGX_SWITCH_HARVEST_006",
                    "type"    => "ERROR"
                ]
             ]
        ]),\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);
    }


    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Harvest  $harvest
     * @return \Illuminate\Http\Response
     */
    public function destroy(Harvest $harvest)
    {
        //
    }

}
