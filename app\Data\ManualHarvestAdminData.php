<?php

namespace App\Data;

use <PERSON><PERSON>\LaravelData\Attributes\Validation\Nullable;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Data;

class ManualHarvestAdminData extends Data
{
    #[Nullable]
    public ?object $senderInfo;
    
    #[Nullable]
    public ?CurrencyAmountData $remittanceDetails;
    
    #[Nullable]
    public ?string $adminNotes;
    
    #[Nullable]
    public ?string $externalReferenceId;
    
    #[Required]
    public int $status;

    public static function fromRequest(array $data): self
    {
        return new self(
            senderInfo: isset($data['senderInfo']) ? (object) $data['senderInfo'] : null,
            remittanceDetails: isset($data['remittanceDetails']) ? CurrencyAmountData::from($data['remittanceDetails']) : null,
            adminNotes: $data['adminNotes'] ?? null,
            externalReferenceId: $data['externalReferenceId'] ?? null,
            status: $data['status']
        );
    }

    public function toArray(): array
    {
        return [
            'senderInfo' => $this->senderInfo,
            'remittanceDetails' => $this->remittanceDetails?->toArray(),
            'adminNotes' => $this->adminNotes,
            'externalReferenceId' => $this->externalReferenceId,
            'status' => $this->status,
        ];
    }
}
