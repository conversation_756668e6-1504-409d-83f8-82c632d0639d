# HarvestController Documentation

## Overview
The HarvestController is a comprehensive service for managing money remittance claiming operations through third-party providers, primarily Yeah Money. This documentation provides complete technical details about the service architecture, API endpoints, database schema, and integration points.

## Purpose
The HarvestController enables users to:
- Claim money transfers sent through various remittance services
- Validate remittance tracking codes
- Transfer claimed funds to their bank accounts
- Generate transaction receipts
- Track transaction history

## Key Features
- **Multi-provider Support**: Supports various remittance service providers
- **Secure Validation**: Comprehensive account and transaction validation
- **Real-time Processing**: Direct integration with Yeah Money API
- **Audit Trail**: Complete logging of all operations
- **Receipt Generation**: PDF receipt generation for completed transactions
- **Error Handling**: Robust error handling with detailed error codes

## Architecture Components

### Core Components
1. **HarvestController** - Main controller handling HTTP requests
2. **Harvest Model** - Database model for transaction records
3. **HarvestServiceCode Model** - Service provider configuration
4. **Data Classes** - Structured data handling (HarvestRequestData, AccountIdData, etc.)
5. **External Services** - Yeah Money API, OBDX Banking System
6. **Support Services** - Notifications, Logging, Settings

### Key Dependencies
- **Laravel Framework** - Web application framework
- **Spatie Laravel Data** - Data transfer objects
- **Laravel Settings** - Configuration management
- **OBDX Integration** - Banking system integration
- **Yeah Money API** - Third-party remittance service

## Documentation Structure

### 📋 [API Documentation](./API_Documentation.md)
Complete API reference including:
- Endpoint specifications
- Request/response formats
- Validation rules
- Error codes and handling
- Authentication requirements

### 🔧 [Service Documentation](./Service_Documentation.md)
Detailed service implementation covering:
- Class structure and methods
- Business logic flow
- Configuration management
- Security features
- Integration points

### 🗄️ [Database Schema](./Database_Schema.md)
Database design documentation including:
- Table structures and relationships
- Field descriptions and constraints
- JSON data formats
- Indexing strategy
- Query examples

### 📊 Visual Diagrams
Interactive diagrams showing:
- Component relationships
- Data flow sequences
- Database entity relationships

## Quick Start Guide

### 1. List Available Service Codes
```http
GET /digx/harvest/create
Authorization: Bearer {token}
```

### 2. Create Harvest Transaction
```http
POST /digx/harvest
Authorization: Bearer {token}
Content-Type: application/json

{
    "trackingCode": "YM401707304",
    "serviceCode": 1000000,
    "branchId": "401"
}
```

### 3. Confirm Transaction
```http
PUT /digx/harvest/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
    "creditAccountId": {
        "value": "40118341500100010001"
    }
}
```

### 4. Generate Receipt
```http
GET /digx/harvest/receipt/{id}
Authorization: Bearer {token}
```

## Configuration

### Environment Variables
```env
# Yeah Money API Configuration
YEAH_MONEY_URL=https://api.yeahmoney.com
YEAH_MONEY_CLIENT_ID=your_client_id
YEAH_MONEY_CLIENT_SECRET=your_client_secret

# OBDX Banking System
OBDX_URL=your_obdx_url
OBDX_PORT=8080
OBDX_HTTP=https
```

### Service Settings
The service uses Laravel Settings for configuration management:
- Third-party API credentials
- Agent information
- Test/production environment switching
- Token management

## Security Considerations

### Authentication & Authorization
- JWT token-based authentication
- Service-level authorization via AuthorizesServices trait
- User permission validation
- Account ownership verification

### Data Protection
- Sensitive data encryption in settings
- Token hiding in API responses
- Audit logging for all operations
- Secure API communication (HTTPS)

### Validation
- Comprehensive input validation
- Account eligibility verification
- Transaction status checking
- Duplicate prevention

## Error Handling

### Common Error Codes
| Code | Description | Resolution |
|------|-------------|------------|
| DIGX_SWITCH_HARVEST_100 | Validation error | Check request format |
| DIGX_SWITCH_HARVEST_004 | Service/remittance issue | Verify tracking code |
| DIGX_SWITCH_HARVEST_005 | Already paid | Transaction completed |
| DIGX_SWITCH_HARVEST_101 | Transaction expired | Create new transaction |
| DIGX_SWITCH_HARVEST_102 | Invalid account | Use correct account |

### Error Response Format
```json
{
    "status": {
        "result": "ERROR",
        "contextID": "OPERATION_CONTEXT",
        "message": {
            "title": "User-friendly message",
            "detail": "Technical details",
            "code": "ERROR_CODE",
            "type": "ERROR"
        }
    }
}
```

## Performance Optimization

### Database Optimization
- Strategic indexing on frequently queried fields
- JSON field optimization for Oracle and MySQL
- Pagination support for large datasets
- Connection pooling for external services

### Caching Strategy
- Token caching to reduce API calls
- Settings caching for configuration data
- Response caching for static data

### Monitoring
- Comprehensive logging for debugging
- Performance metrics tracking
- Error rate monitoring
- External service health checks

## Testing

### Unit Tests
- Controller method testing
- Model relationship testing
- Data validation testing
- Service integration testing

### Integration Tests
- End-to-end API testing
- External service mocking
- Database transaction testing
- Error scenario testing

## Deployment

### Requirements
- PHP 8.0+
- Laravel 9.0+
- MySQL 8.0+ or Oracle 19c+
- Redis (for caching)
- SSL certificates for HTTPS

### Environment Setup
1. Configure database connections
2. Set up third-party API credentials
3. Configure caching and session storage
4. Set up logging and monitoring
5. Configure SSL certificates

## Maintenance

### Regular Tasks
- Monitor API rate limits
- Update access tokens
- Review error logs
- Performance optimization
- Security updates

### Troubleshooting
- Check external service connectivity
- Verify API credentials
- Review database performance
- Monitor memory usage
- Check log files for errors

## Support

### Documentation Updates
This documentation should be updated when:
- API endpoints change
- Database schema modifications
- New features are added
- Configuration changes
- Error codes are modified

### Contact Information
For technical support or questions about this service:
- Development Team: [<EMAIL>]
- System Administrator: [<EMAIL>]
- Documentation: [<EMAIL>]

---

**Last Updated**: 2025-01-22  
**Version**: 1.0  
**Maintainer**: Development Team
