<?php

namespace App\Console;

use App\Jobs\ProcessUnreceivedWasil;
use App\Jobs\SyncUtilityPayment;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use App\Driver;
use App\DeliveryDistance;
use App\Jobs\ProcessGift;
use DB;
//use Gecche\Multidomain\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        //
    ];
    public function __construct(Application $app, Dispatcher $events)
    {
        if (! defined('ARTISAN_BINARY')) {
            define('ARTISAN_BINARY', '/u01/banky/artisan');
        }

        parent::__construct($app,$events);
    }
    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->job(new ProcessGift,'process')->everyMinute();
        $schedule->job(new ProcessUnreceivedWasil,'batch')->dailyAt("03:00");
        $schedule->job(new SyncUtilityPayment,'batch')->dailyAt("04:00");

        $schedule->command("horizon:snapshot")->everyFiveMinutes();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
