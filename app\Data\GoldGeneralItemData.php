<?php

namespace App\Data;

use <PERSON><PERSON>\LaravelData\Data;
use Spatie\LaravelData\Normalizers\ObjectNormalizer;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\DataCollection;
class GoldGeneralItemData extends GeneralItemData
{
    
   
    public function __construct(
        public ?NameData $messageSouth,
        #[DataCollectionOf(GoldGeneralItemData::class)]
        public ?DataCollection $services,
        public int $appVersion=0,
    ) {
    }

    // protected function exceptProperties() : array
    // {
    //     return [
    //         'id'=>is_null($this->id),
    //         'name'=>is_null($this->name),
    //         'icon'=>is_null($this->icon),
    //         'url'=>is_null($this->url),
    //         'status'=>is_null($this->status),
    //         'enabled'=>is_null($this->enabled),
    //         'message'=>is_null($this->message),
    //         'messageSouth'=>is_null($this->messageSouth),
    //         'appVersion'=>false,
    //         'services'=>false,
    //     ];
    // }
}
