<?php

namespace App\Data;

use Spa<PERSON>\LaravelData\Attributes\Validation\Nullable;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Data;

class ManualHarvestRequestData extends Data
{
    #[Required]
    public string $trackingCode;
    
    #[Required]
    public AccountIdData $targetAccountId;
    
    #[Required]
    public CurrencyAmountData $remittanceCurrency;
    
    #[Nullable]
    public ?string $customerNotes;
    
    #[Nullable]
    public ?string $networkType;

    public static function fromRequest(array $data): self
    {
        return new self(
            trackingCode: $data['trackingCode'],
            targetAccountId: AccountIdData::from($data['targetAccountId']),
            remittanceCurrency: CurrencyAmountData::from($data['remittanceCurrency']),
            customerNotes: $data['customerNotes'] ?? null,
            networkType: $data['networkType'] ?? null
        );
    }

    public function toArray(): array
    {
        return [
            'trackingCode' => $this->trackingCode,
            'targetAccountId' => $this->targetAccountId->toArray(),
            'remittanceCurrency' => $this->remittanceCurrency->toArray(),
            'customerNotes' => $this->customerNotes,
            'networkType' => $this->networkType,
        ];
    }
}
