<?php

namespace App\Data\Form;

use App\Data\BaseNonNullableData;
use App\Data\NameData;
use App\Helpers\Regex\RangeRegex;
use Lang;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Attributes\MapOutputName;

class FormValidationData extends BaseNonNullableData
{
    public function __construct(
        #[MapOutputName('rx'),MapInputName('rx')]
        public ?string $regex,
        #[MapOutputName('msg'),MapInputName('msg')]
        public ?NameData $message,
        #[MapOutputName('tp'),MapInputName('tp')]
        public ?string $type
    ) {
    }

    public static function requiredValidation() : static
    {
        return new static(
            regex:"\\S",
            message:new NameData(
                ar:Lang::get('This field is required', locale: 'ar'),
                en:"This field is required"
            ),
            type:"full"
        );
    }
    public static function rangeValidation(float $min,float $max) : ?static
    {
        $regex=RangeRegex::generate($min,$max);

        if(!is_null($regex)){
            return new static(
                regex:$regex,
                message:new NameData(
                    ar:Lang::get('Minimum amount :min Maximum amount :max', ['min' => $min, 'max' => $max], 'ar'),
                    en:Lang::get('Minimum amount :min Maximum amount :max', ['min' => $min, 'max' => $max],'en')
                ),
                type:"full"
            );
        }
       return null;
    }

}
