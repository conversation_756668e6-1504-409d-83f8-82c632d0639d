<?php

namespace App\Http\Controllers\Digx;

use App\Data\AccountConfigData;
use App\Data\AccountData;
use App\Data\GeneralResponseData;
use App\Enums\CurrencyTypeEnum;
use App\Enums\ServiceTagEnum;
use App\Services\OBDX\CustomerService;
use App\Traits\AuthorizesServices;
use Illuminate\Http\Client\Response;

use App\Http\Controllers\Controller;
use App\LogItem;
use App\Models\Cardless;
use Illuminate\Http\Request;
use App\Services\FlexService;
use App\Services\NotificationService;

use Validator;

class CardlessController extends Controller
{
    use AuthorizesServices;
    protected function getServiceTags(): array{
        return [
            ServiceTagEnum::CARDLESS,
            ServiceTagEnum::CARDLESS_TRANSFER
        ];
    }
    protected function checkAvailable($type){
        switch($type){
            case 'self':
                $this->available([
                    ServiceTagEnum::CARDLESS
                ]);
            case 'other':
                $this->available([
                    ServiceTagEnum::CARDLESS_TRANSFER
                ]);
                break;
        }
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        if($request->filled("type")) {
            $this->checkAvailable($request->type);
        }
        $user=$request->user()->userProfile;
        $cardless=Cardless::select("id","otp","reference_id","data","status","expiry_date")
            ->where(function($query) use($user){
                $query->where("device_key",$user->partyId->value)
                ->orWhere("device_key", $user->userName);
            })
            ->skip( $request->from??0)
            ->take($request->limit??20)
            ->orderBy("id","DESC");

        if($request->filled("type") && $request->type=="self"){
            $cardless=$cardless->whereNull("data->receiverPhone");
        }else if($request->filled("type") && $request->type=="other"){
            $cardless=$cardless->whereNotNull("data->receiverPhone");
        }
        return response()->json($cardless->get());

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return ?\Illuminate\Http\Response
     */
    public function create()
    {
        //
    }
    private function isJson(Response $response){
        return strpos($response->header('Content-Type'),'json') !== false;
    }
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator=validator()->make($request->all(),[
            'debitAccountId.value'=>"required|max:20|min:20",
            'amount.amount'=>"required|numeric",
            'amount.currency'=>"required"
        ]);
        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-CARDLESS",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_CARDLESS_100",
                        "type"    => "ERROR"
                    ]
                ]
            ]));
        }

        //$accoutId=AccountIdData::from($request->debitAccountId);
        $user=$request->user()->userProfile;

        $valid=false;
        $message="Can't load your accounts!";
        $result=CustomerService::account($request,$request->input("debitAccountId.value"));
        if($result instanceof AccountData){
            $account=$result;
            $multiples=$account->currencyId==CurrencyTypeEnum::YER->value?1000:100;
            $maximum=$account->currencyId==CurrencyTypeEnum::YER->value?30000:500;

            if($request->input("amount.amount")%$multiples!=0){
                $message="The amount must be multiples of"." ".number_format($multiples,0);
            }else if($request->input("amount.amount")>$maximum){
                $message=__("Maximum allowed amount")." ".number_format($maximum,0);
            }else if($request->input("amount.amount")>$account->balance->amount){
                $message="You don't have enough balance in your account!";
            }else{
                $valid=$account->status=="ACTIVE" && $account->isCash() && $account->currencyId==$request->input("amount.currency") &&
                $account->allowedService(AccountConfigData::cardless);
            }
        }

        if(!$valid){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-CARDLESS",
                    "message"   => [
                        "title"   => __($message),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_CARDLESS_102",
                        "type"    => "ERROR"
                    ]
                ]
            ]));
        }


        if($user->phoneNumber->value==($request->receiverPhone??null)){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "",
                    "message"   => [
                        "title"   => __("Can't send cardless to the same phone number as your account!"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_CARDLESS_103",
                        "type"    => "ERROR"
                    ]
                ]
            ]));
        }
        $userId=$user->partyId->value;
        $cardless=new Cardless();
        LogItem::store($cardless);

        $cardless->payment_id= $this->generateRandomString(null);
        $cardless->device_key= "$userId";//$request->header("deviceKey");
        //$cardless->token=  $request->header("token");
        $cardless->otp= Controller::generateNumericOTP(6);
        $cardless->expiry_date= \Carbon\Carbon::now()->addMinutes(10)->toDateTimeString();
        if(!is_null($request->receiverPhone)){
            $cardless->expiry_date= \Carbon\Carbon::now()->addDays(1)->toDateTimeString();
            $this->checkAvailable('other');
        }else{
            $this->checkAvailable('self');
        }
        $cardless->data= $request->all();
        $cardless->save();

        $object=new \stdClass();
        $object->id=  $cardless->id;
        $object->account_id=  $request->input("debitAccountId.value");
        $object->amount= $request->input("amount.amount");
        $object->currency= $request->input("amount.currency");
        $object->phone= $user->phoneNumber->value;
        $object->receiverPhone= $request->receiverPhone??null;
        $object->receiverName= new \stdClass();
        $object->receiverName->first=$request->input("receiverName.first")??null;
        $object->receiverName->second=$request->input("receiverName.second")??null;
        $object->receiverName->third=$request->input("receiverName.third")??null;
        $object->receiverName->last=$request->input("receiverName.last")??null;
        $object->zone_id= $account->isSouth()?2:1;

        $object->otp= $cardless->otp;
        $object->remark=$request->note;
        // if($request->filled("note")){
        //     $object->remark.= " [". $request->note."]";
        // }
        $object->expiry_date= \Carbon\Carbon::createFromFormat("Y-m-d H:i:s",$cardless->expiry_date)->format("m/d/Y g:i:s A");//"1/23/2022 8:43:05 PM"

        $result=FlexService::cardlessInit($object);
        if($result->status->message->code!="0"){
            return response()->json($result,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);
        }
        $cardless->reference_id=$result->getAdditionalData()["external_reference_id"];
        $cardless->save();

        if(!is_null($object->receiverPhone)){
            $smsMessage=sprintf(trans("cardless_sms_message_reciever"),
                "ATM YKB",
                $cardless->reference_id,
                $request->input("amount.amount")." ".$request->input("amount.currency"),
                $cardless->expiry_date
            );

            NotificationService::sendSMS([
                "mobile"=> $object->receiverPhone,
                "message"=> $smsMessage
            ]);
            // if($result->status->message->code!="0"){
            //     // $cardless->status= -1;
            //     //$cardless->save();

            //     return response()->json($result);
            // }
            $smsMessage=sprintf(trans("cardless_sms_message_sender"),
                "ATM YKB",
                $request->input("amount.amount")." ".$request->input("amount.currency"),
                $cardless->otp,
                $cardless->expiry_date
            );
        }else{
            $smsMessage=sprintf(trans("cardless_sms_message"),
                "ATM YKB",
                $cardless->reference_id,
                $request->input("amount.amount")." ".$request->input("amount.currency"),
                $cardless->otp,
                $cardless->expiry_date
            );
        }


        // NotificationService::sendSMS([
        //     "mobile"=> $user->phoneNumber->value,
        //     "message"=> $smsMessage
        // ]);
        //if($result->status->message->code=="0"){
            $cardless->status= 1;
            $cardless->save();

            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "SUCCESSFUL",
                    "contextID" => "$cardless->id",
                    "message"   => [
                        "title"   => $smsMessage,
                        "detail"  => $smsMessage,
                        "code"    => "0",
                        "type"    => "INFO"
                    ]
                ]
            ]));
        //}

        return response()->json($result);
    }

    function generateRandomString($dataSet,$length = 10) {
        $characters = $dataSet??'0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }
        return $randomString;
    }
    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Cardless  $cardless
     * @return ?\Illuminate\Http\Response
     */
    public function show(Cardless $cardless)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Cardless  $cardless
     * @return ?\Illuminate\Http\Response
     */
    public function edit(Cardless $cardless)
    {
        //
    }

}
