<?php

namespace App\Http\Middleware;

use Closure;
use Symfony\Component\HttpFoundation\Request;
class WebApp{
    protected $appsecret="OYIMFGDzOQ3BjkjdlAbhNoW54eYLX8LegE3qQsP";
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string[]  ...$guards
     * @return mixed
     *
     * @throws \Illuminate\Auth\AuthenticationException
     */
    public function handle($request, Closure $next, ...$guards)
    {
       // dd($request->path());
       // $isEncrypted=$this->isEecrypt($request);
        if($request->filled("appsecret") && $request->appsecret==$this->appsecret){
            session()->put("appsecret",$request->appsecret);
            $response=$next($request);
            return $response;
        }else if(session()->has("appsecret") && session()->get("appsecret")==$this->appsecret){
            session()->put("appsecret",$request->appsecret);
            $response=$next($request);
            return $response;
        }else{
            abort(\Symfony\Component\HttpFoundation\Response::HTTP_NOT_FOUND);
        }
    }

        /**
     * Decrypt the cookies on the request.
     *
     * @param  \Symfony\Component\HttpFoundation\Request  $request
     * @return bool
     */
    // protected function isEecrypt(Request $request)
    // {
    //     foreach ($request->cookies as $key => $cookie) {
    //         if ($this->isDisabled($key)) {
    //             continue;
    //         }

    //         try {
    //             $value = $this->decryptCookie($key, $cookie);
    //         } catch (DecryptException) {
    //             return false;
    //         }
    //     }

    //     return true;
    // }
}
