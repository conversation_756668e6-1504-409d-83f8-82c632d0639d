<?php

namespace App\Data\OBDX\BillPayment;

use App\Data\AccountIdData;
use App\Data\BaseNonNullableData;
use App\Data\CurrencyAmountData;
use Arr;
use Spatie\LaravelData\Attributes\MapInputName;


class BillPaymentData extends BaseNonNullableData
{
    public function __construct(
        public ?string $id,
        #[MapInputName('reference_id')]
        public ?string $referenceId,
        public ?string $title,
        public ?int $service,
        public ?int $item,
        public ?int $bundle,
        public ?string $subscriberNumber,
        public ?AccountIdData $debitAccountId,
        public ?CurrencyAmountData $amount,
        public ?CurrencyAmountData $fee,
        public ?string $remarks,
        public ?object $payload,
        public ?object $extra,
        public ?int $status,
        #[MapInputName('created_at')]
        public ?string $date
    ) {
    }

    public static function prepareForPipeline(array $properties) : array
    {
        $locale=app()->getLocale();
        $properties['subscriberNumber']= data_get($properties,"subscriberNumber", data_get($properties,"subscriber_number"));
        $properties['debitAccountId']= data_get($properties,"debitAccountId",data_get($properties,"debit_account_id"));
        $properties['title']= data_get($properties,"bundle.title.$locale",data_get($properties,"item.title.$locale"));
        $properties['item']= data_get($properties,"item.id",data_get($properties,"item"));
        $properties['bundle']= data_get($properties,"bundle.id",data_get($properties,"bundle"));


        // Cast payload and extra to objects if they are arrays
        foreach (['payload', 'extra'] as $key) {
            if (isset($properties[$key]) && is_array($properties[$key])) {
                $properties[$key] = (object) $properties[$key];
            }
        }

        return $properties;
    }
    public static function shortFormat( $array=null)
    {
        $array['service']= data_get($array,"se");
        $array['item']= data_get($array,"im");
        $array['bundle']= data_get($array,"be");
        $array['subscriberNumber']= data_get($array,"sr");
        $array['amount']= [
            "amount"=>data_get($array,"at.at"),
            "currency"=>data_get($array,"at.cy")
        ];
        $array['debitAccountId']= [
            "value"=>data_get($array,"dd.ve",data_get($array,"dd")),
            //"currency"=>data_get($array,"at.cy")
        ];

        foreach (['pd'=>'payload', 'ea'=>'extra'] as $key=>$value) {
            if (isset($array[$key]) && is_array($array[$key])) {
                $array[$value] = (object) static::shortFormatObject($value,$array[$key]);
            }
        }
       // return $array;
        return static::from($array);
    }
    public static function shortFormatObject($type,array $properties): array
    {
        $array=[];
        if($type=="payload"){
            if(Arr::has($properties,"rn")){
                $array['region']= $properties["rn"];
            }
            if(Arr::has($properties,"br")){
                $array['biller']= $properties["br"];
            }
            if(Arr::has($properties,"by")){
                $array['billerCategory']= $properties["by"];
            }
            if(Arr::has($properties,"lk")){
                $array['link']= $properties["lk"];
            }
            if(Arr::has($properties,"fr")){
                $array['filter']= $properties["fr"];
            }
            if(Arr::has($properties,"se")){
                $array['subscriberType']= $properties["se"];
            }
            if(Arr::has($properties,"te")){
                $array['type']= $properties["te"];
            }
            if(Arr::has($properties,"cy")){
                $array['city']= $properties["cy"];
            }
            if(Arr::has($properties,"ld")){
                $array['lend']= $properties["ld"];
            }
        }else if($type=="extra"){
            if(Arr::has($properties,"bn")){
                $array['billno']= $properties["bn"];
            }
            if(Arr::has($properties,"sc")){
                $array['sac']= $properties["sc"];
            }
        }


        return $array;
    }
    public function toDB(): array
    {
        $dataArray = $this->only('service','item','bundle','amount','fee','remarks','payload','extra')->toArray();
        $dataArray+=[
            'reference_id'=>$this->referenceId,
            'subscriber_number'=>$this->subscriberNumber,
            'debit_account_id'=>$this->debitAccountId,
        ];

        return $dataArray;
    }
}
