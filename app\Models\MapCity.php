<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MapCity extends Model
{
    use HasFactory;
    protected $connection= 'map';

    protected $table = 'cities';
    public function districts()
    {
        return $this->hasMany('App\Models\MapDistrict', "cities_id");
    }

    public function entries()
    {
        return $this->hasMany('App\Models\MapEntry', "city_id");
    }
}
