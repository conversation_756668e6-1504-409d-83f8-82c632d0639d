<?php
namespace Database\Seeders;

use App\Enums\CurrencyTypeEnum;
use App\Models\ExchangeRate;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use DB;
class ExchangeRateTableSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        $equlivent=31.1;
        ExchangeRate::where('currency1',CurrencyTypeEnum::G24->value)
        ->update([
            'buy_rate' => DB::raw("buy_rate*$equlivent"),
            'sell_rate' => DB::raw("sell_rate*$equlivent"),
            'buy_difference' => DB::raw("buy_difference*$equlivent"),
            'sell_difference' => DB::raw("sell_difference*$equlivent"),
        ]);
    }
}
