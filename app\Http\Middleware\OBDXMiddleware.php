<?php

namespace App\Http\Middleware;

use App\Data\GeneralResponseData;
use App\Data\StatusData;
use App\Services\OBDX\CustomerService;
use App\Services\OBDX\LoginService;
use Closure;

class OBDXMiddleware
{

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string[]  ...$guards
     * @return mixed
     *
     * @throws \Illuminate\Auth\AuthenticationException
     */
    public function handle($request, Closure $next, ...$guards)
    {
        if (auth('digx')->check()) {
            auth()->shouldUse('digx');

            //if($request->hasHeader("X-CHALLENGE-RESPONSE")){
                $response=LoginService::verify($request,$next);
                if(!is_null($response)){
                    return $response->setContent($next($request)->getContent());
                }
               // return;
            //}
            return $next($request);
        }
        return response()->json(StatusData::from([
            "result"    => "SUCCESSFUL",
            "contextID" => "MIDDLEWARE",
            "message"   => [
                "title"   => __("System cannot process the request currently. Please try later."),
                "detail"  => "Middleware",
                "code"    => "DIGX_PROD_DEF_0000",
                "type"    => "ERROR",
                "relatedMessage"=> [
                    [
                        "detail"=> "Access denied.",
                        "code"=> "FC_SM_025"
                    ]
                ],
            ]
        ]),\Symfony\Component\HttpFoundation\Response::HTTP_UNAUTHORIZED);
    }

}
