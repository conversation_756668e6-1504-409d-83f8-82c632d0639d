<?php

namespace App\Data\Wheel;

use App\Data\CurrencyAmountData;
use App\Models\CustomerType;
use App\Models\WheelTransaction;
use Spatie\LaravelData\Data;

class WheelConfigData extends Data
{

    public function __construct(
        public ?int $allowSpinningWheel=0,
        public ?CurrencyAmountData $dailyMaxAmount=null,
        public ?int $allowAccumulativeAmountThroughMonth=0,
        public ?int $autoChangeProbability=0
    ) {
    }

    public function toApp(object &$appConfig,bool $haveNorthAccount):object {
        $appConfig->allowSpinningWheel=$this->allowSpinningWheel &&
            $haveNorthAccount &&
            (request()->header('appVersion')??0)>=203 &&
            auth()->user()->customerType==CustomerType::RETAIL
            ?1:0;

        $appConfig->spinningWheelCounts=WheelTransaction::getCustomerWheelCounts();
        if(!WheelTransaction::isCustomerTryWheelAfterDate()){
            $appConfig->spinningWheelCounts+=1;
        }

        return $appConfig;
    }

}
