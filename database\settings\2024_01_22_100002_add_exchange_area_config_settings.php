<?php

use App\Enums\CurrencyTypeEnum;
use App\Models\CustomerType;
use <PERSON><PERSON>\LaravelSettings\Migrations\SettingsMigration;

class AddExchangeAreaConfigSettings extends SettingsMigration
{
    public function up(): void
    {
        $this->migrator->add('config.exchangeAreaConfig',array (
            'areas' => [
                "lowAreas",
                "aden",
                "taiz",
                "mukalla",
                "mareb"
            ],
            'areaBranches' => [
                'lowAreas' => [
                    "402",//sanaa
                    "404",//hodeidah
                    "407",//taizStreet
                    "408",//ibb
                    "409",//rabatStreet
                    "410",//rawda
                    "411",//shoob
                    "412",//alHuban
                    "413",//fiftyStreet
                    "415",//hada
                    "416",//libyan
                    "417",//sixtyStreet
                    "418",//dhamar
                    "419",//al<PERSON><PERSON><PERSON>
                    "420",//omran
                    "421",//hajjah
                    "422",//bayt<PERSON><PERSON>aq<PERSON>
                    "423",//almuhafazaOffice
                    "424",//sawalOffice
                    "502",//sanaaIslamic
                    "504",//taizStreetIslamic
                    "505"//sadaIslamic
                ],
                'aden' => [
                    "403",//aden
                    "414"//sheikhOthman
                ],
                'taiz' => [
                    "405"//taiz
                ],
                'mukalla' => [
                    "406"//mukalla
                ],
                'mareb' => [
                    "506"//marebIslamic
                ],
            ]
        ));

    }

    public function down()
    {
        $this->migrator->delete('config.exchangeAreaConfig');
    }
}
