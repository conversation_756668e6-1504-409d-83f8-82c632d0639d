<?php

namespace App\Data\UP\Query;

use App\Data\AccountIdData;
use App\Data\BaseNonNullableData;
use App\Data\CurrencyAmountData;
use App\Data\StatusData;
use Carbon\Carbon;
use Spatie\LaravelData\Attributes\MapInputName;
use <PERSON><PERSON>\LaravelData\Attributes\MapOutputName;
use Spatie\LaravelData\Attributes\WithCast;
use Spatie\LaravelData\Attributes\WithCastAndTransformer;
use Spatie\LaravelData\Attributes\WithTransformer;
use Spatie\LaravelData\Casts\DateTimeInterfaceCast;
use Spatie\LaravelData\Transformers\DateTimeInterfaceTransformer;


class BillPaymentQueryOfferData extends BaseNonNullableData
{
    public function __construct(
        #[MapInputName('offer_id')]
        public ?string $id = null,
        #[MapInputName('offer_name')]
        public ?string $name = null,
        #[MapInputName('amt')]
        public ?float $amount = null,
        #[MapInputName('exp_date'),WithCast(DateTimeInterfaceCast::class, "YmdHis"),WithTransformer(DateTimeInterfaceTransformer::class, format: "Y-m-d H:i:s")]
        public ?Carbon $expiryDate = null,
        #[MapInputName('eff_date'),WithCast(DateTimeInterfaceCast::class, "YmdHis"),WithTransformer(DateTimeInterfaceTransformer::class, format: "Y-m-d H:i:s")]
        public ?Carbon $issueDate = null,
    ) {
    }

    public static function prepareForPipeline(array $properties) : array
    {
        $properties['exp_date']= str_replace("(","",str_replace(")","",data_get($properties,"exp_date")));
        $properties['eff_date']= str_replace("(","",str_replace(")","",data_get($properties,"eff_date")));
        return $properties;
    }

}
