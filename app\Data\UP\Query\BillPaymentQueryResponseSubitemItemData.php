<?php

namespace App\Data\UP\Query;

use App\Data\BaseNonNullableData;
use App\Data\NameData;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\DataCollection;

class BillPaymentQueryResponseSubitemItemData extends BaseNonNullableData
{
    public function __construct(
        public ?NameData $title=null,
        public ?NameData $subtitle=null,

        public ?string $type=null,
        #[DataCollectionOf(BillPaymentQueryResponseSubitemItemData::class)]
        public ?DataCollection $items=null,

        public ?string $backgroundColor=null,
        public ?string $foregroundColor=null,
    ) {
    }
    //public ?BillPaymentQueryResponseSubitemItemData $foregroundColor;

}
