<?php

namespace App\Data\UP\Query;

use App\Data\AccountIdData;
use App\Data\BaseNonNullableData;
use App\Data\CurrencyAmountData;
use App\Data\Form\FormBaseData;
use App\Data\StatusData;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\DataCollection;


class BillPaymentQueryResponseData extends BaseNonNullableData
{
    public function __construct(
        public ?StatusData $status=null,
        public ?array $filled=null,
        public ?array $config=null,
        #[DataCollectionOf(FormBaseData::class)]
        public ?DataCollection $fields=null,
        #[DataCollectionOf(BillPaymentQueryResponseItemData::class)]
        public ?DataCollection $items=null,
    ) {
    }
}
