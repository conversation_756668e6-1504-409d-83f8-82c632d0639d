<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\UserInterface;
use Spatie\Permission\Models\Permission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;

use DB;
use Auth;

class PermissionsController extends Controller
{

    protected $list = ['*' => 'الكل', 'list' => 'عرض القائمة', 'view' => 'عرض التفاصيل', 'create' => 'انشاء', 'edit' => 'تعديل', 'delete' => 'حذف'];
    public function __construct(Request $request)
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of Permission.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index(Request $request, $level = 0)
    {
    }

    /**
     * Show the form for creating new Permission.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function create($name = "0")
    {
        if (!Auth::user()->hasRole('developer'))
            return abort(401);

        return view('default.admin.permissions.create')
        ->with('list', $this->list)
        ->with('name', $name);
    }

    /**
     * Store a newly created Permission in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        if (!Auth::user()->hasRole('developer'))
            return abort(401);

        if ($request->exists('is_parent'))
            $this->validate($request, [
                'parent_name' => 'required|max:50',
                'name' => 'required|max:50',
            ]);
        else
            $this->validate($request, [
                'name' => 'required|max:50'
            ]);

        $name = "";
        if ($request->exists('is_parent')) {
            $name = $request->parent_name . '.';
            UserInterface::create([
                'name' => $request->parent_ui_name,
                'identifier' => $request->parent_name,
                'route' => $request->parent_route,
                'icon' => $request->parent_icon,
                'status' => $request->parent_status,
                'sort' => $request->parent_sort,
                'guard' => $request->parent_guard??null,
            ]);
        } else if ($request->filled('pname') && $request->pname != '0') {
            $name = $request->pname . '.';
        }
        $name .= $request->name;
        UserInterface::create([
            'name' => $request->ui_name,
            'identifier' => $name,
            'route' => $request->route,
            'icon' => $request->icon,
            'status' => $request->status,
            'sort' => $request->sort,
            'guard' => $request->guard??null,
        ]);

        foreach ($this->list as $key => $value) {
            Permission::create([
                'name' => "$name.$key",
                'guard_name'=>$request->guard
            ]);
        }


        $subnames = array_keys($this->list);
        foreach ($request->subpermissions as $subpermission) {
            if (!in_array($subpermission, $subnames))
                Permission::create([
                    'name' => "$name.$subpermission",
                    'guard_name'=>$request->guard
                ]);
        }

        return redirect($request->input('url') != null ? $request->input('url') : '/admin/permissions/0');
    }


    /**
     * Show the form for editing Permission.
     *
     * @param  string  $name
     * @return \Illuminate\Contracts\View\View
     */
    public function edit($name)
    {
        if (!Auth::user()->hasRole('developer'))
            return abort(401);

        $count = count(explode('.', $name));
        $permission = UserInterface::select(
            "name as ui_name", "route", "icon", "status", "sort",
            DB::raw("identifier"),
            DB::raw("SUBSTRING_INDEX(SUBSTRING_INDEX(identifier,'.',$count-3),'.',1) as name"),
            'guard'
        )
        ->whereRaw("SUBSTRING_INDEX(identifier,'.',$count)='$name'")
        ->first();

        $permission->subname=Permission::select(DB::raw("DISTINCT concat('0;' , SUBSTRING_INDEX(SUBSTRING_INDEX(name,'.',$count+1),'.',-1)) as name"))
            ->whereRaw("SUBSTRING_INDEX(name,'.',?)=?",[$count,$permission->identifier])
            ->pluck('name')
            ->join(',');

        $user_interfaces = UserInterface::pluck('name', 'identifier')->toArray();

        return view('default.admin.permissions.edit', compact('user_interfaces'))->with('permission', $permission)->with('list', $this->list)->with('name', $name);
    }

    /**
     * Update Permission in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $name
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $name)
    {
        if (!Auth::user()->hasRole('developer'))
            return abort(401);

        $names = explode('.', $name);
        $count = count($names);
        $request->name = str_replace($names[$count - 1], $request->name, $name);

        Permission::whereRaw("SUBSTRING_INDEX(name,'.',$count)='$name'")
        ->update([
            'name' => DB::raw("concat(concat('" . $request->name . "','.'),SUBSTRING_INDEX(name,'.'," . (in_array('*', $request->subpermissions) ? '-1' : '-2') . "))"),
            'guard_name'=> $request->guard
        ]);
        UserInterface::whereRaw("identifier='$name'")
            ->update([
                'name' => $request->ui_name,
                'identifier' => $request->name,
                'route' => $request->route,
                'icon' => $request->icon,
                'status' => $request->status,
                'sort' => $request->sort,
                'guard' => $request->guard,
            ]);
        UserInterface::withCacheUpdate()->whereRaw("SUBSTRING_INDEX(identifier,'.',$count)='$name'")
        ->whereRaw("identifier!='$request->name'")
        ->update([
            'identifier' => DB::raw("concat(concat('" . $request->name . "','.'),SUBSTRING_INDEX(identifier,'.','-1'))")
        ]);


        $subnames = Permission::select(DB::raw("SUBSTRING_INDEX(SUBSTRING_INDEX(name,'.',$count+1),'.',-1) as subname"))
        ->whereRaw("SUBSTRING_INDEX(name,'.',$count)='$name'")
        ->groupBy(DB::raw("SUBSTRING_INDEX(SUBSTRING_INDEX(name,'.',$count+1),'.',-1)"))
        ->get()->pluck('subname')->toArray();

        if (in_array('*', $subnames)) {
            foreach ($request->subpermissions as $subpermission) {
                if (!in_array($subpermission, $subnames))
                    Permission::create([
                        'name' => "$name.$subpermission",
                        'guard_name'=>$request->guard
                    ]);
            }

            if (Auth::user()->hasRole('developer'))
                Permission::whereRaw("SUBSTRING_INDEX(name,'.',$count)='$name'")
                    ->whereRaw("SUBSTRING_INDEX(SUBSTRING_INDEX(name,'.',$count+1),'.',-1) not in ('" . join("','", $request->subpermissions) . "')")
                    ->delete();
        }
        return redirect($request->input('url') != null ? $request->input('url') : '/admin/permissions/0');
    }


    /**
     * Remove Permission from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($name)
    {
        if (!Auth::user()->hasRole('developer'))
            return abort(401);

        $names = explode('.', $name);
        $count = count($names);

        Permission::whereRaw("SUBSTRING_INDEX(name,'.',$count)='$name'")
        ->delete();

        UserInterface::withCacheUpdate()
        ->whereRaw("SUBSTRING_INDEX(identifier,'.',$count)='$name'")
        ->delete();

        app(\Spatie\Permission\PermissionRegistrar::class)
        ->forgetCachedPermissions();
        return back()->with('success', __("Operation accomplished successfully"));
    }


    public function show(Request $request, $level = 0)
    {
        // $permissions = Permission::pluck('name');
        // dd($permissions->toArray());
        // return;
        if ($level > 1 || !Auth::user()->hasRole('developer'))
            return abort(401);

        $prefix = DB::getTablePrefix();
        $name = $request->name ?? "";
        // $permissions = Permission::select(
        //     "user_interfaces.sort as sort",
        //     "user_interfaces.name as ui_name",
        //     DB::raw("SUBSTRING_INDEX({$prefix}permissions.name,'.',$level+1) as identify_name"),
        //     DB::raw("max(SUBSTRING_INDEX(SUBSTRING_INDEX({$prefix}permissions.name,'.',$level-3),'.',1)) as name"),
        //     DB::raw("group_concat(DISTINCT SUBSTRING_INDEX(SUBSTRING_INDEX({$prefix}permissions.name,'.',$level+2),'.',-1) SEPARATOR ',') as subname")
        // )
        // ->leftJoin('user_interfaces', 'user_interfaces.identifier', '=', DB::raw("SUBSTRING_INDEX({$prefix}permissions.name,'.',$level+1)"))
        // ->groupBy(DB::raw("SUBSTRING_INDEX({$prefix}permissions.name,'.',$level+1)"));


        $permissions =UserInterface::with('subNames')->select(
            DB::raw("Max(sort) as sort"),
            DB::raw("Max(name) as ui_name"),
            "identifier",
            DB::raw("Max(SUBSTRING_INDEX(identifier,'.',$level+1)) as identify_name"),
            DB::raw("Max(SUBSTRING_INDEX(SUBSTRING_INDEX(identifier,'.',$level-2),'.',1)) as name")
            //DB::raw("group_concat(DISTINCT SUBSTRING_INDEX(SUBSTRING_INDEX({$prefix}permissions.name,'.',$level+2),'.',-1) SEPARATOR ',') as subname")
        )

        ->groupBy(DB::raw("SUBSTRING_INDEX(identifier,'.',$level+1)"))->groupBy('identifier');

        if (!$request->filled("name")){
            $permissions = $permissions->where('identifier', 'NOT LIKE', "%.%");
        }
        if ($level != 0){
            $permissions = $permissions->whereRaw("SUBSTRING_INDEX(identifier,'.',$level)='$name'")->where('identifier','<>',$name);
        }

        $permissions = $permissions->orderBy('sort')->paginate(25); //->get();//$permissions->pluck('subname','name');
        $user_interfaces = UserInterface::pluck('name', 'identifier')->toArray();
        // dd($permissions->toArray());
       // return;
        return view('default.admin.permissions.index', compact('permissions', 'user_interfaces'))
        ->with('list', $this->list)
        ->with('level', $level + 1)
        ->with('name', $name);
    }
    /**
     * Delete all selected Permission at once.
     *
     * @param Request $request
     */
    public function massDestroy(Request $request)
    {
        if (!Auth::user()->hasRole('developer'))
            return abort(401);
        /*if (! Auth::user()->canAny(['management.permission.*','management.permission.delete'])) return abort(401);*/
        Permission::whereIn('id', request('ids'))->delete();
        app(\Spatie\Permission\PermissionRegistrar::class)->forgetCachedPermissions();
        return response()->noContent();
    }

}
