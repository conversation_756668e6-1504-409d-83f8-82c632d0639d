<?php

namespace App\Http\Controllers\Digx;
use App\Data\AccountConfigData;
use App\Data\AccountData;
use App\Data\AccountIdData;
use App\Data\GeneralResponseData;
use App\Data\OBDX\Transfer\ExternalTransferData;
use App\Data\OBDX\Transfer\TransferData;
use App\Data\ReceiptData;
use App\Data\OBDX\Transfer\DomesticTransferData;
use App\Data\OBDX\Transfer\InternalTransferData;
use App\Data\OBDX\Transfer\SelfTransferData;
use App\Enums\InvoiceTransactionsTypeEnum;
use App\Enums\ServiceTagEnum;
use App\Enums\TransactionStatusEnum;
use App\LogItem;
use App\Models\Invoice;
use App\Models\InvoiceTransaction;
use App\Models\Transfer;
use App\Services\OBDX\AdminService;
use App\Services\OBDX\CustomerService;
use App\Http\Controllers\Controller;
use App\Traits\AuthorizesServices;
use Illuminate\Http\Request;

class TransferFundController extends Controller
{
    use AuthorizesServices;
    protected function getServiceTags(): array{
        return [
            ServiceTagEnum::MY_ACCOUNTS_TRANSFER,
            ServiceTagEnum::MONEY_TRANSFER,
            ServiceTagEnum::YKP_PAYMENT,
            ServiceTagEnum::DOMESTIC
        ];
    }

    protected function checkAvailable($type){
        switch($type){
            case 'EXTERNALFT':
               // ServiceTagEnum::TRANSACTIONS
                $this->available([
                    ServiceTagEnum::YKP_PAYMENT,
                ]);
                break;
            case 'INDIADOMESTICFT':
                $this->available([
                    ServiceTagEnum::DOMESTIC,
                ]);
                break;
            case 'INTERNALFT':
                $this->available([
                    ServiceTagEnum::MONEY_TRANSFER,
                ]);
                break;
            case 'SELFFT':
                $this->available([
                    ServiceTagEnum::MY_ACCOUNTS_TRANSFER,
                ]);
                break;
        }
    }
    /**
     * Display a listing of the resource.
     *
     * @return ?\Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $validator=validator()->make($request->all(),[
            'type' => 'required|in:INDIADOMESTICFT,INTERNALFT,SELFFT,EXTERNALFT',
            'bankCode' => 'Nullable|string',
            'service' => 'Nullable|string|in:transfer,payment',
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "INDEX-TRANSFER-FUND",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_TRANSFER_001",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        $transfers=Transfer::
        where('type',$request->type)
        ->skip( $request->from)
        ->take($request->limit)
        ->orderBy("created_at","DESC");

        if($request->type=="INDIADOMESTICFT"){
            if($request->filled('bankCode')){
                $transfers=$transfers->where('payload->bankCode',$request->bankCode);
            }else{
                $transfers=$transfers->whereNotIn('payload->bankCode',['OILP']);
            }
        }
        if($request->type=="INDIADOMESTICFT" && $request->filled('service')){
            $transfers=$transfers->where('payload->service',$request->service);
        }

        if($request->filled('text')){
            $transfers=$transfers->where(function($query) use ($request) {
                $query->where('payload->receiverNumber', 'like', '%' . $request->text . '%')
                      ->orWhere('payload->receiverName', 'like', '%' . $request->text . '%');
            });
        }
        $transfers=$transfers->get();
        //return response()->json( $transfers);
        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])
        ->additional([
            "transactions"=>TransferData::collect($transfers->toArray())
        ])->toArray());
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return ?\Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return ?\Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        switch($request->paymentType??''){
            case 'EXTERNALFT':
                $invoice=Invoice::where("id",$request->invoiceId)
                ->where("status",1)
                ->first();

                if(is_null($invoice)){
                    return response()->json(GeneralResponseData::from([
                        'status'=>[
                            "result"    => "ERROR",
                            "contextID" => "STORE-TRANSFER-FUND",
                            "message"   => [
                                "title"   => __("Invalid invoice ID."),
                                "detail"  => "",
                                "code"    => "DIGX_SWITCH_TRANSFER_001",
                                "type"    => "ERROR"
                            ]
                        ]
                    ]));
                }

                $debitAccountId=AccountIdData::from($request->debitAccountId);
                $creditAccountId=AccountIdData::from($invoice->debit_account_id);

                $valid=false;
                $message="You don't have enough balance in your account!";
                $result=CustomerService::account($request, $debitAccountId->value);
                if($result instanceof AccountData){
                    $account=$result;
                    if($account->status!="ACTIVE"){
                        $message="Your account not active!";
                    }else if(!$account->allowedService(AccountConfigData::ykpPayment)){
                        $message="This account not allowed to use this service!";
                    }else if(!$account->isCash()){
                        $message="Account is not cash!";
                    }else if($account->currencyId!=$invoice->amount->currency){
                        $message="Account currency not match!";
                    }else if($account->balance->amount<$invoice->amount->amount){
                        $message="You don't have enough balance in your account!";
                    }else if(!(
                        ($account->currencyId==\App\Enums\CurrencyTypeEnum::YER->value && $account->isNorth()==$creditAccountId->isNorth()) ||
                        $account->currencyId!=\App\Enums\CurrencyTypeEnum::YER->value
                    )){
                        $message="Merchant account not accept this payment from this currency account!";
                    }else{
                        $valid=true;
                    }
                }

                if(!$valid){
                    return response()->json(GeneralResponseData::from([
                        'status'=>[
                            "result"    => "ERROR",
                            "contextID" => "STORE-INVOICE",
                            "message"   => [
                                "title"   => __($message),
                                "detail"  => "",
                                "code"    => "DIGX_SWITCH_TRANSFER_001",
                                "type"    => "ERROR"
                            ]
                        ]
                    ]));
                }

                $result=CustomerService::postExternalTransefer([
                    "transferDetails"=> [
                        "merchantCode"=> $invoice->initiator_id,
                        "transactionAmount"=> [
                            "amount"=> $invoice->amount->amount,
                            "currency"=> $invoice->amount->currency
                        ],
                        "serviceCharges"=> [
                            "amount"=> 0,
                            "currency"=> "YER"
                        ],
                        "partyId"=> null,
                        "amount"=> [
                            "amount"=> $invoice->amount->amount,
                            "currency"=> $invoice->amount->currency
                        ],
                        "remarks"=> $invoice->remarks,
                        "status"=> "INT",
                        "userReferenceNo"=> $invoice->order_id,
                        "debitAccountId"=> $request->debitAccountId
                    ],
                    "epiRefId"=> $invoice->purpose
                ]);

                $invoice->external_reference_id= $result->paymentId;
                $invoice->save();

                return response()->json($result);
            }

    }

    /**
     * Display the specified resource.
     *
     * This method generates a PDF document with the title "Hello World" and content "Hello World".
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Transfer $transfer
     * @return void
     */
    public function show(Request $request,Transfer $transfer)
    {
        $validator=validator()->make($request->all(),[
            'showAccountInfo' => 'Nullable|numeric|in:0,1',
            'showAccountNumber' => 'Nullable|numeric|in:0,1',
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "INDEX-TRANSFER-FUND",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_TRANSFER_001",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        $this->generateReceipt($this->getReceiptData($transfer));
    }
    protected function getReceiptData(Transfer $transfer): ReceiptData
    {
        $transferData=TransferData::from($transfer->toArray());
        $beneficiary=is_null($transferData->receiverName)?
                        $transferData->receiverNumber:
                        html_entity_decode($transferData->receiverName).' ('.$transferData->receiverNumber.')';

        $statement=is_null($transferData->bankCode)?
                        "":
                        __("through").' ('.__($transferData->bankCode). ')';

        return ReceiptData::from([
            "id"=> $transferData->id,
            "date"=> date_format(date_create($transferData->date), "Y-m-d H:i:s"),
            "title"=> __("Funds transfer") ." (".__($transferData->type).")",
            "beneficiary"=> $beneficiary,
            "statement"=> __("Funds transfer") ." (".__($transferData->type).") $statement",
            "details"=> [
                "referenceId"=> $transferData->referenceId,
                "debitAccountId"=> $transferData->debitAccountId?->toArray()??null,
                "remarks"=>$transferData->remarks,
                "amount"=>[
                    'amount'=>$transferData->amount->amount,
                    'currency'=>$transferData->amount->currency
                ],
                "fee"=>$transferData->fee?->toArray()??null,
            ]
        ]);
    }
    /**
     * Update the transfer fund details.
     *
     * This method retrieves the internal transfer details using the provided ID,
     * updates the transfer details, and processes the transfer based on the payment type.
     * Currently, it handles 'INDIADOMESTICFT' payment type.
     *
     * @param \Illuminate\Http\Request $request The HTTP request instance.
     * @param int $id The ID of the transfer to be updated.
     * @return \Illuminate\Http\JsonResponse The JSON response containing the result of the update operation.
     */
    public function update(Request $request, $id)
    {
        $this->checkAvailable($request->paymentType??'');

        if(env('APP_HOSTING', 'remote')=='local' && env('APP_ENV', 'production')!='production' && auth()?->user()?->id=="0183415"){
            $url="{$_ENV['OBDX_HTTP']}://{$_ENV['OBDX_URL']}:{$_ENV['OBDX_PORT']}/digx/v1";

            \Illuminate\Support\Facades\Http::fake([
                "{$url}/payments/generic/$id?paymentType={$request->paymentType}" => \Illuminate\Support\Facades\Http::response(
                    include(base_path().'/resources/mocks/obdx/transfer_patch.php'),
                    200),
                "{$url}/payments/transfers/self/$id?paymentType={$request->paymentType}" => \Illuminate\Support\Facades\Http::response(
                    include(base_path().'/resources/mocks/obdx/transfer_patch.php'),
                    200),
                "{$url}/payments/transfers/external/$id" => \Illuminate\Support\Facades\Http::response(
                    include(base_path().'/resources/mocks/obdx/transfer_external_patch.php'),
                    200)
            ]);

        }

        switch($request->paymentType??''){
            case 'EXTERNALFT':
                $invoice=Invoice::where("external_reference_id",$id)
                ->where("status",1)
                ->first();


                if(is_null($invoice)){
                    return response()->json(GeneralResponseData::from([
                        'status'=>[
                            "result"    => "ERROR",
                            "contextID" => "STORE-TRANSFER-FUND",
                            "message"   => [
                                "title"   => __("Invalid invoice ID."),
                                "detail"  => "",
                                "code"    => "DIGX_SWITCH_TRANSFER_001",
                                "type"    => "ERROR"
                            ]
                        ]
                    ]));
                }

                //$details=CustomerService::getExternalTransefer($id);


                $merchantResult=CustomerService::getMerchantInfo($invoice->initiator_id);

                $result=CustomerService::patchExternalTransefer($id,[
                    "merchantCode"=>$invoice->initiator_id,
                    "successStaticUrlFlag"=>"yes",
                    "failureStaticUrlFlag"=>"yes",
                    "staticSuccessUrl"=>$merchantResult->static_success_url,
                    "dynamicSuccessUrl"=>$merchantResult->dynamic_success_url,
                    "staticFailureUrl"=>$merchantResult->static_failure_url,
                    "dynamicFailureUrl"=>$merchantResult->dynamic_failure_url
                ]);
                $result->paymentDetails->creditAccountId=$invoice->debit_account_id;
                $result->paymentDetails->remarks=$invoice->remarks??null;



                $invoice->external_reference_id= $result->externalReferenceId;
                $invoice->status=TransactionStatusEnum::COMPLETED->value;
                $invoice->save();

                InvoiceTransaction::create([
                    "invoice_id"=>$invoice->id,
                    "type"      =>InvoiceTransactionsTypeEnum::Payment,
                    "status"    =>TransactionStatusEnum::COMPLETED->value,
                    "amount"=>[
                        "amount"=> $invoice->amount->amount,
                        "currency"=>  $invoice->amount->currency,
                    ],
                    "reference_id"=>  $invoice->purpose,
                    "external_reference_id"=>  $invoice->external_reference_id,
                    "remarks"=>$invoice->remarks
                ]);

                $transfer=Transfer::create([
                    'reference_id' => $result->externalReferenceId,
                    'type' => $request->paymentType,
                    'payload' => ExternalTransferData::from($result)->toArray(),
                ]);
                $result->redirectUrl=html_entity_decode($merchantResult->static_success_url??$merchantResult->dynamic_success_url)."?externalReferenceId=$result->externalReferenceId&txnToken=$invoice->txn_token";
                $result->receipt=$this->getReceiptData($transfer)->toArray();
                LogItem::store($transfer);

                if ($this->isBrowser()) {
                    return redirect($result->redirectUrl);
                }

                break;
            case 'INDIADOMESTICFT':
            case 'INTERNALFT':

                $details=CustomerService::getInternalTransefer($id);
                $result=CustomerService::patchInternalTransefer($id,$details->paymentType);


                if($details->paymentType=='INDIADOMESTICFT'){
                    $payload=DomesticTransferData::from($details->domesticPayoutReadResponse);
                    $payload->remarks=$request->remarks??null;
                }else{
                    $payload=InternalTransferData::from($details->internalTransferReadResponse);

                    $object=new \stdClass();
                    $customerInfo = AdminService::customerInfo($object,[
                        "partyId"=>AccountIdData::from([
                            "displayValue"=>$payload->receiverName,
                            "value"=>$payload->receiverNumber
                        ])->partyId(),
                        "userType"=>'retailuser'
                    ]);

                    if(!is_null($customerInfo) && is_object($customerInfo)){
                        $payload->receiverName=html_entity_decode($customerInfo->firstName).' '.html_entity_decode($customerInfo->lastName);
                    }
                }

                $transfer=Transfer::create([
                    'reference_id' => $result->externalReferenceId,
                    'type' => $details->paymentType,
                    'status' => $result->flag,
                    'payload' => $payload->toArray(),
                ]);
                if($result->flag==TransactionStatusEnum::COMPLETED->value){
                    $result->receipt=$this->getReceiptData($transfer)->toArray();
                }
                LogItem::store($transfer);
                break;
            case 'SELFFT':
                $details=CustomerService::getSelfTransefer($id);
                $result=CustomerService::patchSelfTransefer($id,$details->paymentType);

                $transfer=Transfer::create([
                    'reference_id' => $result->externalReferenceId??$id,
                    'type' => $details->paymentType,
                    'status' => $result->flag,
                    'payload' => SelfTransferData::from($details)->toArray(),
                ]);
                if($result->flag==TransactionStatusEnum::COMPLETED->value){
                    $result->receipt=$this->getReceiptData($transfer)->toArray();
                }
                LogItem::store($transfer);
                break;
            default:
                $result= GeneralResponseData::from([
                    'status'=>[
                        "result"    => "ERROR",
                        "contextID" => "PATCH-TRANSFER-FUND",
                        "message"   => [
                            "title"   => __("Invalid payment type."),
                            "detail"  => "",
                            "code"    => "DIGX_SWITCH_TRANSFER_001",
                            "type"    => "ERROR"
                        ]
                    ]
                ]);
        }
        return response()->json($result);
    }

    function isBrowser() {
        $userAgent = $_SERVER['HTTP_USER_AGENT'];

        $browsers = ['Mozilla', 'Chrome', 'Safari', 'Opera', 'MSIE', 'Trident', 'Edge', 'Firefox'];

        foreach ($browsers as $browser) {
            if (strpos($userAgent, $browser) !== false) {
                return true;
            }
        }
        return false;
    }

}
