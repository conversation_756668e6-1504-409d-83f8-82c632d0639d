<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MapEntry extends Model
{
    use HasFactory;
    public $timestamps = false;

    protected $connection= 'map';

    protected $table = 'entries';

    public function city()
    {
        return $this->belongsTo('App\Models\MapCity');
    }
    public function service()
    {
        return $this->belongsTo('App\Models\MapService');
    }
    public function district()
    {
        return $this->belongsTo('App\Models\MapDistrict');
    }

}
