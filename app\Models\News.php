<?php

namespace App\Models;

use App\Data\AppConfigData;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class News extends Model
{   

    use HasFactory;
    protected $fillable = [
        "id",
        "title",
        "description",
        "image",
        "url",
        "source_name",
        "source_icon",
        'type',
        "status"
    ];
    protected $table = 'news';

   // protected $appends = ['name'];

    protected $casts = [
        'title' => 'object',
        'name' => 'object',
        'description' => 'object',
        'source_name' => 'object',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];
    protected $hidden = [
        'status','created_at','updated_at'
    ];
    /**
     * The "booting" method of the model.
     *
     * @return void
     */
    protected static function boot(){
        parent::boot();
        // static::saving(function() {
        //     AppConfigData::resetCacheSignature();
        // });
        // static::deleting(function($item) {
        //     AppConfigData::resetCacheSignature();
        // });
    }
//    public function getNameAttribute()
//    {
//        $local=app()->getLocale();
//        return  $this->title?->{"$local"};
//    }
    
}
