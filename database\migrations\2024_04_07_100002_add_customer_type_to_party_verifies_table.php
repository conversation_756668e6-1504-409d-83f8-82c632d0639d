<?php

use App\Models\Notification;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('party_verifies', function (Blueprint $table) {
            $table->bigInteger('customer_type_id')->nullable()->index('party_verifies_customer_type_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('party_verifies', function (Blueprint $table) {
            $table->dropColumn('customer_type_id');
        });
    }
};
