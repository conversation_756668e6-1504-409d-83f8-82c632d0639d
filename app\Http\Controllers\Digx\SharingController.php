<?php

namespace App\Http\Controllers\Digx;
use App\Data\GeneralResponseData;
use App\Data\SharingData;
use App\Enums\CurrencyTypeEnum;
use App\Models\Gift;
use App\Models\GiftTransaction;
use App\Services\OBDX\AdminService;
use App\Services\OBDX\CustomerService;
use App\Data\AccountData;
use App\Enums\TransactionStatusEnum;
use App\Helpers\JsonCamel\JsonCamelHelperFacade;
use App\Http\Controllers\Controller;
use App\Models\GiftRequest;
use App\Models\Invoice;
use App\Scopes\CustomerScope;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class SharingController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */

    public function index(Request $request)
    {
        $errorResult=GeneralResponseData::from([
            'status'=>[
                "result"    => "ERROR",
                    "contextID" => "INFO-SHARING",
                    "message"   => [
                        "title"   => "",
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_SHARING_100",
                        "type"    => "ERROR"
                    ]
            ]
        ]);

        $validator=validator()->make($request->all(),[
            'txnToken'=>"required",
        ]);

        if($validator->fails()){
            $errorResult->status->message->title=join("\n",$validator->errors()->all());
            return response()->json($errorResult,400);
        }
        $sharing=\App\Models\Sharing::where("txn_token",$request->txnToken)->first();
        if(!is_null($sharing)){
            switch($sharing->type){
                case 'transfer':
                    $invoice=Invoice::where("txn_token",$sharing->txn_token)->where("status",1)->first();
                    if(!is_null($invoice)){
                        if($invoice->initiator_type=="ykbPayment"){
                            $result=CustomerService::getMerchantInfo($invoice->initiator_id);
                            $invoice->debit_account_id=[
                                "displayValue"=> $result->description,
                                "value"       => $result->merchantAccount,
                            ];
                            $invoice->save();
                        }
                        if($invoice->initiator_type=="sdk"){
                            $date=Carbon::parse($invoice->created_at)->addMinute();
                            if(Carbon::now()->isAfter($date)){
                                $errorResult->status->message->title=__("This request is not exist or already expired!");
                                return response()->json($errorResult,400);
                            }
                        }
                        return response()->json(GeneralResponseData::from([
                            'status'=>[
                                "result"    => "SUCCESSFUL",
                                "contextID" => "INFO-SHARING-$invoice->id",
                                "message"   => [
                                    "title"   => "",
                                    "detail"  => "",
                                    "code"    => "0",
                                    "type"    => "INFO"
                                ]
                            ]
                        ])->additional([
                            "sharingType"=>$sharing->type,
                            "transferDetails"=>[
                                "id"=>$invoice->id,
                                "debitAccountId"=>$invoice->debit_account_id,
                                "amount"=>$invoice->amount,
                                "purpose"=>$invoice->purpose,
                                "remarks"=>$invoice->remarks,
                                "type"=>$invoice->initiator_type,
                            ]
                        ]));
                    }
                    break;
                case 'ykbPay':
                    $invoice=Invoice::where("txn_token",$sharing->txn_token)->where("status",1)->first();
                    if(!is_null($invoice)){
                        $result=CustomerService::getMerchantInfo($invoice->initiator_id);
                        $invoice->debit_account_id=[
                            "displayValue"=> $result->description,
                            "value"       => $request->merchantAccount,
                        ];
                        $invoice->save();
                        return response()->json(GeneralResponseData::from([
                            'status'=>[
                                "result"    => "SUCCESSFUL",
                                "contextID" => "INFO-SHARING-$invoice->id",
                                "message"   => [
                                    "title"   => "",
                                    "detail"  => "",
                                    "code"    => "0",
                                    "type"    => "INFO"
                                ]
                            ]
                        ])->additional([
                            "sharingType"=>$sharing->type,
                            "transferDetails"=>[
                                "id"=>$invoice->id,
                                "debitAccountId"=>$invoice->debit_account_id,
                                "amount"=>$invoice->amount,
                                "purpose"=>$invoice->purpose,
                                "remarks"=>$invoice->remarks,
                                "type"=>$invoice->initiator_type,
                            ]
                        ]));
                    }
                    break;
                case 'account':
                    return response()->json(GeneralResponseData::from([
                        'status'=>[
                            "result"    => "SUCCESSFUL",
                            "contextID" => "INFO-SHARING-$sharing->id",
                            "message"   => [
                                "title"   => "",
                                "detail"  => "",
                                "code"    => "0",
                                "type"    => "INFO"
                            ]
                        ]
                    ])->additional(SharingData::from($sharing->toArray())->toArray()));
                case 'gift':
                    $transaction=GiftTransaction::where("id",$sharing->data->id)->where("status",1)->first();
                    if(!is_null($transaction)){
                        $gift_info=Gift::withoutGlobalScope(CustomerScope::class)->where("id",$transaction->gift_id)->first();
                        if(in_array($transaction->amount->currency,[CurrencyTypeEnum::G21->value,CurrencyTypeEnum::G24->value]) && ($request->header('appVersion')??0)<173){
                            return response()->json(GeneralResponseData::from([
                                'status'=>[
                                    "result"    => "SUCCESSFUL",
                                    "contextID" => "INFO-INVOICE-$transaction->id",
                                    "message"   => [
                                        "title"   => "",
                                        "detail"  => "",
                                        "code"    => "0",
                                        "type"    => "INFO"
                                    ]
                                ]
                            ])->additional([
                                "sharingType"=>'unknown',
                            ]));
                        }
                        return response()->json(GeneralResponseData::from([
                            'status'=>[
                                "result"    => "SUCCESSFUL",
                                "contextID" => "INFO-INVOICE-$transaction->id",
                                "message"   => [
                                    "title"   => "",
                                    "detail"  => "",
                                    "code"    => "0",
                                    "type"    => "INFO"
                                ]
                            ]
                        ])->additional([

                            "sharingType"=>$sharing->type,
                            "giftDetails"=>[
                                "id"=>$transaction->id,
                                "amount"=>$transaction->amount,
                                "debit_account_id"=>$gift_info->debit_account_id,
                                "type"=>$gift_info->type,
                                "party_name"=>$gift_info->party_name,
                                "remarks"=>$gift_info->remarks,
                                "created_at"=>$gift_info->created_at,
                            ]
                        ]));
                    }
                    break;
                case 'giftRequest':
                    $giftRequest=GiftRequest::withoutGlobalScope(CustomerScope::class)
                    ->select('id','amount',"type",'party_name','party_phone','remarks','created_at as date')
                    ->where("id",$sharing->data->id)
                    ->where("status",TransactionStatusEnum::INIT->value)
                    ->where("sender->value",auth()->user()->phone)
                    ->first();

                    if(!is_null($giftRequest)){
                        return JsonCamelHelperFacade::json(GeneralResponseData::from([
                            'status'=>[
                                "result"    => "SUCCESSFUL",
                                "contextID" => "INFO-INVOICE-$giftRequest->id",
                                "message"   => [
                                    "title"   => "",
                                    "detail"  => "",
                                    "code"    => "0",
                                    "type"    => "INFO"
                                ]
                            ]
                        ])->additional([
                            "sharingType"=>$sharing->type,
                            "giftRequestDetails"=>$giftRequest
                        ])->transform()
                    );
                    }

                    break;
                default:
                    return abort(404);
            }

            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "INFO-SHARING",
                    "message"   => [
                        "title"   => __("This request is not exist or already expired!"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_SHARING_200",
                        "type"    => "ERROR"
                    ]
                ]
            ]));
        }else{
            $result=AdminService::sharingProfile($request->txnToken);
            if($result->status->message->code=="0"){
                $profile=$result->getAdditionalData()['profile'];
                return response()->json(GeneralResponseData::from([
                    'status'=>[
                        "result"    => "SUCCESSFUL",
                        "contextID" => "INFO-SHARING",
                        "message"   => [
                            "title"   => "",
                            "detail"  => "",
                            "code"    => "0",
                            "type"    => "INFO"
                        ]
                    ]
                ])->additional([
                    "sharingType"=>"friend",
                    "data"=>[
                        "name"=>$profile->name,
                        //"image"=>,
                    ]
                ]));
            }
        }
        return abort(404);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator=validator()->make($request->all(),[
            'sharingType'=>"required",
            'data'=>"required"
        ]);
        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-SHARING",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_SHARING_100",
                        "type"    => "ERROR"
                    ]
                ]
            ]));
        }

        $validateAccountOwner=null;
        switch($request->sharingType){
            case 'account':
                $validator=validator()->make($request->all(),[
                    'data.accountName'=>"required",
                    'data.accountNumber'=>"required|max:20|min:20"
                ]);
                $validateAccountOwner=$request->input("data.accountNumber");
                $data=[
                    'accountName'  =>$request->input("data.accountName"),
                    'accountNumber'=>$request->input("data.accountNumber"),
                    'nickName'=>$request->input("data.nickName")??"",

                ];
                break;
            default:
                return abort(404);
        }


        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-SHARING",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_SHARING_100",
                        "type"    => "ERROR"
                    ]
                ]
            ]));
        }

        $user=$request->user()->userProfile;
        if(!is_null($validateAccountOwner)){
            $valid=false;
            $message="You don't have the correct account!";
            $result=CustomerService::account($request,$validateAccountOwner);
            if($result instanceof AccountData){
                $account=$result;
                $valid=$account->status=="ACTIVE" && $account->isCashOut();
            }

            if(!$valid){
                return response()->json(GeneralResponseData::from([
                    'status'=>[
                        "result"    => "ERROR",
                        "contextID" => "STORE-SHARING",
                        "message"   => [
                            "title"   => __($message),
                            "detail"  => "",
                            "code"    => "DIGX_SWITCH_SHARING_101",
                            "type"    => "ERROR"
                        ]
                    ]
                ]));
            }

        }

        $sharing=\App\Models\Sharing::create([
            "txn_token" =>(string) Str::orderedUuid(),
            "type"      =>$request->sharingType,
            "party_id"  =>$user->partyId->value,
            "data"      =>$data
        ]);

        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "INI-SHARING-$sharing->id",
                "message"   => [
                    "title"   => __("Operation accomplished successfully"),
                    "detail"  => __("Operation accomplished successfully"),
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])->additional([
            "externalReferenceId"=>$sharing->txn_token,
        ]));

    }


    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Invoice  $invoice
     * @return ?\Illuminate\Http\JsonResponse
     */
    public function update(Request $request, Invoice $invoice)
    {

    }


}
