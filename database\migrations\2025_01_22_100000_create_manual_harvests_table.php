<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('manual_harvests', function (Blueprint $table) {
            $table->bigInteger('id', true);
            $table->string('party_id', 45)->index();
            $table->string('tracking_code', 100)->index();
            $table->string('network_type', 50)->nullable()->index();
            $table->text('target_account_id')->nullable(); // JSON: AccountIdData
            $table->text('remittance_currency')->nullable(); // JSON: CurrencyAmountData
            $table->text('customer_notes')->nullable();
            $table->text('receiver_info')->nullable(); // JSON: Customer profile data
            $table->text('sender_info')->nullable(); // JSON: Sender information (filled by admin)
            $table->text('remittance_details')->nullable(); // JSON: Amount, fees, etc. (filled by admin)
            $table->text('admin_notes')->nullable();
            $table->string('processed_by', 45)->nullable()->index(); // Admin user ID
            $table->string('external_reference_id', 100)->nullable()->index(); // Final transaction reference
            $table->integer('status')->default(0)->index(); // 0=pending, 1=processing, 2=completed, -1=failed, -2=cancelled
            $table->timestamp('processed_at')->nullable();
            $table->timestamps();
            
            // Foreign key constraints
            $table->foreign('party_id')->references('id')->on('users')->onDelete('cascade');
            
            // Unique constraint to prevent duplicate tracking codes per user
            $table->unique(['party_id', 'tracking_code'], 'manual_harvests_unique_tracking');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('manual_harvests');
    }
};
