<?php
namespace Database\Seeders;

//use App\Models\User;

use App\Models\CustomerType;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class FixCustomerRolesSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        $customerType=CustomerType::find(CustomerType::RETAIL);
        $customerType->roles=json_encode(["basic"]);
        $customerType->save();

        $customerType=CustomerType::find(CustomerType::CORPORATE);
        $customerType->roles=json_encode(["checker","maker","full"]);
        $customerType->save();

        $customerType=CustomerType::find(CustomerType::BUSINESS);
        $customerType->roles=json_encode(["full","basic"]);
        $customerType->save();

        $customerType=CustomerType::find(CustomerType::AGENT);
        $customerType->roles=json_encode(["full","basic"]);
        $customerType->save();

    }
}
