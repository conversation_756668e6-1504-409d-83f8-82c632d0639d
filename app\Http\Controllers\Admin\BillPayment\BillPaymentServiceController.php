<?php

namespace App\Http\Controllers\Admin\BillPayment;
use App\Data\GeneralItemData;
use App\Http\Controllers\Controller;
use App\Jobs\SyncUtilityPayment;
use App\Models\BillPaymentFilter;
use App\Models\BillPaymentModelFilter;
use App\Models\BillPaymentService;
use Illuminate\Http\Request;
use Auth;

class BillPaymentServiceController extends Controller
{
    protected $viewTypes=[
        "select"=>"Select",
        "filter"=>"Filter",
        "chip"=>"Chip",
        "grid"=>"Grid"
    ];
    protected $types=[
        "gold"=>"gold"
    ];

    public function __construct(Request $request){$this->middleware('auth');}
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response|\Illuminate\Contracts\View\View
     */
    public function index(Request $request)
    {
        if (! Auth::user()->canAny(['bill_payment.service.*','bill_payment.service.list'])) abort(401);

        $filter=$request->all();

        $items=BillPaymentService::select('id','title','status','created_at')->orderBy('id','desc');


        if($request->filled('filter') ){
            $items=$items->whereHas('filters',function($query) use($request){
                $query->where('id',$request->filter);
            });
        }

        if($request->filled('searchname'))
            $items=$items->where(function($query) use($filter){
                return $query->where('title->ar','like','%'.$filter['searchname'].'%')
                ->orWhere('title->en','like','%'.$filter['searchname'].'%');
            });

        $items=$items->paginate(15);

      //  GeneralItemData::collect($items->paginate(15));//->wrap('paginated_data');

      $filters=BillPaymentFilter::select('id','title')->where('status',1)->get();

        return view('default.admin.bill_payment.service.index')
        ->with('items', $items)
        ->with('filters', $filters)
        ->with('filter', $filter);
    }
    public function sync(){
        if (! Auth::user()->canAny(['bill_payment.service.*','bill_payment.service.sync'])) return abort(401);

        SyncUtilityPayment::dispatchSync();
        return back()->with('success',__("Operation accomplished successfully"));

    }

    /**
     * Show the form for creating a resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function create()
    {
        if (! Auth::user()->canAny(['bill_payment.service.*','bill_payment.service.create'])) abort(401);

        return view('default.admin.bill_payment.service.view')
        ->with('types', $this->types);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\BillPaymentService  $service
     * @return \Illuminate\Contracts\View\View
     */
    public function show(BillPaymentService $service){
        if (! Auth::user()->canAny(['bill_payment.service.*','bill_payment.service.view','bill_payment.service.edit'])) return abort(401);

        $filters=BillPaymentFilter::select('id','title')->where('status',1)->get();


        return view('default.admin.bill_payment.service.view')
        ->with('item', $service)
        ->with('viewTypes', $this->viewTypes)
        ->with('filters', $filters);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\BillPaymentService  $service
     * @return \Illuminate\Http\Response | \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, BillPaymentService $service)
    {
        if (! Auth::user()->canAny(['bill_payment.service.*','bill_payment.service.edit'])) abort(401);
        //return response()->json($request->all());
        $this->validate($request,[
            'title.en' => 'required|max:500',
            'title.ar' => 'required|max:500',
            'url'       => 'required_if:view_type,==,grid',
            'view_type' => 'nullable|in:'.join(',',collect($this->viewTypes)->keys()->toArray()),
            'filters' => 'nullable|array',
            'status' => 'required|in:0,1',
        ]);
        return $this->save($request,$service);
    }

    public function save(Request $request, ?BillPaymentService $item=null){
        ini_set('memory_limit', '1024M');
        if(is_null($item)){
            $isNew=true;
            $item = new BillPaymentService;
        }

        $this->setImage('image',"billPayments");

        // dd($request->all());
        // return;
        $item->image = $request->image;
        $item->title = $request->title;
        $item->view_type = $request->view_type;
        $item->url = $request->url;
        $item->status = $request->status;
        $item->save();

        BillPaymentModelFilter::where('model_id', $item->id)
        ->where('model_type', BillPaymentService::class)
        ->delete();
        if($request->filled('filters') &&  count($request->filters)){
            $filterItems=[];
            $parentId=null;
            foreach($request->filters as $filter){
                $filterItems[]=[
                    "model_type"=>BillPaymentService::class,
                    "model_id"  =>$item->id,
                    "bill_payment_filter_id"=>$filter,
                    "parent_filter_id"=>$parentId,
                    "created_at"=>\Carbon\Carbon::now()->toDateTimeString()
                ];
                $parentId=$filter;
            }
            if(!empty($filterItems)){
                collect($filterItems)->chunk(100)
                ->each(function ($chunked) {
                    BillPaymentModelFilter::insert($chunked->values()->toArray());
                });
            }
        }

        BillPaymentService::cacheUpdate(["catalog:$item->id"]);

        if(isset($isNew)){
            return redirect(route("admin.billPayment.service.show",$item->id))
            ->with('success',__("Operation accomplished successfully"));
        }else{
            return back()->with('success',__("Operation accomplished successfully"));
        }
    }
    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id){
        if (! Auth::user()->canAny(['bill_payment.service.*','bill_payment.service.delete'])) return abort(401);

    }

}
