<?php

namespace App\Models;

use App\Scopes\CustomerScope;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Transfer extends Model
{
    use HasFactory;

    protected $fillable = ['reference_id','party_id','payload','type','status'];
    protected $casts = [
        'payload' => 'object',
        'created_at'        => 'datetime:Y-m-d H:i:s',
        'updated_at'        => 'datetime:Y-m-d H:i:s',
    ];
    protected $hidden = [
        'rn'
    ];
    protected static function boot(){
        parent::boot();
        static::addGlobalScope(new CustomerScope);
         // auto-sets values on creation
        static::creating(function ($query) {
            $query->party_id = auth()->user()->id;
        });
    }

    protected function serializeDate(DateTimeInterface $date){
        return $date->timezone('Asia/Aden')->format('Y-m-d H:i:s');
    }
    public function logs()
    {
        $_name=static::class;
        return $this->hasMany('App\Models\LogEntry', 'model_id')->where('model',$_name)->where('type','request')->with('relatedEntries');
    }
}
