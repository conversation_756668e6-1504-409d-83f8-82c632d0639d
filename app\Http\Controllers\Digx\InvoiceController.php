<?php

namespace App\Http\Controllers\Digx;

use App\Data\AccountConfigData;
use App\Data\GeneralResponseData;
use App\Data\ReceiptData;
use App\Enums\InvoiceTransactionsTypeEnum;

use App\Enums\ServiceTagEnum;
use App\Models\InvoiceTransaction;
use App\Models\Merchant;
use App\Services\OBDX\CustomerService;
use App\Data\AccountData;
use App\Data\AccountIdData;
use App\Http\Controllers\Controller;
use App\Models\Invoice;
use App\Models\User;

use App\Traits\AuthorizesServices;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use App\Services\FlexService;

use App\Enums\TransactionStatusEnum;
use App\LogItem;

class InvoiceController extends Controller
{
    use AuthorizesServices;
    protected function getServiceTags(): array{
        return [

        ];
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */

    public function index(Request $request)
    {
        $validator=validator()->make($request->all(),[
            'txnToken'=>"required",
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "INFO-INVOICE",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_INVOICE_100",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }
        $sharing=\App\Models\Sharing::where("txn_token",$request->txnToken)->first();
        if(!is_null($sharing)){
            switch($sharing->type){
                case 'transfer':
                    $invoice=Invoice::where("txn_token",$sharing->txn_token)->where("status",TransactionStatusEnum::INIT->value)->first();
                    if(!is_null($invoice)){
                        return response()->json(GeneralResponseData::from([
                            'status'=>[
                                "result"    => "SUCCESSFUL",
                                "contextID" => "INFO-INVOICE-$invoice->id",
                                "message"   => [
                                    "title"   => "",
                                    "detail"  => "",
                                    "code"    => "0",
                                    "type"    => "INFO"
                                ]
                            ]
                        ])->additional([
                            "sharingType"=>$sharing->type,
                            "transferDetails"=>[
                                "id"=>$invoice->id,
                                "debitAccountId"=>$invoice->debit_account_id,
                                "amount"=>$invoice->amount,
                                "purpose"=>$invoice->purpose,
                                "remarks"=>$invoice->remarks,
                                "type"=>$invoice->initiator_type,
                            ]
                        ]));
                    }
                    break;
                default:
                    return response()->json(GeneralResponseData::from([
                        'status'=>[
                            "result"    => "ERROR",
                            "contextID" => "INFO-INVOICE",
                            "message"   => [
                                "title"   => __("This process not supported in this version, please update the app!"),
                                "detail"  => "",
                                "code"    => "DIGX_SWITCH_INVOICE_200",
                                "type"    => "ERROR"
                            ]
                        ]
                    ]));
            }
        }
        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "INFO-INVOICE",
                "message"   => [
                    "title"   => __('Transaction not exist!'),
                    "detail"  => "",
                    "code"    => "DIGX_SWITCH_INVOICE_200",
                    "type"    => "ERROR"
                ]
             ]
        ]));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator=validator()->make($request->all(),[
            'debitAccountId.displayValue'=>"required",
            'debitAccountId.value'=>"required|max:20|min:20",
            'amount.amount'=>"required|numeric",
            'amount.currency'=>"required|in:".join(",",\App\Enums\CurrencyTypeEnum::values()),
            'purpose'=>"nullable|string",
            'remarks'=>"nullable|string",
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-INVOICE",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_INVOICE_100",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        $user=$request->user()->userProfile;

        $valid=false;
        $message="You don't have the correct account!";
        $result=CustomerService::account($request,$request->input("debitAccountId.value"));
        if($result instanceof AccountData){
            $account=$result;
            $valid=$account->status=="ACTIVE" && $account->isCash() && $account->currencyId==$request->input("amount.currency")&&
            $account->allowedService(AccountConfigData::invoicePayment);
        }

        if(!$valid){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-INVOICE",
                    "message"   => [
                        "title"   => __($message),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_INVOICE_101",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        $invoice=Invoice::create([
            "txn_token"=>(string) Str::orderedUuid(),
            "initiator_id"     =>$user->partyId->value,
            "initiator_type"   =>"app",
            "status"           =>TransactionStatusEnum::INIT->value,
            "debit_account_id"=>[
                "displayValue"=> $request->input("debitAccountId.displayValue"),
                "value"=> $request->input("debitAccountId.value"),
            ],
            "amount"=>[
                "amount"=> $request->input("amount.amount"),
                "currency"=>  $request->input("amount.currency"),
            ],
            "purpose"=>$request->purpose,
            "remarks"=>$request->remarks
        ]);
        LogItem::store($invoice);

        $sharing=\App\Models\Sharing::create([
            "txn_token"=> $invoice->txn_token,
            "type"     =>"transfer",
            "party_id"   =>$user->partyId->value,
            // "data"=>[
            //     "txnToken"=> $invoice->txn_token
            // ]
        ]);

        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "INI-INVOICE-$invoice->id",
                "message"   => [
                    "title"   => __("Operation accomplished successfully"),
                    "detail"  => __("Operation accomplished successfully"),
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ])->additional([
            "externalReferenceId"=>$sharing->txn_token,
        ]));

    }


    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Invoice  $invoice
     * @return ?\Illuminate\Http\JsonResponse
     */
    public function update(Request $request, Invoice $invoice)
    {
        $validator=validator()->make($request->all(),[
            'debitAccountId.value'=>"required|max:20|min:20"
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-INVOICE",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_INVOICE_100",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }
        LogItem::store($invoice);

        //return response()->json($invoice);
        if($invoice->status!=TransactionStatusEnum::INIT->value){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-INVOICE",
                    "message"   => [
                        "title"   => "This transaction already expired",
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_INVOICE_101",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        $merchantUser=User::with('merchant')->find($invoice->initiator_id);

        $valid=false;
        $message="You don't have the correct account!";
        $result=CustomerService::account($request,$request->input("debitAccountId.value"));
        if($result instanceof AccountData){
            $account=$result;
            $valid=$account->status=="ACTIVE" && $account->isCash() && $account->currencyId==$invoice->amount->currency&&
            $account->allowedService(AccountConfigData::invoicePayment)&&
            (
                ($account->currencyId==\App\Enums\CurrencyTypeEnum::YER->value && $account->isNorth()==($merchantUser->merchant->is_north==1)) ||
                $account->currencyId!=\App\Enums\CurrencyTypeEnum::YER->value
            );
        }

        if(!$valid){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-INVOICE",
                    "message"   => [
                        "title"   => __($message),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_INVOICE_102",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        $invoice->customer_account_id=$request->debitAccountId;
        $invoice->save();

        return response()->json(GeneralResponseData::from(array(
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => "Successfully init customer account",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ))->additional([
            'paymentId'=>$invoice->id,
        ]));

    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Invoice  $invoice
     * @return ?\Illuminate\Http\JsonResponse
     */
    public function confirm(Request $request,  Invoice $invoice)
    {
        $errorResult=GeneralResponseData::from([
            'status'=>[
                "result"    => "ERROR",
                    "contextID" => "STORE-INVOICE",
                    "message"   => [
                        "title"   => "",
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_INVOICE_101",
                        "type"    => "ERROR"
                    ]
            ]
        ]);

        $this->available([
            ServiceTagEnum::REQUEST_MONEY
        ]);

        LogItem::store($invoice);

        if( is_null($invoice) ||
            $invoice->status!=TransactionStatusEnum::INIT->value ||
            is_null($invoice->customer_account_id) ||
            Carbon::now()->isAfter(Carbon::parse($invoice->created_at)->addMinute())){

            $errorResult->status->message->title=__("This request is not exist or already expired!");
            return response()->json($errorResult,400);
        }

        $merchantUser=User::with('merchant')->find($invoice->initiator_id);

        $valid=false;
        $message="You don't have enough balance in your account!";
        $result=CustomerService::account($request, $invoice->customer_account_id->value);
        if($result instanceof AccountData){
            $account=$result;
            if($account->status!="ACTIVE"){
                $message="Your account not active!";
            }else if(!$account->allowedService(AccountConfigData::invoicePayment)){
                $message="This account not allowed to use this service!";
            }else if(!$account->isCash()){
                $message="Account is not cash!";
            }else if($account->currencyId!=$invoice->amount->currency){
                $message="Account currency not match!";
            }else if($account->balance->amount<$invoice->amount->amount){
                $message="You don't have enough balance in your account!";
            }else if(!(
                ($account->currencyId==\App\Enums\CurrencyTypeEnum::YER->value && $account->isNorth()==($merchantUser->merchant->is_north==1)) ||
                $account->currencyId!=\App\Enums\CurrencyTypeEnum::YER->value
            )){
                $message="Merchant account not accept this payment from this currency account!";
            }else{
                $valid=true;
            }
        }

        if(!$valid){
            $errorResult->status->message->code="DIGX_SWITCH_INVOICE_102";
            $errorResult->status->message->title=__($message);
            return response()->json($errorResult,400);
        }

        $invoiceTransaction=$invoice->transactions()->where("type",InvoiceTransactionsTypeEnum::Payment)->first();
        if(!is_null($invoiceTransaction) && $invoiceTransaction->status != TransactionStatusEnum::INIT->value){

            $errorResult->status->message->code="DIGX_SWITCH_INVOICE_103";
            $errorResult->status->message->title=__("This transaction not in the correct status!");
            return response()->json($errorResult,400);

        }else if(is_null($invoiceTransaction)){
            $invoiceTransaction = InvoiceTransaction::create([
                "invoice_id"=>$invoice->id,
                "type"      =>InvoiceTransactionsTypeEnum::Payment,
                "status"    =>TransactionStatusEnum::INIT->value,
                "amount"=>[
                    "amount"=> $invoice->amount->amount,
                    "currency"=>  $invoice->amount->currency,
                ],
                "reference_id"=>  round(microtime(true)),
                "remarks"=>$request->remarks
            ]);
        }

        $merchantUser=User::with('merchant')->find($invoice->initiator_id);

        $object=new \stdClass();
        if(!is_null($merchantUser->merchant->service_name)){
            $object->reference_id= $invoiceTransaction->reference_id;
            $object->service_name= $merchantUser->merchant->service_name;
            $object->account_id = $invoice->customer_account_id->value;
            $object->amount     = $invoiceTransaction->amount->amount;
            $object->currency   = $invoiceTransaction->amount->currency;
            $object->remarks= $invoice->remarks??__("Pay for invoice");

            $result=FlexService::debitToAccount($object);

        }else if(!is_null($merchantUser->merchant->account_id)){
            $object->journals= [
                "genericPayee" =>[
                    "nickName" =>'',
                    "accountName" =>'temp',
                    "accountNumber" => $merchantUser->merchant->account_id,
                    "transferMode"=> 'ACC'
                ],
                "genericPayout" =>[
                    "amount" =>[
                        "amount" =>$invoiceTransaction->amount->amount,
                        "currency" =>$invoiceTransaction->amount->currency
                    ],
                    "purpose" =>'FAML',
                    "purposeText" => null,
                    "debitAccountId" => $invoice->customer_account_id,
                    "remarks"=> $invoice->remarks??__("Pay for invoice")
                ],
                "paymentType" => 'INTERNALFT'
            ];

            $result=CustomerService::internalTransefer($object);
        }else{
            $errorResult->status->message->code="DIGX_SWITCH_INVOICE_104";
            $errorResult->status->message->title=__("This merchant not active right now, please try again later!");
            return response()->json($errorResult,400);
        }

        if($result->status->message->code=="0"){
            $invoice->external_reference_id= $result->getAdditionalData()["externalReferenceId"];
            $invoice->status=TransactionStatusEnum::COMPLETED->value;
            $invoice->save();

            $invoiceTransaction->external_reference_id= $invoice->external_reference_id;
            $invoiceTransaction->status=TransactionStatusEnum::COMPLETED->value;
            $invoiceTransaction->save();

            $result->additional([
                "schema"=>$merchantUser->merchant->schema??null,
                "externalReferenceId"=>$result->getAdditionalData()["externalReferenceId"],
                "receipt"=>$this->getReceiptData($merchantUser->merchant,$invoice,$invoiceTransaction)->toArray()
            ]);
            return response()->json($result);
        }else{
            $invoice->status=TransactionStatusEnum::ERROR->value;
            $invoice->save();

            $invoiceTransaction->status=TransactionStatusEnum::ERROR->value;
            $invoiceTransaction->save();
        }
        return response()->json($result,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);

    }
        /**
     * Display the specified resource.
     *
     * @return void
     */
    public function receipt($id,Request $request)
    {
        $item=Invoice::with(['transactions'=>function($query)use($request){
            $query->where('status',TransactionStatusEnum::COMPLETED->value)
            ->whereIn('type', [InvoiceTransactionsTypeEnum::Payment->value,InvoiceTransactionsTypeEnum::Reverse->value,InvoiceTransactionsTypeEnum::Refund->value]);
            return $query;
        }])
        ->whereHas('transactions',function($query)use($request){
            $query->where('status',TransactionStatusEnum::COMPLETED->value)
            ->whereIn('type', [InvoiceTransactionsTypeEnum::Payment->value,InvoiceTransactionsTypeEnum::Reverse->value,InvoiceTransactionsTypeEnum::Refund->value]);
            return $query;
        })
        ->where('id', $id)
        ->first();

        $accountId=AccountIdData::from($item->customer_account_id);
        if(auth()->user()->id!=$accountId->partyId()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "RECEIPT-INVOICE",
                    "message"   => [
                        "title"   => __("You don't have a permission!"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_INVOICE_101",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }
        $merchantUser=User::with('merchant')->find($item->initiator_id);
        $this->generateReceipt($this->getReceiptData($merchantUser->merchant,$item));

    }
    protected function getReceiptData(Merchant $merchant,Invoice $invoice,?InvoiceTransaction $transaction=null): ReceiptData
    {
        $transaction??=$invoice->transactions->first();
        $title=__("Payment for purchases")." ".__("wasil_type_". $transaction->type->value);

        return ReceiptData::from([
            "id"=> $invoice->id,
            "date"=> date_format(date_create($invoice->created_at), "Y-m-d H:i:s"),
            "title"=>  $title,
            "beneficiary"=> $merchant->name??null,
            "statement"=>  $title,
            "details"=> [
                "debitAccountId"=> $invoice->customer_account_id,
                "referenceId"=>$transaction->external_reference_id??null,
                "amount"=>[
                    'amount'=>$transaction->amount->amount,
                    'currency'=>$transaction->amount->currency
                ],
                "remarks"=>$transaction->remarks??$invoice->remarks??__("Pay for invoice"),
            ]
        ]);
    }

}
