<?php

namespace App\Http\Controllers;
use App\Http\Controllers\Controller;
use App\LogItem;
use App\Models\CustomerType;
use App\Models\LogEntry;
use App\Models\Service;
use Crypt;
use Illuminate\Http\Request;
use App\Models\User,App\UserAccount,App\UserInterface;
use DateTime,Image,Auth,File,Storage,DB,Validator;

use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Session;
use Laravel\Telescope\IncomingEntry;
use Lara<PERSON>\Telescope\Telescope;

class SharingController extends Controller
{
    public function install(Request $request)
    {
        $bundle=env('APP_BUNDLE',"com.ykb.bankylite");
        $bundle="com.bankylite.commercial";
        $appStoreIdentifier=env('APP_STORE_IDENTIFIER',"**********");

        $url="";
        $urlAndroid="transfer";
        $urlIOS="transfer";

        $urlstore="";
        $playstore="https://play.google.com/store/apps/details?id=$bundle";
        $appstore="https://apps.apple.com/us/app/id$appStoreIdentifier";
        $webstore="https://".request()->header("x-forwarded-host")."/app";

        //Detect special conditions devices
        $iPod = stripos($_SERVER['HTTP_USER_AGENT'], "iPod");
        $iPhone = stripos($_SERVER['HTTP_USER_AGENT'], "iPhone");
        $iPad = stripos($_SERVER['HTTP_USER_AGENT'], "iPad");
        $Android = stripos($_SERVER['HTTP_USER_AGENT'], "Android") || stripos($_SERVER['HTTP_USER_AGENT'], "Linux");
        $webOS = stripos($_SERVER['HTTP_USER_AGENT'], "webOS");


        $schema = $bundle=="com.bankylite.commercial"?"bankylite":"ibankylite";
        //do something with this information
        if ($iPod || $iPhone || $iPad) {
            $url="$schema://$urlIOS";
            $urlstore=$appstore;
        } else if ($Android) {
            $url="intent://$urlAndroid#Intent;scheme=$schema;package=$bundle;end";
            $urlstore=$playstore;
        } else {
            //$url="https://www.yk-bank.com/ar/Personal/banking-services/banky-lite";
            $url="$schema://$urlIOS";
            $urlstore=$webstore;
        }
        //return redirect("$urlstore");

        return view('default.layouts.install')
        ->with('url', $url)
        ->with('urlstore', $urlstore)
        ->with('playstore', $playstore)
        ->with('appstore', $appstore);
    }


    public function show(String $token,Request $request)
    {
        $_token=$token;
        if(Session::has('txn_token') && $token=="redirect"){
            $_token=Session::get('txn_token');
        }
       // return $this->_redirectHandler($token);
        $date=\Carbon\Carbon::now()->subHours(24)->toDateTimeString();
        //$sharing=\App\Models\Sharing::where("txn_token",$_token)->whereDate("created_at",'>=',DB::raw("timestamp '".$date."'"))->first();
        $sharing=\App\Models\Sharing::where("txn_token",$_token)->first();

        if(!is_null($sharing)){
            if($token!="redirect"){
                switch($sharing->type){
                    case 'gift':
                        $id=181;
                        $items=Service::with(['items'=>function($query) use($id){
                            $query->whereNotNull('service_id')
                            ->where('status','<>',0)
                            ->whereHas('customerTypes', function ($query) use($id) {
                                return $query->where('page_service_id',$id);
                            });
                            // if(!in_array('RetailUser',$customer->userProfile->roles)){
                            //     $query->where('id',17);
                            // }
                            return $query;
                        }])
                        ->whereNull('service_id')
                        ->where('status','<>',0)
                        ->whereHas('customerTypes', function ($query) use($id) {
                            return $query->where('page_service_id',$id);
                        })
                        ->orderBy('sort','asc');
                        $item=$items->first();

                        if(!is_null($item) && count($item->items)){
                            Session::put('txn_token',$sharing->txn_token);
                            return view('default.layouts.sharing')->with('sharing',$sharing)->with('items',$items->get());
                        }
                        break;
                    default:
                    break;
                }
            }
            return $this->_redirectHandler($sharing->txn_token);

        }else{
            try {
                $partyId=Crypt::decrypt($_token);
                return $this->_redirectHandler($_token);
            } catch (\Illuminate\Contracts\Encryption\DecryptException $e) {}

        }
        return abort(404,"Transaction expired or not found!");

    }

    private function _redirectHandler(String $txnToken){
        $bundle=env('APP_BUNDLE');
        //Detect special conditions devices
        $iPod = stripos($_SERVER['HTTP_USER_AGENT'], "iPod");
        $iPhone = stripos($_SERVER['HTTP_USER_AGENT'], "iPhone");
        $iPad = stripos($_SERVER['HTTP_USER_AGENT'], "iPad");
        $Android = stripos($_SERVER['HTTP_USER_AGENT'], "Android") || stripos($_SERVER['HTTP_USER_AGENT'], "Linux");
        $webOS = stripos($_SERVER['HTTP_USER_AGENT'], "webOS");
        $Windows = isset($_SERVER['HTTP_X_WINDOWS_APP']) && $_SERVER['HTTP_X_WINDOWS_APP'] == "true";

        $schema = $bundle=="com.ykb.bankylite"?"bankylite":"ibankylite";
        $bundle="com.bankylite.commercial";
        //do something with this information
        if ($iPod || $iPhone || $iPad) {
            $urlIOS="transfer?txnToken=$txnToken";
            return redirect("$schema://$urlIOS");
            //browser reported as an iPhone/iPod touch -- do something here
        } else if ($Android) {
            $urlAndroid="transfer/$txnToken";
            // \Log::info("intent://$urlAndroid#Intent;scheme=bankylite;package=com.ykb.bankylite;end");
            // return redirect("intent://$urlAndroid#Intent;scheme=bankylite;package=com.ykb.bankylite;end");

            return redirect("market://details?id=$bundle&url=$schema://$urlAndroid#Intent");
            //bankylite://transfer/98a5975c-ecd9-46f4-a8ec-5fb03aa7fd4d#Intent;scheme=bankylite;package=com.ykb.bankylite;end
            //browser reported as an Android device -- do something here
        } else if ($Windows) {
            $urlWindows="transfer/$txnToken";
            return redirect("$schema://$urlWindows");
        } else {
            if(session()->has("appsecret")){
                $urlWeb="app?txnToken=$txnToken";
                return redirect("https://".request()->header("x-forwarded-host")."/$urlWeb");
            }

            return redirect("https://play.google.com/store/apps/details?id=$bundle");
            // return redirect("/app/$token");
            //browser reported as a webOS device -- do something here
        }
    }
}
