<?php

namespace App\Http\Controllers\Digx;

use App\Data\AccountData;
use App\Data\GeneralResponseData;
use App\Data\ManualHarvestAdminData;
use App\Data\ManualHarvestResponseData;
use App\Enums\ServiceTagEnum;
use App\Http\Controllers\Controller;
use App\LogItem;
use App\Models\ManualHarvest;
use App\Services\NotificationService;
use App\Services\OBDX\CustomerService;
use App\Traits\AuthorizesServices;
use Illuminate\Http\Request;

class ManualHarvestController extends Controller
{
    use AuthorizesServices;

    /**
     * Get the service tags for authorization.
     */
    protected function getServiceTags(): array
    {
        return [ServiceTagEnum::CLAIM_MONEY];
    }

    /**
     * Create a manual processing request for remittances that cannot be processed through Yeah Money.
     */
    public function store(Request $request)
    {
        $validator = validator()->make($request->all(), [
            'trackingCode' => 'required|string|max:100',
            'targetAccountId.value' => 'required|string|min:20|max:20',
            'remittanceCurrency.currency' => 'required|string|min:3|max:3',
            'remittanceCurrency.amount' => 'nullable|numeric|min:0',
            'customerNotes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json(GeneralResponseData::from([
                'status' => [
                    "result" => "ERROR",
                    "contextID" => "STORE-MANUAL-HARVEST",
                    "message" => [
                        "title" => join("\n", $validator->errors()->all()),
                        "detail" => join("\n", $validator->errors()->all()),
                        "code" => "DIGX_SWITCH_MANUAL_HARVEST_100",
                        "type" => "ERROR"
                    ]
                ]
            ]));
        }

        $user = $request->user()->userProfile;
        
        // Check if manual request already exists for this tracking code
        $existingRequest = ManualHarvest::where('tracking_code', $request->trackingCode)
            ->where('party_id', auth()->user()->id)
            ->first();
            
        if ($existingRequest) {
            return response()->json(GeneralResponseData::from([
                'status' => [
                    "result" => "ERROR",
                    "contextID" => "STORE-MANUAL-HARVEST-DUPLICATE",
                    "message" => [
                        "title" => "Manual processing request already exists for this tracking code",
                        "detail" => "Request ID: {$existingRequest->id}",
                        "code" => "DIGX_SWITCH_MANUAL_HARVEST_101",
                        "type" => "ERROR"
                    ]
                ]
            ]));
        }

        // Validate account ownership
        $accountId = $request->input('targetAccountId.value');
        $accountResult = CustomerService::account($request, $accountId);
        
        if (!($accountResult instanceof AccountData)) {
            return response()->json(GeneralResponseData::from([
                'status' => [
                    "result" => "ERROR",
                    "contextID" => "STORE-MANUAL-HARVEST-ACCOUNT",
                    "message" => [
                        "title" => "Invalid account or account does not belong to you",
                        "detail" => "",
                        "code" => "DIGX_SWITCH_MANUAL_HARVEST_102",
                        "type" => "ERROR"
                    ]
                ]
            ]));
        }

        $networkType = ManualHarvest::determineNetworkType($request->trackingCode);
        
        // Create manual harvest request
        $manualHarvest = new ManualHarvest();
        LogItem::store($manualHarvest);
        
        $manualHarvest->tracking_code = $request->trackingCode;
        $manualHarvest->network_type = $networkType;
        $manualHarvest->target_account_id = $request->targetAccountId;
        $manualHarvest->remittance_currency = $request->remittanceCurrency;
        $manualHarvest->customer_notes = $request->customerNotes;
        
        // Store receiver information from user profile
        $manualHarvest->receiver_info = [
            'full_name' => $user->firstName . ' ' . $user->middleName . ' ' . $user->lastName,
            'phone_number' => $user->phoneNumber->value,
            'email' => $user->emailId->value,
            'party_id' => $user->partyId->value,
            'address' => $user->address,
        ];
        
        $manualHarvest->save();

        return response()->json(GeneralResponseData::from([
            'status' => [
                "result" => "SUCCESSFUL",
                "contextID" => "",
                "message" => [
                    "title" => "Manual processing request created successfully",
                    "detail" => "Your request has been submitted for manual processing. You will be notified once it's completed.",
                    "code" => "0",
                    "type" => "INFO"
                ]
            ]
        ])->additional([
            'manualHarvest' => ManualHarvestResponseData::fromModel($manualHarvest)
        ]));
    }

    /**
     * List manual processing requests for the authenticated user.
     */
    public function index(Request $request)
    {
        $manualHarvests = ManualHarvest::orderBy('id', 'DESC')
            ->skip($request->input('from', 0))
            ->take($request->input('limit', 20))
            ->get();

        $response = $manualHarvests->map(function ($manualHarvest) {
            return ManualHarvestResponseData::fromModel($manualHarvest);
        });

        return response()->json($response);
    }

    /**
     * Show a specific manual processing request.
     */
    public function show(ManualHarvest $manualHarvest)
    {
        return response()->json(ManualHarvestResponseData::fromModel($manualHarvest));
    }

    /**
     * Admin: List all manual processing requests (without customer scope).
     */
    public function adminIndex(Request $request)
    {
        $query = ManualHarvest::forAdmin()
            ->with(['user', 'processor'])
            ->orderBy('id', 'DESC');

        // Filter by status if provided
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Filter by network type if provided
        if ($request->has('network_type')) {
            $query->where('network_type', 'like', '%' . $request->network_type . '%');
        }

        $manualHarvests = $query
            ->skip($request->input('from', 0))
            ->take($request->input('limit', 50))
            ->get();

        $response = $manualHarvests->map(function ($manualHarvest) {
            $data = ManualHarvestResponseData::fromModel($manualHarvest);
            // Add admin-specific fields
            $data->receiverInfo = $manualHarvest->receiver_info;
            $data->senderInfo = $manualHarvest->sender_info;
            $data->remittanceDetails = $manualHarvest->remittance_details;
            $data->adminNotes = $manualHarvest->admin_notes;
            return $data;
        });

        return response()->json($response);
    }

    /**
     * Admin: Update manual processing request.
     */
    public function adminUpdate(Request $request, ManualHarvest $manualHarvest)
    {
        $validator = validator()->make($request->all(), [
            'status' => 'required|integer|in:0,1,2,-1,-2',
            'senderInfo' => 'nullable|array',
            'remittanceDetails.amount' => 'nullable|numeric|min:0',
            'remittanceDetails.currency' => 'nullable|string|min:3|max:3',
            'adminNotes' => 'nullable|string|max:2000',
            'externalReferenceId' => 'nullable|string|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json(GeneralResponseData::from([
                'status' => [
                    "result" => "ERROR",
                    "contextID" => "UPDATE-MANUAL-HARVEST-ADMIN",
                    "message" => [
                        "title" => join("\n", $validator->errors()->all()),
                        "detail" => join("\n", $validator->errors()->all()),
                        "code" => "DIGX_SWITCH_MANUAL_HARVEST_ADMIN_100",
                        "type" => "ERROR"
                    ]
                ]
            ]));
        }

        $adminData = ManualHarvestAdminData::fromRequest($request->all());
        
        // Update fields
        if ($adminData->senderInfo) {
            $manualHarvest->sender_info = $adminData->senderInfo;
        }
        
        if ($adminData->remittanceDetails) {
            $manualHarvest->remittance_details = $adminData->remittanceDetails;
        }
        
        if ($adminData->adminNotes) {
            $manualHarvest->admin_notes = $adminData->adminNotes;
        }
        
        if ($adminData->externalReferenceId) {
            $manualHarvest->external_reference_id = $adminData->externalReferenceId;
        }

        // Handle status changes
        $oldStatus = $manualHarvest->status;
        $newStatus = $adminData->status;
        
        if ($oldStatus !== $newStatus) {
            switch ($newStatus) {
                case ManualHarvest::STATUS_PROCESSING:
                    $manualHarvest->markAsProcessing(auth()->user()->id);
                    break;
                case ManualHarvest::STATUS_COMPLETED:
                    $manualHarvest->markAsCompleted($adminData->externalReferenceId);
                    // Send notification to customer
                    NotificationService::send($manualHarvest->party_id, [
                        'title' => 'Manual Remittance Processed',
                        'body' => "Your remittance {$manualHarvest->tracking_code} has been successfully processed.",
                        'type' => 'harvest_completed'
                    ]);
                    break;
                case ManualHarvest::STATUS_FAILED:
                    $manualHarvest->markAsFailed($adminData->adminNotes);
                    // Send notification to customer
                    NotificationService::send($manualHarvest->party_id, [
                        'title' => 'Manual Remittance Failed',
                        'body' => "Your remittance {$manualHarvest->tracking_code} could not be processed. Please contact support.",
                        'type' => 'harvest_failed'
                    ]);
                    break;
                default:
                    $manualHarvest->status = $newStatus;
                    $manualHarvest->save();
            }
        } else {
            $manualHarvest->save();
        }

        return response()->json(GeneralResponseData::from([
            'status' => [
                "result" => "SUCCESSFUL",
                "contextID" => "",
                "message" => [
                    "title" => "Manual harvest request updated successfully",
                    "detail" => "",
                    "code" => "0",
                    "type" => "INFO"
                ]
            ]
        ])->additional([
            'manualHarvest' => ManualHarvestResponseData::fromModel($manualHarvest->fresh())
        ]));
    }
}
