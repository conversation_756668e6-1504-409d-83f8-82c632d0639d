<?php

namespace App\Jobs;

use App\Models\Cardless;
use App\SmsVerfiy;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeEncrypted;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;

class SendSmsNotification implements ShouldQueue, ShouldBeEncrypted
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     * @param array $data
     * @return void
     */
    public function __construct(
        public array $data
    ){
        //
    }

    /* Get the tags that should be assigned to the job.
    *
    * @return array<int, string>
    */
    public function tags(): array
    {
        $tags=['sms'];
        if(isset($this->data['user_id'])){
            $tags[]='userId:'.$this->data['user_id']??'';
        }
        if(isset($this->data['mobile'])){
            $tags[]='userPhone:'.$this->data['mobile']??'';
        }
        if(isset($this->data['type'])){
            $tags[]='type:'.$this->data['type']??'';
        }
        return $tags;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {

        $settings=app(\App\Settings\ThirdPartySettings::class)->sms;
        if($settings->is_test){
            $settings=$settings->test_data;
        }

        $fields=$this->data;
        $response = rescue(function () use($fields,$settings){
            $refId=round(microtime(true));
            return Http::post(
                $settings->url,
                [
                    "user"=> $settings->client_id,
                    "password"=> $settings->client_secret,
                    "sender"=> $settings->instid,
                    "refId"=> "$refId",
                    "mobile"=> $fields['mobile'],
                    "message"=> $fields['message'],
                ]
            );
        }, function ($e) {
            return $e->getMessage();
        });

        if(!is_string($response)){
            $result=$response->body();
            if ($response->failed()) {
                \Log::error("$result");
            }
        }
        return;
    }
}
