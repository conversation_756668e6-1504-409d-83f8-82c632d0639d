<?php

namespace App\Data\Limit;

use App\Data\CurrencyAmountData;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Normalizers\ObjectNormalizer;
use Spatie\LaravelData\DataCollection;
class LimitData extends Data
{
    public ?string $limitType;
    public ?CurrencyAmountData $maxAmount;
    public ?LimitAmountRangeData $amountRange;

    public ?int $maxCount;

    public ?string $periodicity;

}
