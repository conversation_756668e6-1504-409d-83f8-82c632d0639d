<?php

namespace App\Http\Controllers\Digx;

use App\Data\AccountData;
use App\Data\CardData;
use App\Data\CustomCardData;
use App\Data\GeneralResponseData;

use App\Data\TransactionData;
use App\Data\VirtualCardData;
use App\Enums\ServiceTagEnum;
use App\Http\Controllers\Controller;
use App\Services\FlexService;
use App\Services\OBDX\CustomerService;
use App\Services\UtilityPayementService;
use App\Services\VirtualCardService;
use App\Traits\AuthorizesServices;
use Illuminate\Http\Request;
use Spatie\LaravelData\DataCollection;

class VirtualCardController extends Controller
{
    use AuthorizesServices;
    protected function getServiceTags(): array{
        return [
            ServiceTagEnum::CARD_MANAGMENT
        ];
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */

    public function index(Request $request)
    {
        //Get vc card
        $user=$request->user()->userProfile;
        $object=new \stdClass();
        $object->partyId=$user->partyId->value;
        $object->phone=$request->user()->phone;

        $cards=[];
        $result=VirtualCardService::getCards($object);
        if(isset($result->getAdditionalData()["virtualCards"])){
            $cards=array_merge($cards,$result->getAdditionalData()["virtualCards"]->toArray());
        }

        //Get credit card
        $result=CustomerService::accounts($request);
        if(!($result instanceof GeneralResponseData)){
            $account=$result->filter(function (AccountData $element,int $key){
                return $element->status=="ACTIVE" && $element->isCreditCard();
            })->first();
            if(!is_null($account)){
                $object=new \stdClass();
                $object->account_id= $account->id->value;
                $result=FlexService::getCustomerCreditCardInfo($object);
                if(isset($result->getAdditionalData()["creditCards"])){
                    if(!is_array($result->getAdditionalData()["creditCards"])){
                        $cards=array_merge($cards,$result->getAdditionalData()["creditCards"]->toArray());
                    }
                }
            }
        }

        //Get card from fav
        $result=CustomerService::billers($request,['INTERNET_CARD','MASTERCARD']);
        if(!($result instanceof GeneralResponseData)){
            $cards=array_merge($cards,CustomCardData::collect($result,DataCollection::class)->toArray());
        }

        if(!empty($cards)){
            $cards=CardData::collect($cards,DataCollection::class);
        }

        return response()->json(GeneralResponseData::from([
            'status' => [
                "result" => "SUCCESSFUL",
                "contextID" => "",
                "message" => [
                    "code" => "0",
                    "type" => "INFO"
                ]
            ]
        ])->additional([
            'virtualCards'=>$cards
        ]));

        //return response()->json($cards);

    }
    /**
     * Display the specified resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($number,Request $request)
    {
        $cardDetails=new \stdClass();



        /*$object=new \stdClass();
        $object->ac=4011;
        $object->sc=50002;
        $object->sno=$number;
        $object->msisdn=$request->user()->phone;

        $result=UtilityPayementService::transactions($object);
        if(isset($result->ExtData->transactionHistoryList)){
            $cardDetails->transactions=TransactionData::collect($result->ExtData->transactionHistoryList,DataCollection::class);
        }else{
            $object=new \stdClass();
            $object->partyId=$request->user()->id;
            $object->phone=$request->user()->phone;

            $result=VirtualCardService::getCards($object);
            if(!isset($result->getAdditionalData()["virtualCards"])){
                return response()->json(GeneralResponseData::from([
                    'status'=>[
                        "result"    => "ERROR",
                        "contextID" => "CARD-DETAILS",
                        "message"   => [
                            "title"   => __("You don't have a permission!"),
                            "detail"  => "",
                            "code"    => "DIGX_SWITCH_V_CARD_101",
                            "type"    => "ERROR"
                        ]
                    ]
                ]),\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);
            }

            $cards=$result->getAdditionalData()["virtualCards"];
            $card=$cards->filter(function (VirtualCardData $element,int $key) use($number) {
                return $element->cardNumber==$number;
            })->first();

            if(is_null($card)){
                return response()->json(GeneralResponseData::from([
                    'status'=>[
                        "result"    => "ERROR",
                        "contextID" => "CARD-DETAILS",
                        "message"   => [
                            "title"   => __("You don't have a permission!"),
                            "detail"  => "",
                            "code"    => "DIGX_SWITCH_V_CARD_102",
                            "type"    => "ERROR"
                        ]
                    ]
                ]),\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);
            }

            $object=new \stdClass();
            $object->ac=4011;
            $object->sc=50002;
            $object->sno=$card->cardNumber;
            $object->msisdn=$card->msisdn;

            $result=UtilityPayementService::transactions($object);
            if(isset($result->ExtData->transactionHistoryList)){
                $cardDetails->transactions=TransactionData::collect($result->ExtData->transactionHistoryList,DataCollection::class);
            }
        }

        $object=new \stdClass();
        $object->ac=4010;
        $object->sc=50002;
        $object->sno=$number;

        $result=UtilityPayementService::query($object);
        if(isset($result->ExtData->Balance)){
            $cardDetails->balance=CurrencyAmountData::from([
                "amount"=> $result->ExtData->Balance,
                "currency"=> $result->ExtData->Currency,
            ]);
        }*/

        $object=new \stdClass();
        $object->ac=4012;
        $object->sc=50002;
        $object->sno=$number;

        $result=UtilityPayementService::query($object);
        if(isset($result->ExtData->Status)){
            $cardDetails->statusName=$result->ExtData->Status;
        }else{
            $cardDetails->statusName="UNBLOCKED";
        }




        return response()->json(GeneralResponseData::from(array(
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ))->additional(collect($cardDetails)->toArray()));
    }

     /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param   $number
     * @return ?\Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $number)
    {

        $phone=$request->user()->phone;

        $object=new \stdClass();
        $object->ac=4011;
        $object->sc=50002;
        $object->sno=$number;
        $object->msisdn=$phone;

        $result=UtilityPayementService::transactions($object);
        if(!isset($result->ExtData->transactionHistoryList)){
            $object=new \stdClass();
            $object->partyId=$request->user()->id;
            $object->phone=$phone;

            $result=VirtualCardService::getCards($object);
            if(!isset($result->getAdditionalData()["virtualCards"])){
                return response()->json(GeneralResponseData::from([
                    'status'=>[
                        "result"    => "ERROR",
                        "contextID" => "CHANGE-STATUS",
                        "message"   => [
                            "title"   => __("You don't have a permission!"),
                            "detail"  => "",
                            "code"    => "DIGX_SWITCH_V_CARD_101",
                            "type"    => "ERROR"
                        ]
                    ]
                ]));
            }

            $cards=$result->getAdditionalData()["virtualCards"];
            $card=$cards->filter(function (VirtualCardData $element,int $key) use($number) {
                return $element->cardNumber==$number;
            })->first();

            if(is_null($card)){
                return response()->json(GeneralResponseData::from([
                    'status'=>[
                        "result"    => "ERROR",
                        "contextID" => "CHANGE-STATUS",
                        "message"   => [
                            "title"   => __("You don't have a permission!"),
                            "detail"  => "",
                            "code"    => "DIGX_SWITCH_V_CARD_101",
                            "type"    => "ERROR"
                        ]
                    ]
                ]));
            }

            $phone=$card->msisdn;
        }

        $object=new \stdClass();
        $object->ac=4020;
        $object->sc=50002;
        $object->sno=$number;
        $object->msisdn=$phone;

        $result=UtilityPayementService::status($object);
        if(isset($result->RC) && $result->RC==0){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "SUCCESSFUL",
                    "contextID" => "",
                    "message"   => [
                        "title"   => __("Successfully change card status"),
                        "detail"  => "",
                        "code"    => "0",
                        "type"    => "INFO"
                    ]
                ]
            ]));
        }

        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "CHANGE-STATUS",
                "message"   => [
                    "title"   => __("Can't change status!"),
                    "detail"  => "",
                    "code"    => "DIGX_SWITCH_V_CARD_103",
                    "type"    => "ERROR"
                ]
            ]
        ]));


    }

    public function transactions(Request $request)
    {
        $cardDetails=new \stdClass();

        $phone=$request->user()->phone;

        $object=new \stdClass();
        $object->ac=4011;
        $object->sc=50002;
        $object->sno=$request->number;
        $object->msisdn=$phone;
        $object->fromDate=$request->fromDate;
        $object->toDate=$request->toDate;
        $object->limit=$request->limit;

        $result=UtilityPayementService::transactions($object);
        if(isset($result->ExtData->transactionHistoryList)){
            $cardDetails->transactions=TransactionData::collect($result->ExtData->transactionHistoryList,DataCollection::class);
        }else{
            $object=new \stdClass();
            $object->partyId=$request->user()->id;
            $object->phone=$request->user()->phone;

            $result=VirtualCardService::getCards($object);
            if(!isset($result->getAdditionalData()["virtualCards"])){
                return response()->json(GeneralResponseData::from([
                    'status'=>[
                        "result"    => "ERROR",
                        "contextID" => "CARD-DETAILS",
                        "message"   => [
                            "title"   => __("You don't have a permission!"),
                            "detail"  => "",
                            "code"    => "DIGX_SWITCH_V_CARD_101",
                            "type"    => "ERROR"
                        ]
                    ]
                ]),\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);
            }

            $cards=$result->getAdditionalData()["virtualCards"];
            $card=$cards->filter(function (VirtualCardData $element,int $key) use($request) {
                return $element->cardNumber==$request->number;
            })->first();

            if(is_null($card)){
                return response()->json(GeneralResponseData::from([
                    'status'=>[
                        "result"    => "ERROR",
                        "contextID" => "CARD-DETAILS",
                        "message"   => [
                            "title"   => __("You don't have a permission!"),
                            "detail"  => "",
                            "code"    => "DIGX_SWITCH_V_CARD_102",
                            "type"    => "ERROR"
                        ]
                    ]
                ]),\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);
            }
            $object=new \stdClass();
            $object->ac=4011;
            $object->sc=50002;
            $object->sno=$card->cardNumber;
            $object->msisdn=$card->msisdn;
            $object->fromDate=$request->fromDate;
            $object->toDate=$request->toDate;
            $object->limit=$request->limit;

            $result=UtilityPayementService::transactions($object);
            if(isset($result->ExtData->transactionHistoryList)){
                $cardDetails->transactions=TransactionData::collect($result->ExtData->transactionHistoryList,DataCollection::class);
            }
        }

        return response()->json(GeneralResponseData::from(array(
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => "",
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
        ))->additional(collect($cardDetails)->toArray()));

    }


     /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $number
     * @return ?\Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, $number)
    {

        $result=CustomerService::billers($request,['INTERNET_CARD','MASTERCARD']);
        if(!($result instanceof GeneralResponseData)){
            $billers=collect($result)->where('relationshipNumber',$number);
            foreach ( $billers as $value) {
                $result=CustomerService::billerRemove($request,$value->billerId,$value->relationshipNumber);
            }
        }
        return response()->json($result);
    }

}
