<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\CacheModel;
use Auth;
use DB;

//use Spatie\Permission\Models\Permission;

class UserInterface extends CacheModel
{

    // public function scopeWithAttributes($query){
    //     $query->addSelect(['subname' => Permission::
    //         select(DB::raw("DISTINCT concat('0;' , SUBSTRING_INDEX(SUBSTRING_INDEX(name,'.',$count+1),'.',-1)) as name"))
    //         ->whereRaw("SUBSTRING_INDEX(name,'.',$count)={$prefix}user_interfaces.identifier")
    //     // ->whereRaw("SUBSTRING_INDEX(name,'.',?)=?",[$count,$permission->identifier])
    //         ->pluck('name')->join(',')
    //         //->groupBy('product_id' )
    //         //->having('price',\DB::raw("min(price)"))
    //         //->take(1)
    //     ]);
    // }
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = ['name', 'identifier', 'route','icon', 'status', 'sort','guard'];
    public function children(){
        return $this->hasMany('App\Models\UserInterface','identifier_level1','identifier')
        ->where('status', '>=', 1)
        ->whereRaw("(LENGTH(identifier) - LENGTH(REPLACE(identifier,'.',''))) >= 1")->orderBy('sort');
        //return $this->hasMany('App\SubUserInterface',  'parent_identifier', 'identifier')->orderBy('sort');
    }
    public function subNames(){
        return $this->hasMany('App\Models\UserInterface','identifier_level1','identifier')
        ->whereRaw("(LENGTH(identifier) - LENGTH(REPLACE(identifier,'.',''))) >= 1")->orderBy('sort');
        //return $this->hasMany('App\SubUserInterface',  'parent_identifier', 'identifier')->orderBy('sort');
    }
    public static function getSideMenu(){
        return static::getWithCache('sidemenu'.auth()->id(), function ($query) {
            $items= $query->with(['children'=>function($quary){
                if(!Auth::user()->hasRole('developer'))
                  $quary->whereIn('identifier',function ($query) {
                    $query->select(DB::raw("SUBSTRING_INDEX(name,'.',2)"))
                    ->from('permissions')
                    ->whereIn('id',function ($query) {
                        $query->select("permission_id")
                            ->from('role_has_permissions')
                            ->whereIn('role_id',function ($query) {
                                $query->select("role_id")
                                    ->from('model_has_roles')
                                    ->where('model_id', auth()->user()->id);
                            });
                    })
                    ->whereRaw("SUBSTRING_INDEX(name,'.',-1) in('*','list','view')");
                });
            }])
            ->whereIn('status',[2,3])
            ->whereRaw("identifier not like '%.%'");

            if(!Auth::user()->hasRole('developer')){
                $items=$items->whereIn('identifier',function ($query) {
                    $query->select(DB::raw("SUBSTRING_INDEX(name,'.',1)"))
                    ->from('permissions')
                    ->whereIn('id',function ($query) {
                        $query->select("permission_id")
                            ->from('role_has_permissions')
                            ->whereIn('role_id',function ($query) {
                                $query->select("role_id")
                                    ->from('model_has_roles')
                                    ->where('model_id', auth()->user()->id);
                            });
                    })
                    ->whereRaw("SUBSTRING_INDEX(name,'.',-1) in('*','list','view')");
                });
            }
            // else{
            //     $items=$items->whereIn('status',[2,3]);
            // }

            return $items->orderBy('sort')->get();
        });
    }

    public function permissions(){
        return $this->hasMany('App\Models\Permission','name_level1','identifier_level1');
      //  return $this->hasMany('App\Models\UserInterface','identifier_level1','identifier_level1');

    }
    // public function subPermissions(){
    //     return $this->hasMany('App\Models\Permission','name_level2','identifier_level1');

    // }


}
