<?php

namespace App\Data;

use <PERSON><PERSON>\LaravelData\Attributes\Validation\Nullable;
use <PERSON><PERSON>\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Data;

class HarvestRequestData extends Data
{
    #[Required]
    public string $trackingCode;
    #[Required]
    public ?CurrencyAmountData $amount;
    #[Required]
    public ?string $senderPhone;
    #[Required]
    public ?AccountIdData $creditAccountId;
    #[Required]
    public int $serviceCode;

    #[Required]
    public ?int $branchId;

    #[Nullable]
    public ?string $firstName;
    #[Nullable]
    public ?string $secondName;
    #[Nullable]
    public ?string $thirdName;
    #[Nullable]
    public ?string $lastName;
    #[Nullable]
    public ?string $fullName;
    public function fullName():string {
        if(!is_null($this->fullName)){
            return $this->fullName;
        }
       return "{$this->firstName} {$this->secondName} {$this->thirdName} {$this->lastName}";
    }

    public function payoutInfoParams():array {
        $params=[
            "Receiver_First_Name"   =>$this->firstName,
            "Receiver_Second_Name"  =>$this->secondName,
            "Receiver_Third_Name"   =>$this->thirdName,
            "Receiver_Surname"      =>$this->lastName,
            "Receiver_Mobile"       =>html_entity_decode(auth()->user()->phone),
            "Payout_Amount"         =>is_null($this->amount)?"": $this->amount->amount,
            "Payout_Currency_Code"  =>is_null($this->amount)?"": $this->amount->currency,
            "Sender_Mobile"         =>$this->senderPhone,
            "Receiver_Type"         =>'I',
        ];
        if(in_array(auth()->user()->customerRole??'',['full','maker','checker'])){
            $params['Receiver_Full_Name']=$this->fullName();
            $params['Receiver_Type']='C';
        }
        return $params;
     }

   // public static function ()
}
