<?php

namespace App\Data\Agent;
use App\Data\BaseNonNullableData;
use DateTime;
use Illuminate\Support\Collection;

use App\Data\AccountIdData;
use App\Data\CurrencyAmountData;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Attributes\Validation\Nullable;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\WithCast;
use Spatie\LaravelData\Casts\DateTimeInterfaceCast;
use Spatie\LaravelData\Data;

class AgentTransactionData extends BaseNonNullableData
{
   public ?int $id;

   #[MapInputName('external_reference_id')]
   public ?string $externalReferenceId;

   public ?CurrencyAmountData $amount;
   public ?CurrencyAmountData $fee;

   public ?CurrencyAmountData $agentCommission;

   #[MapInputName('customer_account_id')]
   public ?AccountIdData $customerAccountId;

   // #[MapInputName('created_at'),WithCast(DateTimeInterfaceCast::class)]
   #[MapInputName('created_at')]
   public ?string $date;

   public ?string $senderName;
   // protected function exceptProperties() : array
   // {
   //     return [
   //         'externalReferenceId'=>is_null($this->externalReferenceId),
   //     ];
   // }
   public static function prepareForPipeline(array $properties) : array
   {

      $properties['agentCommission']= [
         "amount"=>data_get($properties, "init_response_data.Settllement_Info.Settlement_Fee_Amount")??0.0,
         "currency"=>data_get($properties,"init_response_data.Settllement_Info.Settlement_Fee_Currency_Code")??"",
      ];
      $properties['fee']= [
         "amount"=>data_get($properties,"init_response_data.Payin_Info.Payin_Fee_Amount")??0.0,
         "currency"=>data_get($properties,"init_response_data.Payin_Info.Fee_Currency_Code")??"",
      ];
      $properties['senderName']= data_get($properties,"request_data.firstName")." ".data_get($properties,"request_data.secondName")." ".data_get($properties,"request_data.thirdName")." ".data_get($properties,"request_data.lastName");
      // $properties['agentCommission']= [
      //    "amount"=>$properties["init_response_data"]["Settllement_Info"]["Settlement_Fee_Amount"],
      //    "currency"=>$properties["init_response_data"]["Settllement_Info"]["Settlement_Fee_Currency_Code"],
      // ];
      // $properties['fee']= [
      //    "amount"=>$properties["init_response_data"]["Payin_Info"]["Payin_Fee_Amount"],
      //    "currency"=>$properties["init_response_data"]["Payin_Info"]["Fee_Currency_Code"],
      // ];
      return $properties;
   }
}
