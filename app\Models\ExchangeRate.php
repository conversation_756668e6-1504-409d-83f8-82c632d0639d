<?php

namespace App\Models;

use App\CacheModel;
use App\Data\CurrencyAmountData;
use App\Enums\CurrencyTypeEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ExchangeRate extends CacheModel
{
    use HasFactory;
    protected $fillable = ["branch","type","currency1",'currency2','buy_rate','sell_rate','area'];
    protected $casts = [
        'buy_rate' => 'float',
        'sell_rate' => 'float',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];

    protected $appends = ['name','rate','debitRate','creditRate'];
    protected $hidden = [
        'buy_difference','sell_difference','deleted_at','currency1','currency2','type','buy_rate','sell_rate','updated_at'
    ];
    public function getRateAttribute()
    {
        return CurrencyAmountData::from([
            "amount"=>1,
            "currency"=>$this->currency1??"",
        ]);
    }
    public function getDebitRateAttribute()
    {
        return CurrencyAmountData::from([
            "amount"=>$this->sell_rate??0.0,
            "currency"=>$this->currency2??"",
            "status"=>$this->sell_difference,
        ]);
    }

    public function getCreditRateAttribute()
    {
        return CurrencyAmountData::from([
            "amount"=>$this->buy_rate??0.0,
            "currency"=>$this->currency2??"",
            "status"=>$this->buy_difference,
        ]);
    }
    public function getNameAttribute()
    {
        return  __("{$this->area}");
    }
    public function rates()
    {
        return $this->hasMany('App\Models\ExchangeRate', "area", "area");
    }

    public static function getAppHomeExchangeRate($areas){
        $currencies=app(\App\Settings\ConfigSettings::class)->exchangeAreaConfig->currencies;

        if((request()->header('appVersion')??0)>1820){
            $items= static::getWithCache('appHomeEX', function ($query) use($currencies) {
                // $areaBranches=app(\App\Settings\ConfigSettings::class)->exchangeAreaConfig->areaBranches;
                // $areas=[];
                return static::select("area")
                ->with(["rates"=>function($query) use($currencies){
                    return $query->select("area","type","currency1",'currency2','buy_rate','sell_rate','buy_difference','sell_difference')
                    ->whereRaw("created_at=(select max(created_AT) from DIGX_LITE_EXCHANGE_RATES d
                        where d.currency1 = DIGX_LITE_EXCHANGE_RATES.currency1 and d.area=DIGX_LITE_EXCHANGE_RATES.area)")
                    ->whereIn("currency1",$currencies)
                    ->groupBy("area")
                    ->groupBy("type")
                    ->groupBy("currency1")
                    ->groupBy("currency2")
                    ->groupBy("buy_rate")
                    ->groupBy("sell_rate")
                    ->groupBy("buy_difference")
                    ->groupBy("sell_difference")
                    ->orderBy('currency1');
                }])
               // ->whereIn('area',$areas)
                ->whereNotNull('area')
                ->groupBy("area")
                ->get();
            });

            $items = collect($items)->filter(function ($item) use ($areas) {
                return in_array($item->area, $areas);
            })->values();
            return $items;
        }else if((request()->header('appVersion')??0)>155){
            return static::select("area")
            ->with(["rates"=>function($query) use($currencies){
                return $query->select("area","type","currency1",'currency2','buy_rate','sell_rate','buy_difference','sell_difference')
                ->whereRaw("created_at=(select max(created_AT) from DIGX_LITE_EXCHANGE_RATES d
                    where d.currency1 = DIGX_LITE_EXCHANGE_RATES.currency1 and d.area=DIGX_LITE_EXCHANGE_RATES.area)")
                ->whereIn("currency1",$currencies)
                ->groupBy("area")
                ->groupBy("type")
                ->groupBy("currency1")
                ->groupBy("currency2")
                ->groupBy("buy_rate")
                ->groupBy("sell_rate")
                ->groupBy("buy_difference")
                ->groupBy("sell_difference")
                ->orderBy('currency1');
            }])
            ->whereIn('area',$areas)
            ->whereNotNull('area')
            ->groupBy("area")
            ->get();
        }

        return null;
    }

}
