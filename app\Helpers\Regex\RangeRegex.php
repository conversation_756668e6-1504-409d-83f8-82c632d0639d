<?php

namespace App\Helpers\Regex;

use InvalidArgumentException;
class RangeRegex
{
    public static function generate($min = null, $max = null)
    {
        $minNumber = 0;
        $maxNumber = null;

        $minIsNull = is_null($min);
        $maxIsNull = is_null($max);

        if (!$minIsNull) {
            $number = static::convertToInt($min);
            $minNumber = $number ?? 0;
        }

        if (!$maxIsNull) {
            $number = static::convertToInt($max);
            if (!is_null($number)) {
                $maxNumber = $number;
            }
        }

        if (!$minIsNull && !$maxIsNull && $minNumber > $maxNumber) {
            [$minNumber, $maxNumber] = [$maxNumber, $minNumber];
        }

        try {
            return static::regex($minNumber, $maxNumber);
        } catch (\Exception $e) {
            return null;
        }
    }
    private static function convertToInt($value)
    {
        if (is_null($value)) return null;

        if (is_int($value)) return $value;

        if (is_string($value) && is_numeric($value)) {
            return (int)$value;
        }

        if (is_float($value)) {
            return (int)$value;
        }

        return null;
    }

    /**
     * Generate a regex that matches any number between $min and $max (inclusive),
     * with up to two decimal places (0, 1 or 2 digits).
     */
    private static function regex(int $min, int $max): string
    {
        // Build the integer‐only core exactly as before...
        $minLen = strlen((string)$min);
        $maxLen = strlen((string)$max);
        $segments = [];

        for ($len = $minLen; $len <= $maxLen; $len++) {
            $start = ($len === $minLen) ? $min : (int) pow(10, $len - 1);
            $end   = ($len === $maxLen) ? $max :    (int) (pow(10, $len) - 1);
            if ($start > $end) {
                continue;
            }
            if ($start === $end) {
                // single literal value
                $segments[] = preg_quote((string)$start, '/');
                continue;
            }
            // same‐length padding
            $s = str_pad((string)$start, strlen((string)$end), '0', STR_PAD_LEFT);
            $e = str_pad((string)$end,   strlen((string)$end), '0', STR_PAD_LEFT);
            $lenE = strlen($e);
            $pattern = '';
            for ($i = 0; $i < $lenE; $i++) {
                if ($s[$i] === $e[$i]) {
                    $pattern .= $s[$i];
                } else {
                    $pattern .= '[' . $s[$i] . '-' . $e[$i] . ']';
                    $remaining = $lenE - $i - 1;
                    if ($remaining > 1) {
                        $pattern .= '\d{' . $remaining . '}';
                    } elseif ($remaining === 1) {
                        $pattern .= '\d';
                    }
                    break;
                }
            }
            $segments[] = $pattern;
        }

        $intPattern = '(?:' . implode('|', $segments) . ')';

        // **Key change**: allow up to two decimals
        //   (?:\.\d{1,2})?  →  optional “.x” or “.xx”
        return '^' . $intPattern . '(?:\.\d{1,2})?$';
    }

/**
 * Returns a regex matching any integer between $min and $max.
 * Uses digit-length segmentation to keep the pattern compact.
 */
// function regex(int $min, int $max): string
// {
//     if ($min > $max) {
//         throw new InvalidArgumentException('Min must be ≤ max.');
//     }

//     $minLen = strlen((string)$min);
//     $maxLen = strlen((string)$max);
//     $segments = [];

//     for ($len = $minLen; $len <= $maxLen; $len++) {
//         // true 10^(len-1) floor for full-length chunks
//         $start = ($len === $minLen) ? $min : (int) pow(10, $len - 1);
//         $end   = ($len === $maxLen) ? $max :    (int) (pow(10, $len) - 1);

//         if ($start > $end) {
//             continue;
//         }

//         $segments[] = $this->buildSegmentPattern($start, $end);
//     }

//     return '/^(?:' . implode('|', $segments) . ')$/';
// }

/**
 * Build a regex fragment matching all numbers between $start and $end,
 * assuming they have the same digit-length.
 */
// function buildSegmentPattern(int $start, int $end): string
// {
//     // If it's just one number, return it literally
//     if ($start === $end) {
//         return preg_quote((string)$start, '/');
//     }

//     $s = str_pad((string)$start, strlen((string)$end),   '0', STR_PAD_LEFT);
//     $e = str_pad((string)$end,   strlen((string)$end),   '0', STR_PAD_LEFT);
//     $len = strlen($e);
//     $pattern = '';

//     for ($i = 0; $i < $len; $i++) {
//         if ($s[$i] === $e[$i]) {
//             // same fixed digit
//             $pattern .= $s[$i];
//         } else {
//             // digit-range at first point of divergence...
//             $pattern .= '[' . $s[$i] . '-' . $e[$i] . ']';
//             // ...then any digit for the remaining positions
//             $remaining = $len - $i - 1;
//             if ($remaining > 1) {
//                 $pattern .= '\d{' . $remaining . '}';
//             } elseif ($remaining === 1) {
//                 $pattern .= '\d';
//             }
//             break;
//         }
//     }

//     return $pattern;
// }

}
