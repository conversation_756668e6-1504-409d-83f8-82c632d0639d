<?php

namespace App\Http\Controllers\Digx;

use App\Data\GeneralResponseData;
use App\Enums\ServiceTagEnum;
use App\Http\Controllers\Controller;
use App\Services\LoanService;
use App\Traits\AuthorizesServices;
use Illuminate\Http\Request;

class LoanController extends Controller
{
    use AuthorizesServices;
    protected function getServiceTags(): array{
        return [
            ServiceTagEnum::ELOAN
        ];
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $object=new \stdClass();
        $object->partyId=auth()->user()->id;
        $result =LoanService::orders($object);
        return response()->json($result,200, [], JSON_OBJECT_AS_ARRAY);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function create(Request $request)
    {
        $object=new \stdClass();
        $object->partyId=auth()->user()->id;
        $result =LoanService::create($object);
        return response()->json($result);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator=validator()->make($request->all(),[
            'product_code'=>"required",
            'amount.amount'=>"required|numeric",
            'amount.currency'=>"required",
            'sector_code'=>"required",
            'activity_id'=>"required",
            'activity_name'=>"required",
            'activity_phone'=>"required",
            'activity_address'=>"required",
            'closest_branch_id'=>"required",
            'payment_reason'=>"nullable"
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-LOAN",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_LOAN_100",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        $object=new \stdClass();
        $object->customer_no = auth()->user()->id;
        $object->product_code = $request->product_code;
        $object->amount = $request->input("amount.amount");
        $object->currency = $request->input("amount.currency");
        $object->sector_code = $request->sector_code;
        $object->activity_id = $request->activity_id;
        $object->activity_name = $request->activity_name;
        $object->activity_phone = $request->activity_phone;
        $object->activity_address = $request->activity_address;
        $object->closest_branch_id = $request->closest_branch_id;
        $object->payment_reason = $request->payment_reason??"";

        $result =LoanService::request($object);
        if(in_array($result->status->message->code,["36","33","31","0"])){
            return response()->json($result);
        }
        return response()->json($result,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);
    }
}
