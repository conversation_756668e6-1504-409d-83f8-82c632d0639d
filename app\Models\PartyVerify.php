<?php

namespace App\Models;

use App\Scopes\CustomerScope;
use App\Scopes\UsernameScope;
use DB;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Str;

class PartyVerify extends Model
{

    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'party_id',
        'username',
        'phone_number',
        'otp',
        'token',
        'attempts_left',
        'resends_left',
        'device_key',
        'terminal',
        'expiry_date',
        'status',
        'customer_type_id',
        'type'
    ];
    protected $casts = [
        'terminal' => 'object'
    ];
    protected $table = 'party_verifies';

    protected static function boot(){
        parent::boot();
        static::addGlobalScope(new CustomerScope);
        static::addGlobalScope(new UsernameScope);

         // auto-sets values on creation
         static::creating(function ($query) {
            if(is_null($query->party_id))
                $query->party_id = auth()->user()->id;
            if(is_null($query->username) && isset(auth()->user()?->username))
                $query->username = auth()->user()->username;
        });
    }

    public function party()
    {
        return $this->belongsTo(Party::class, 'party_id', 'party_id')
                    ->where('username', $this->username);
    }
}

