<?php

namespace App\Http\Controllers\Digx;

use <PERSON><PERSON>\Passport\Exceptions\OAuthServerException;
use <PERSON>yholm\Psr7\Response as Psr7Response;
use Psr\Http\Message\ServerRequestInterface;
use <PERSON><PERSON>\Passport\Http\Controllers\AccessTokenController as ATC;

class AccessTokenController extends ATC 
{
    /**
     * Authorize a client to access the user's account.
     *
     * @param  \Psr\Http\Message\ServerRequestInterface  $request
     * @return \Illuminate\Http\Response
     */
    public function issueToken(ServerRequestInterface $request)
    {
        try {
            $response = $this->withErrorHandling(function () use ($request) {
                return $this->convertResponse(
                    $this->server->respondToAccessTokenRequest($request, new Psr7Response)
                );
            });
        } catch (OAuthServerException $e) {
            // Invalid grant exception, only thrown in case of wrong credentials,
            // See https://github.com/thephpleague/oauth2-server/blob/master/src/Grant/PasswordGrant.php#L107
            if ($e->getCode() === 8 || $e->getCode() === 9) {
                return response()->json([
                    'message' => "Unauthenticated.",
                ], 401);
            }
    
            throw $e;
        }
    
        return $response;
    }
}
