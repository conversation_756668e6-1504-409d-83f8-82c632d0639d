<?php

namespace App\Data\Wheel;

use Illuminate\Support\Collection;
use App\Models\CustomerType;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\DataCollection;
use Spatie\LaravelData\Normalizers\ObjectNormalizer;

class WheelItemsData extends Data
{
    public function __construct(
        #[DataCollectionOf(WheelItemData::class)]
        public ?DataCollection $items
    ) {
    }

}
