<?php

namespace App\Jobs;

use App\Models\PartyVerify;
use App\Models\User;
use App\Scopes\CustomerScope;
use App\Scopes\UsernameScope;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\SendReport;
use App\Models\Notification;
use Laravel\Horizon\Contracts\Silenced;

class SendPushNotification implements ShouldQueue, Silenced
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     * @param array $data
     * @return void
     */
    public function __construct(
        public array $data
    ){
        //
    }

    /* Get the tags that should be assigned to the job.
    *
    * @return array<int, string>
    */
    public function tags(): array
    {
        $tags=['push'];
        if(isset($this->data['user_id'])){
            $tags[]='userId:'.$this->data['user_id']??'';
        }
        if(isset($this->data['extra_id'])){
            $tags[]='id:'.$this->data['extra_id']??'';
        }
        if(isset($this->data['type'])){
            $tags[]='type:'.$this->data['type']??'';
        }
        return $tags;
    }
    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try{
            if(!isset($this->data['tokens']) || empty($this->data['tokens']))
                return;
            if(!isset($this->data['title']) || empty($this->data['title']))
                return;

            $fields = array(
                //'registration_ids'  =>$this->data['tokens'],
                //"content_available"=> $silent,
                'notification'      => array(
                    'body'  =>  $this->data['body']??'',
                    'title' =>  $this->data['title'],
                    'id'    =>  $this->data['extra_id']??'',
                    'type'  =>  $this->data['type']??'',
                    'color' =>  '#'.env('APP_COLOR'),
                    //'sound' =>'bell',
                    //"image"=> "http://192.168.1.0/logo.png",
                )
            );
            if(isset($this->data['image'])){
                $fields["notification"]["photo"]=$this->data['image'];
                $fields["notification"]["image"]=$this->data['image'];
            }
            if(isset($this->data['sound'])){
                $fields["notification"]["sound"]=$this->data['sound'];
            }
            switch($fields['notification']['type']){
                case 'verification':
                    $fields['data']=$fields['notification'];
                    //unset($fields['notification']);
                break;
                case 'unverification':
                    $fields['data']=collect($fields['notification'])->except(['title', 'body'])
                    ->toArray();
                    unset($fields['notification']);
                break;
                case 'general':
                    if(isset($this->data['service_id'])){
                        $fields["notification"]["service_id"]=$this->data['service_id'];
                    }
                    if(isset($this->data['url'])){
                        $fields["notification"]["url"]=$this->data['url'];
                    }
                    $fields['data']=$fields['notification'];
                break;
                case 'update':
                    $fields['data']=$fields['notification'];
                break;
                default:
                break;
            }

            $message = CloudMessage::fromArray($fields);
            $messaging = app('firebase.messaging');
            $result=$messaging->sendMulticast($message,$this->data['tokens']);
            if(isset($this->data['notificationId'])){
                $notification=Notification::find($this->data['notificationId']);
                $notification->result=$result->map(function(SendReport $report) {
                    // $ms= json_encode($report->message()->jsonSerialize());
                    // \Log::error("[Notifications]: error: {$report->error()}");
                    // \Log::error("[Notifications]: message: {$ms}");
                    // \Log::error("[Notifications]: result: {$report->result()}");
                    return $report->result();
                });
                foreach($notification->result as $key=>$report){
                    if(is_null($report)){
                        $partyIds=explode(",",$notification->party_ids);
                        $partyVerify=PartyVerify::withoutGlobalScope(CustomerScope::class)
                        ->withoutGlobalScope(UsernameScope::class)
                        ->whereIn('party_id',$partyIds)
                        ->where('terminal->registrationToken',$this->data['tokens'][$key])
                        ->first();
                        if(!is_null($partyVerify)){
                            $terminal=$partyVerify->terminal;
                            $terminal->registrationToken=null;
                            $partyVerify->terminal=$terminal;
                            $partyVerify->save();
                        }

                    }
                }
                $notification->save();
            }
            return $result;

        }catch(\Exception $e){
           \Log::error("[Notifications]: send");
        }
    }
}
