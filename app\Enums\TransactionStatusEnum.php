<?php

namespace App\Enums;
enum TransactionStatusEnum:int {
    case CANCELED=-2;

    case ERROR=-1;
    case INIT=1;
    case COMPLETED=2;
    case PENDING=3;

    public static function findByValue(string $value):TransactionStatusEnum|null {
        return collect(TransactionStatusEnum::cases())->filter(function($item) use($value){
            return $item->value==$value;
        })->first();
    }
    public static function values():array {
        return collect(static::cases())->pluck("value")->toArray();
    }
}
