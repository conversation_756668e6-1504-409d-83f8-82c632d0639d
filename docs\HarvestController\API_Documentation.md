# HarvestController API Documentation

## Overview
The HarvestController manages money remittance claiming operations through the Yeah Money service. It provides endpoints for listing, creating, confirming, and generating receipts for harvest transactions.

## Base URL
```
/digx/harvest
```

## Authentication
All endpoints require authentication via the `digx.obdx` middleware.

## Endpoints

### 1. List Harvest Transactions
**GET** `/digx/harvest`

Lists completed harvest transactions for the authenticated user.

#### Query Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| from | integer | No | Offset for pagination (default: 0) |
| limit | integer | No | Number of records to return |

#### Response
```json
[
  {
    "id": 123,
    "tracking_code": "YM401707304",
    "sender_phone": "*********",
    "sender_name": "John Doe",
    "amount": {
      "amount": 124.5,
      "currency": "USD"
    },
    "credit_account_id": "40118341500100010001",
    "reference_id": "REF123456789",
    "name": "Yeah Money",
    "status": 1,
    "date": "2023-12-01 10:30:00"
  }
]
```

### 2. Get Available Service Codes
**GET** `/digx/harvest/create`

Returns available harvest service codes for creating new transactions.

#### Response
```json
[
  {
    "id": 1000000,
    "name": "شبكه محلية"
  },
  {
    "id": 1000001,
    "name": "SHIFT Financial Services Ltd."
  }
]
```

### 3. Create Harvest Transaction
**POST** `/digx/harvest`

Initiates a new harvest transaction by validating the tracking code and retrieving remittance information.

#### Request Body
```json
{
  "trackingCode": "YM401707304",
  "amount": {
    "amount": 124.5,
    "currency": "USD"
  },
  "senderPhone": "*********",
  "branchId": "401",
  "serviceCode": 1000000
}
```

#### Validation Rules
| Field | Rules |
|-------|-------|
| trackingCode | required |
| amount.amount | nullable, numeric |
| amount.currency | nullable, min:3, max:3 |
| senderPhone | nullable, min:9, max:9 |
| branchId | required, min:3, max:3 |
| serviceCode | required, numeric |

#### Success Response (200)
```json
{
  "status": {
    "result": "SUCCESSFUL",
    "contextID": "",
    "message": {
      "title": "",
      "detail": "",
      "code": "0",
      "type": "INFO"
    }
  },
  "harvest": {
    "id": 123,
    "amount": {
      "amount": 124.5,
      "currency": "USD"
    },
    "sender_name": "John Doe",
    "receiver_name": "Jane Smith"
  }
}
```

#### Error Responses

**Validation Error (422)**
```json
{
  "status": {
    "result": "ERROR",
    "contextID": "STORE-HARVEST",
    "message": {
      "title": "Validation error message",
      "detail": "Detailed validation errors",
      "code": "DIGX_SWITCH_HARVEST_100",
      "type": "ERROR"
    }
  }
}
```

**Service Code Not Found (200)**
```json
{
  "status": {
    "result": "ERROR",
    "contextID": "STORE-HARVEST-SERVICE-CODE",
    "message": {
      "title": "Service code not found!",
      "detail": "",
      "code": "DIGX_SWITCH_HARVEST_004",
      "type": "ERROR"
    }
  }
}
```

**Already Paid (200)**
```json
{
  "status": {
    "result": "ERROR",
    "contextID": "[YEAH_MONEY_SERVER_ENQUIRY] - 0 - Success",
    "message": {
      "title": "This remittance already paid!",
      "detail": "",
      "code": "DIGX_SWITCH_HARVEST_005",
      "type": "ERROR"
    }
  }
}
```

### 4. Confirm Harvest Transaction
**PUT/PATCH** `/digx/harvest/{id}`

Confirms and processes a harvest transaction by transferring funds to the specified account.

#### Request Body
```json
{
  "creditAccountId": {
    "value": "40118341500100010001"
  }
}
```

#### Validation Rules
| Field | Rules |
|-------|-------|
| creditAccountId.value | required, min:20, max:20 |

#### Success Response (200)
```json
{
  "status": {
    "result": "SUCCESSFUL",
    "contextID": "",
    "message": {
      "title": "Remittance successfully harvest to your account.",
      "detail": "Remittance successfully harvest to your account.",
      "code": "0",
      "type": "INFO"
    }
  },
  "externalReferenceId": "EXT123456789",
  "receipt": {
    "id": "123",
    "date": "2023-12-01 10:30:00",
    "title": "Claim money through (Yeah Money)",
    "sender": "John Doe - (*********)",
    "statement": "Claim money through (Yeah Money)",
    "details": {
      "debitAccountId": "40118341500100010001",
      "remittanceId": "YM401707304",
      "amount": {
        "amount": 124.5,
        "currency": "USD"
      }
    }
  }
}
```

#### Error Responses

**Transaction Expired (200)**
```json
{
  "status": {
    "result": "ERROR",
    "contextID": "CONFIRM-HARVEST",
    "message": {
      "title": "This transaction already expired",
      "detail": "",
      "code": "DIGX_SWITCH_HARVEST_101",
      "type": "ERROR"
    }
  }
}
```

**Invalid Account (200)**
```json
{
  "status": {
    "result": "ERROR",
    "contextID": "STORE-HARVEST",
    "message": {
      "title": "You don't have the correct account!",
      "detail": "",
      "code": "DIGX_SWITCH_HARVEST_102",
      "type": "ERROR"
    }
  }
}
```

### 5. Generate Receipt
**GET** `/digx/harvest/receipt/{id}`

Generates a PDF receipt for a completed harvest transaction.

#### Response
Returns a PDF file download.

## Status Codes

### Transaction Status
- `0`: Pending (awaiting confirmation)
- `1`: Completed successfully
- `-1`: Failed/Rejected

### Error Codes
| Code | Description |
|------|-------------|
| DIGX_SWITCH_HARVEST_100 | Validation error |
| DIGX_SWITCH_HARVEST_004 | Service code not found or remittance inquiry failed |
| DIGX_SWITCH_HARVEST_005 | Remittance already paid |
| DIGX_SWITCH_HARVEST_101 | Transaction expired |
| DIGX_SWITCH_HARVEST_102 | Invalid account |
| DIGX_SWITCH_HARVEST_006 | Payout processing failed |
| DIGX_SWITCH_001 | External service connection error |

## Business Logic Flow

### 1. Create Transaction Flow
1. Validate request parameters
2. Verify service code exists and is active
3. Obtain access token from Yeah Money API
4. Login to Yeah Money service
5. Query remittance information using tracking code
6. Validate remittance is available and not already paid
7. Create harvest record with status 0 (pending)
8. Return transaction details

### 2. Confirm Transaction Flow
1. Validate account ID format
2. Check transaction is still pending (status = 0)
3. Validate account belongs to user and meets criteria:
   - Account is ACTIVE
   - Account is cash account
   - Currency matches remittance currency
   - For YER currency: account region matches branch region
4. Get user card information
5. Submit payout request to Yeah Money API
6. Update transaction status based on result
7. Send notification to user
8. Return confirmation with receipt data

## External Dependencies
- **Yeah Money API**: Third-party remittance service
- **OBDX CustomerService**: Internal banking service for account validation
- **NotificationService**: For sending user notifications
