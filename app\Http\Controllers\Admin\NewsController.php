<?php

namespace App\Http\Controllers\Admin;
use App\Data\GeneralItemData;
use App\Http\Controllers\Controller;
use App\Models\News;
use Illuminate\Http\Request;
use Auth;
use Spatie\LaravelData\PaginatedDataCollection;

class NewsController extends Controller
{

    protected $types=[
        "gold"=>"gold"
    ];

    public function __construct(Request $request){$this->middleware('auth');}
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response|\Illuminate\Contracts\View\View
     */
    public function index(Request $request)
    {
        if (! Auth::user()->canAny(['news.*','news.list'])) return abort(401);

        $filter=$request->all();

        $items=News::select('id','title','type','status')->orderBy('id','desc');


        if($request->filled('type') ){
            $items=$items->where('type',$request->type);
        }

        if($request->filled('searchname'))
            $items=$items->where(function($query) use($filter){
                return $query->where('title->ar','like','%'.$filter['searchname'].'%')
                ->orWhere('title->en','like','%'.$filter['searchname'].'%');
            });

        $items=$items->paginate(15);

      //  GeneralItemData::collect($items->paginate(15));//->wrap('paginated_data');


        return view('default.admin.news.index')
        ->with('items', $items)
        ->with('types', $this->types)
        ->with('filter', $filter);
    }


    /**
     * Show the form for creating a resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function create()
    {
        if (! Auth::user()->canAny(['news.*','news.create'])) return abort(401);

        return view('default.admin.news.view')
        ->with('types', $this->types);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        if (! Auth::user()->canAny(['news.*','news.create'])) return abort(401);

        $this->validate($request,[
            'title.en' => 'required|max:500',
            'title.ar' => 'required|max:500',
            'description.en' => 'required',
            'description.ar' => 'required',
            'url' => 'nullable',
            'source_name' => 'nullable',
            'source_icon' => 'nullable',
            'type' => 'required|in:'.join(",",collect($this->types)->keys()->toArray()),
            'status' => 'required|in:0,1',

        ]);
        return $this->save($request);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\News  $news
     * @return \Illuminate\Contracts\View\View
     */
    public function show(News $news){
        if (! Auth::user()->canAny(['news.*','news.view','news.edit'])) return abort(401);

        return view('default.admin.news.view')
        ->with('item', $news)
        ->with('types', $this->types);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\News  $news
     * @return \Illuminate\Http\Response | \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, News $news)
    {
        if (! Auth::user()->canAny(['news.*','news.edit'])) return abort(401);
        //return response()->json($request->all());
        $this->validate($request,[
            'title.en' => 'required|max:500',
            'title.ar' => 'required|max:500',
            'description.en' => 'required',
            'description.ar' => 'required',
            'url' => 'nullable',
            'source_name' => 'nullable',
            'source_icon' => 'nullable',
            'type' => 'required|in:'.join(",",collect($this->types)->keys()->toArray()),
            'status' => 'required|in:0,1',
        ]);
        return $this->save($request,$news);
    }

    public function save(Request $request, ?News $item=null){
        ini_set('memory_limit', '1024M');
        if(is_null($item)){
            $isNew=true;
            $item = new News;
        }

        $this->setImage('image',"news");
        $this->setImage('source_icon',"news");

        // dd($request->all());
        // return;
        $item->image = $request->image;

        $item->title = $request->title;
        $item->description = $request->description;
        $item->url = $request->url;
        $item->source_name = $request->source_name;
        $item->source_icon = $request->source_icon;
        $item->type = $request->type;
        $item->status = $request->status;


        $item->save();


        if(isset($isNew)){
            return redirect("/admin/news/$item->id")
            ->with('success',__("Operation accomplished successfully"));
        }else{
            return back()->with('success',__("Operation accomplished successfully"));
        }
    }
    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id){
        if (! Auth::user()->canAny(['news.*','news.delete'])) return abort(401);

    }

}
