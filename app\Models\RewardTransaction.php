<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RewardTransaction extends Model
{
    use HasFactory;

    protected $fillable = ["reward_id","sender_party_id","sender_phone","receiver_party_id","receiver_phone","credit_account_id",'amount','reference_id','external_reference_id','gl_service_name','status','type','gift_send_date'];
    protected $casts = [
        'credit_account_id' => 'object',
        'amount' => 'object'

    ];
}
