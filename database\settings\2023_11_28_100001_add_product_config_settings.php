<?php

use App\Models\CustomerType;
use <PERSON><PERSON>\LaravelSettings\Migrations\SettingsMigration;

class AddProductConfigSettings extends SettingsMigration
{
    public function up(): void
    {
        if(env('APP_FLAG',"banky.commercial")=="banky.islamic"){
            $this->migrator->add('config.productConfig',array (
                'values' => [
                    "CURC",
                    "CURI",
                    "CRCC",
                    "CRCI",
                    "CURS",
                    "CRSA",
                    "SAVI",
                    "SAVS",
                    "SDCA",
                    "SNDA",
                    "PBCC",
                    "GI21",
                    "GI24"
                ],
                'current' => [
                    "CURC",
                    "CURI",
                    "CRCC",
                    "CRCI",
                    "CURS",
                    "CRSA",
                    "SDCA"
                ],
                'saving' => [
                    "SAVI",
                    "SAVS",
                ],
                'individual' => [
                    "CURI",
                    "CRCI",
                    "CURS",
                    "SDCA"
                ],
                'ad' => [
                ],
                'st' => [
                    "STCU",
                ],
                'corporation' => [
                    "CURC",
                    "CRCC",
                    "CRSA"
                ],
                'cash' => [
                    "CRCC",
                    "CRCI",
                    "CURS",
                    "SAVI",
                    "SAVS",
                    "SDCA",
                    "SNDA"
                ],
                'cashNoneYER' => [
                    "CURC",
                    "CURI"
                ],
                'cashOut' => [
                    "CRCC",
                    "CRCI",
                    "CURS",
                    "SAVI",
                    "SAVS",
                    "SDCA"
                ],
                'cashOutNoneYER' => [
                    "CURC",
                    "CURI"
                ],
                'eloan' => [
                ],
                'creditCard' => [
                    "PBCC",
                ],
                'gold' => [
                    "GI21",
                    "GI24"
                ],
            ));
        }else{
            $this->migrator->add('config.productConfig',array (
                'values' => [
                    "CUCI",
                    "CUCC",
                    "CUCS",
                    //non cash
                    "CURI",
                    "CUST",
                    "CURC",
                    "SDCA",
                    "SVCI",
                    "SACI",
                    "OVCI",
                    "SNDA",
                    "SAVI",
                    "STCU",
                    "ADSA",
                    "OVCC",
                    "LBPA",
                    "PBCC",
                    "GI21",
                    "GI24",
                    "PNPL"
                ],
                'current' => [
                    "CUCI",
                    "CUCC",
                    "CUCS",
                    //non cash
                    "CURI",
                    "CUST",
                    "CURC",
                    "SDCA"
                ],
                'saving' => [
                    "SVCI",
                    "SACI",
                    "SAVI",
                ],
                'individual' => [
                    "SACI",
                    "CUCI",
                    "OVCI",
                    "SVCI",
                    "SAVI",
                    "SDCA",
                    "SNDA"
                ],
                'ad' => [
                    "ADSA",
                ],
                'st' => [
                    "STCU",
                ],
                'corporation' => [
                    "CUCC",
                    "OVCC"
                ],
                'cash' => [
                    "SACI",
                    "CUCI",
                    "CUCC",
                    "CUCS",
                    "OVCI",
                    "OVCC",
                    "SVCI",
                    "SDCA",
                    "SNDA",
                    "PNPL"
                ],
                'cashNoneYER' => [
                    "CUST",
                    "CURI",
                    "SAVI"
                ],
                'cashOut' => [
                    "SVCI",
                    "CURC",
                    "CUCC",
                    "CUST",
                    "CUCS",
                    "CURI",
                    "CUCI",
                    "SDCA"
                ],
                'cashOutNoneYER' => [
                    "SAVI"
                ],
                'eloan' => [
                    "LBPA",
                ],
                'creditCard' => [
                    "PBCC",
                ],
                'gold' => [
                    "GI21",
                    "GI24"
                ],
            ));
        }

    }

    public function down()
    {
        $this->migrator->delete('config.productConfig');
    }
}
