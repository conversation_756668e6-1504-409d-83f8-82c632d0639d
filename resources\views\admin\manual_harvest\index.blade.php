@extends('layouts.admin')

@section('title', 'Manual Harvest Requests')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Manual Harvest Requests</h3>
                    <div class="card-tools">
                        <div class="input-group input-group-sm" style="width: 150px;">
                            <input type="text" name="table_search" class="form-control float-right" placeholder="Search">
                            <div class="input-group-append">
                                <button type="submit" class="btn btn-default">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Filter Controls -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <select class="form-control" id="statusFilter">
                                <option value="">All Status</option>
                                <option value="0">Pending</option>
                                <option value="1">Processing</option>
                                <option value="2">Completed</option>
                                <option value="-1">Failed</option>
                                <option value="-2">Cancelled</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="networkFilter">
                                <option value="">All Networks</option>
                                <option value="RIA">RIA Money Transfer</option>
                                <option value="Western Union">Western Union</option>
                                <option value="MoneyGram">MoneyGram</option>
                                <option value="Small World">Small World</option>
                                <option value="Al Ansari">Al Ansari Exchange</option>
                                <option value="UAE Exchange">UAE Exchange</option>
                                <option value="Unknown">Unknown Network</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-primary" id="refreshBtn">
                                <i class="fas fa-sync"></i> Refresh
                            </button>
                        </div>
                    </div>

                    <!-- Requests Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="manualHarvestTable">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Tracking Code</th>
                                    <th>Network</th>
                                    <th>Customer</th>
                                    <th>Target Account</th>
                                    <th>Currency</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="requestsTableBody">
                                <!-- Data will be loaded via AJAX -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="row mt-3">
                        <div class="col-sm-12 col-md-5">
                            <div class="dataTables_info" id="tableInfo">
                                Showing 0 to 0 of 0 entries
                            </div>
                        </div>
                        <div class="col-sm-12 col-md-7">
                            <div class="dataTables_paginate paging_simple_numbers" id="tablePagination">
                                <!-- Pagination will be generated -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Processing Modal -->
<div class="modal fade" id="processModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Process Manual Harvest Request</h4>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="processForm">
                    <input type="hidden" id="requestId" name="requestId">
                    
                    <!-- Customer Information (Read-only) -->
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Customer Information</h5>
                            <div class="form-group">
                                <label>Full Name</label>
                                <input type="text" class="form-control" id="customerName" readonly>
                            </div>
                            <div class="form-group">
                                <label>Phone Number</label>
                                <input type="text" class="form-control" id="customerPhone" readonly>
                            </div>
                            <div class="form-group">
                                <label>Target Account</label>
                                <input type="text" class="form-control" id="targetAccount" readonly>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5>Request Details</h5>
                            <div class="form-group">
                                <label>Tracking Code</label>
                                <input type="text" class="form-control" id="trackingCode" readonly>
                            </div>
                            <div class="form-group">
                                <label>Network Type</label>
                                <input type="text" class="form-control" id="networkType" readonly>
                            </div>
                            <div class="form-group">
                                <label>Customer Notes</label>
                                <textarea class="form-control" id="customerNotes" readonly rows="3"></textarea>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <!-- Sender Information (Admin Input) -->
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Sender Information</h5>
                            <div class="form-group">
                                <label>Sender Full Name</label>
                                <input type="text" class="form-control" name="senderInfo[sender_full_name]" id="senderFullName">
                            </div>
                            <div class="form-group">
                                <label>Sender Phone</label>
                                <input type="text" class="form-control" name="senderInfo[sender_phone]" id="senderPhone">
                            </div>
                            <div class="form-group">
                                <label>Sender Country</label>
                                <input type="text" class="form-control" name="senderInfo[sender_country]" id="senderCountry">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5>Remittance Details</h5>
                            <div class="form-group">
                                <label>Amount</label>
                                <input type="number" step="0.01" class="form-control" name="remittanceDetails[amount]" id="remittanceAmount">
                            </div>
                            <div class="form-group">
                                <label>Currency</label>
                                <select class="form-control" name="remittanceDetails[currency]" id="remittanceCurrency">
                                    <option value="USD">USD</option>
                                    <option value="YER">YER</option>
                                    <option value="SAR">SAR</option>
                                    <option value="EUR">EUR</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>External Reference ID</label>
                                <input type="text" class="form-control" name="externalReferenceId" id="externalReferenceId">
                            </div>
                        </div>
                    </div>

                    <!-- Status and Notes -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Status</label>
                                <select class="form-control" name="status" id="requestStatus" required>
                                    <option value="0">Pending</option>
                                    <option value="1">Processing</option>
                                    <option value="2">Completed</option>
                                    <option value="-1">Failed</option>
                                    <option value="-2">Cancelled</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Admin Notes</label>
                                <textarea class="form-control" name="adminNotes" id="adminNotes" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveProcessBtn">Save Changes</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    let currentPage = 0;
    let pageSize = 20;
    
    // Load initial data
    loadRequests();
    
    // Event handlers
    $('#statusFilter, #networkFilter').change(function() {
        currentPage = 0;
        loadRequests();
    });
    
    $('#refreshBtn').click(function() {
        loadRequests();
    });
    
    $('#saveProcessBtn').click(function() {
        saveProcessing();
    });
    
    function loadRequests() {
        const status = $('#statusFilter').val();
        const network = $('#networkFilter').val();
        
        $.ajax({
            url: '/digx/v1/switch/admin/harvest/manual',
            method: 'GET',
            data: {
                from: currentPage * pageSize,
                limit: pageSize,
                status: status,
                network_type: network
            },
            success: function(data) {
                renderTable(data);
            },
            error: function(xhr) {
                console.error('Error loading requests:', xhr);
                alert('Error loading requests');
            }
        });
    }
    
    function renderTable(data) {
        const tbody = $('#requestsTableBody');
        tbody.empty();
        
        data.forEach(function(request) {
            const row = `
                <tr>
                    <td>${request.id}</td>
                    <td>${request.trackingCode}</td>
                    <td>${request.networkType}</td>
                    <td>${request.receiverInfo?.full_name || 'N/A'}</td>
                    <td>${request.targetAccountId?.value || 'N/A'}</td>
                    <td>${request.remittanceCurrency?.currency || 'N/A'}</td>
                    <td>
                        <span class="badge badge-${getStatusBadgeClass(request.status)}">
                            ${request.statusText}
                        </span>
                    </td>
                    <td>${new Date(request.createdAt).toLocaleDateString()}</td>
                    <td>
                        <button class="btn btn-sm btn-primary" onclick="openProcessModal(${request.id})">
                            <i class="fas fa-edit"></i> Process
                        </button>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
    }
    
    function getStatusBadgeClass(status) {
        switch(parseInt(status)) {
            case 0: return 'warning';
            case 1: return 'info';
            case 2: return 'success';
            case -1: return 'danger';
            case -2: return 'secondary';
            default: return 'light';
        }
    }
    
    window.openProcessModal = function(requestId) {
        // Load request details
        $.ajax({
            url: `/digx/v1/switch/admin/harvest/manual/${requestId}`,
            method: 'GET',
            success: function(request) {
                populateModal(request);
                $('#processModal').modal('show');
            },
            error: function(xhr) {
                console.error('Error loading request details:', xhr);
                alert('Error loading request details');
            }
        });
    };
    
    function populateModal(request) {
        $('#requestId').val(request.id);
        $('#customerName').val(request.receiverInfo?.full_name || '');
        $('#customerPhone').val(request.receiverInfo?.phone_number || '');
        $('#targetAccount').val(request.targetAccountId?.value || '');
        $('#trackingCode').val(request.trackingCode);
        $('#networkType').val(request.networkType);
        $('#customerNotes').val(request.customerNotes || '');
        
        // Populate existing data if available
        if (request.senderInfo) {
            $('#senderFullName').val(request.senderInfo.sender_full_name || '');
            $('#senderPhone').val(request.senderInfo.sender_phone || '');
            $('#senderCountry').val(request.senderInfo.sender_country || '');
        }
        
        if (request.remittanceDetails) {
            $('#remittanceAmount').val(request.remittanceDetails.amount || '');
            $('#remittanceCurrency').val(request.remittanceDetails.currency || '');
        }
        
        $('#requestStatus').val(request.status);
        $('#adminNotes').val(request.adminNotes || '');
        $('#externalReferenceId').val(request.externalReferenceId || '');
    }
    
    function saveProcessing() {
        const requestId = $('#requestId').val();
        const formData = $('#processForm').serialize();
        
        $.ajax({
            url: `/digx/v1/switch/admin/harvest/manual/${requestId}`,
            method: 'PATCH',
            data: formData,
            success: function(response) {
                if (response.status.result === 'SUCCESSFUL') {
                    alert('Request updated successfully');
                    $('#processModal').modal('hide');
                    loadRequests();
                } else {
                    alert('Error: ' + response.status.message.title);
                }
            },
            error: function(xhr) {
                console.error('Error saving request:', xhr);
                alert('Error saving request');
            }
        });
    }
});
</script>
@endsection
