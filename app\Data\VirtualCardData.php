<?php

namespace App\Data;

use App\Data\Classes\BranchData;
use App\Data\Classes\ProductData;
use Illuminate\Support\Collection;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Data;

class VirtualCardData extends Data
{

    public ?string $customerId;
    #[MapInputName('deviceNumber')]
    public string $cardNumber;
    public ?string $expiryDate;
    //public ?string $cvv;
    public ?string $walletNumber;
    public ?string $walletCurrency;
    public ?string $msisdn;
    public ?string $registeredEmailId;
    #[MapInputName('embossedName')]
    public ?string $name;
    public ?string $type;

    public function with():array {
        $functions=[];
        if($this->type=="Primary"){
            $functions=[/*"reload","create","status",*/"balance","history"];
        }else{
            $functions=[/*"reload","status",*/"balance","history"];
        }
        return [
            'classification'=>"VC",
            'provider'=>"mastercard",
            'product'=>"Prepaid",
            "functions"=> $functions
            //'statusName'=>"Active",
            //'status'=>1,
        ];
    }
    public static function prepareForPipeline(array $properties) : array
    {
        $properties['walletCurrency']= $properties["walletCurrency"]=="840"?"USD":"YER";
        //$properties['type']= $properties["primary_card"]=="Y"?"Primery":"Supplementary";
        //$properties['expiryDate']= substr($properties["card_renewal_dt"],5, 2).substr($properties["card_renewal_dt"],8, 2);

        return $properties;
    }

}
