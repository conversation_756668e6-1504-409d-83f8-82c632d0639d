<?php

namespace App\Mail;

use App\Models\PartyVerify;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class OtpSent extends Mailable
{
    use Queueable, SerializesModels;

    protected PartyVerify $partyVerify;
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($partyVerify)
    {
        $this->partyVerify=$partyVerify;
    }

    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope()
    {
        return new Envelope(
            subject: 'Login Verification',
        );
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */
    public function content()
    {
        return new Content(
            markdown: 'emails.login.otp',
            with:[
                'otp'=>"{$this->partyVerify->otp}",
                'end'=>$this->partyVerify->expiry_date,
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array
     */
    public function attachments()
    {
        return [];
    }
}
