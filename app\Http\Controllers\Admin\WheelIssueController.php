<?php

namespace App\Http\Controllers\Admin;
use App\Enums\TransactionStatusEnum;
use App\Http\Controllers\Controller;
use App\Models\WheelTransaction;
use App\Models\PartyVerify;
use App\Scopes\CustomerScope;
use App\Scopes\UsernameScope;
use App\Services\OBDX\AdminService;
use Illuminate\Http\Request;
use Auth;



class WheelIssueController extends Controller
{

    public function __construct(Request $request){$this->middleware('auth');}

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response|\Illuminate\Contracts\View\View
     */
    public function index(Request $request)
    {
        if (! Auth::user()->canAny(['wheel.*','wheel.view','wheel.list'])) abort(401);

        $filter=$request->all();

        $items=WheelTransaction::withoutGlobalScope(CustomerScope::class)
        ->with('user')
        ->where('status',TransactionStatusEnum::COMPLETED->value)
        ->whereNull('wheel_item')
        //->whereNotNull("transaction_id")
        ->orderBy('created_at','desc');

        $items=$items->paginate(15);

        return view('default.admin.wheel.index')
        ->with('items', $items)
        ->with('filter', $filter);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        if (! Auth::user()->canAny(['wheel.*','wheel.create']))
            abort(401);

        $this->validate($request,[
            'count' => 'required|numeric',
            'note' => 'required|string|max:500',
            'party_id' => 'required',
        ]);



        $partyVerify=PartyVerify::withoutGlobalScope(CustomerScope::class)
        ->withoutGlobalScope(UsernameScope::class)
        ->where('party_id',$request->party_id)->first();
        if(is_null($partyVerify)){
            return back()->with('error',__("No party found!"));
        }

        $object=new \stdClass();
        $customerInfo = AdminService::customerInfo($object,[
            "partyId"=>$partyVerify->party_id,
            "userType"=>'retailuser'
        ]);

        if(is_null($customerInfo) || !is_object($customerInfo)){
            return back()->with('error',__("No party found!"));
        }

        $partyName=html_entity_decode($customerInfo->firstName)." ".html_entity_decode($customerInfo->middleName??"")." ".html_entity_decode($customerInfo->lastName);


        $transactions=[];
        for ($i=0; $i < $request->count; $i++) {
            $transactions[]=[
                "note"=>$request->note,
                "party_id"=>$partyVerify->party_id,
                "party_name"=>$partyName,
                "user_id"=>auth()->user()->id,
                "status"=>TransactionStatusEnum::COMPLETED->value,
                "created_at"=>\Carbon\Carbon::now()->toDateTimeString()
            ];
        }
        WheelTransaction::insert($transactions);
        return back()->with('success',__("Seccussfully added points!"));

    }

}
