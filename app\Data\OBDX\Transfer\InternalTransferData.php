<?php

namespace App\Data\OBDX\Transfer;

use App\Data\AccountIdData;
use App\Data\BaseNonNullableData;
use App\Data\CurrencyAmountData;
use Arr;


class InternalTransferData extends BaseNonNullableData
{
    public function __construct(
        public ?AccountIdData $debitAccountId,
        public ?CurrencyAmountData $amount,
        public ?string $receiverNumber,
        public ?string $receiverName,
        public ?string $remarks,
    ) {
    }

    public static function prepareForPipeline(array $properties) : array
    {
        $properties['debitAccountId']= data_get($properties,"transferDetails.debitAccountId");
        $properties['amount']= data_get($properties,"transferDetails.amount");
        $properties['receiverNumber']=data_get($properties,"payeeDetails.accountNumber");
        $properties['receiverName']= html_entity_decode(data_get($properties,"payeeDetails.accountName"));
        //if(Arr::has($properties,"transferDetails.remarks")){
            $properties['remarks']= html_entity_decode(data_get($properties,"transferDetails.remarks"));
        //}
        return $properties;
    }

}
