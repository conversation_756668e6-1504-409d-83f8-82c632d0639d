<?php

namespace App\Http\Controllers\Digx;

use App\Data\AccountIdData;
use App\Data\GeneralResponseData;
use App\Data\ReceiptData;
use App\Data\TokenData;
use App\Enums\ServiceTagEnum;
use App\Enums\TransactionStatusEnum;
use App\Enums\YeahMoneyReceivedStatusEnum;
use App\Http\Controllers\Controller;
use App\LogItem;
use App\Models\Harvest;
use App\Models\YeahMoney;
use App\Services\NotificationService;
use App\Services\OBDX\CustomerService;
use App\Traits\AuthorizesServices;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
class YeahMoneyController extends Controller
{
    use AuthorizesServices;

    //protected $tag=ServiceTagEnum::YEAH_MONEY;
    protected $settings;

    protected function getServiceTags(): array{
        return [
            ServiceTagEnum::YEAH_MONEY
        ];
    }
    public function getAccessToken($forceUpdate=false)
    {
        $status=200;
        $token=$this->settings->token;
        if($forceUpdate){
            $token->access_token="";
            $this->settings->token=$token;
        }
        if(!isset($token->access_token)|| is_null($token->access_token)||empty($token->access_token)){
            $response = Http::timeout(10)
            ->withBasicAuth($this->settings->client_id, $this->settings->client_secret)

            ->asForm()
            ->post("{$this->settings->url}/oauth/token",[
                "grant_type"=>"client_credentials"
            ]);

            if ($response->failed()) {
                return $response;
            }
            $status=$response->status();
            $response=$response->object();

            if(isset($response->access_token)){
                $this->settings->token=TokenData::from($response);
                $settings=app(\App\Settings\ThirdPartySettings::class);
                $yeahMoney=$settings->yeahMoney;
                if($yeahMoney->is_test){
                    $test_data=$yeahMoney->test_data;
                    $test_data->token=$this->settings->token;
                    $yeahMoney->test_data=$test_data;
                }else{
                    $yeahMoney->token=$this->settings->token;
                }
                $settings->yeahMoney=$yeahMoney;
                $settings->save();
            }
           // dd($response);

        }
        return $status;
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $user=$request->user()->userProfile;
        $yeahMoney=YeahMoney::skip( $request->from)
        ->take($request->limit)
        ->orderBy("id","DESC");

        if($request->filled('text')){
            $query = $request->input('text');
            if (static::checkPhone($query)) {
                $yeahMoney = $yeahMoney->where('receiver_info->receiver_Mobile','like','%'.$query.'%');
            }else if (is_numeric($query)) {
                $yeahMoney = $yeahMoney->where("external_reference_id",'like','%'.$query.'%');
            }else{
                $yeahMoney = $yeahMoney->where("receiver_info->receiver_Full_Name",'like','%'.$query.'%');
            }
        }

        $yeahMoney=$yeahMoney->get();
        return response()->json($yeahMoney);

    }
    static function checkPhone($phone) {
        $find1 = strlen($phone);
        $find2 = str_starts_with($phone, '70')||
                    str_starts_with($phone, '71')||
                    str_starts_with($phone, '73')||
                    str_starts_with($phone, '77')||
                    str_starts_with($phone, '78');
        return ($find1==9 && $find2);
    }

    public function show($id,Request $request)
    {
        $item=YeahMoney::where("external_reference_id", $id)
        ->first();

        if(is_null($item)){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "RECEIPT-YEAHMONEY",
                    "message"   => [
                        "title"   => __("This transaction not found!"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_YEAHMONEY_101",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        $this->generateReceipt( $this->getReceiptData($item));
    }
    protected function getReceiptData(YeahMoney $item): ReceiptData
    {
        $statement=null;
        if(!is_null($item->payin_info->payin_Notes)){
            $statement= $item->payin_info->payin_Notes;
            if(!is_null($item->payin_info->message_to_Beneficiary)){
                $statement.="\n".$item->payin_info->message_to_Beneficiary;
            }
        }else if(!is_null($item->payin_info->message_to_Beneficiary)){
            $statement= $item->payin_info->message_to_Beneficiary;
        }
        return ReceiptData::from([
            "id"=> $item->id,
            "date"=> date_format(date_create($item->created_at), "Y-m-d H:i:s"),
            "title"=>__("Funds transfer") ." (".__("Yeah money").")",
            "beneficiary"=> $item->receiver_info->receiver_Full_Name.' ('.$item->receiver_info->receiver_Mobile.')',
            "statement"=> $statement??__("Remittance through yeah money network"),
            "details"=> [
                "debitAccountId"=> AccountIdData::from([
                    "value"=> $item->sender_info->sender_Account_No,
                    "displayValue"=> $item->sender_info->sender_Account_No
                ])->toArray() ,
                "remittanceId"=> $item->external_reference_id,
                "amount"=>[
                    'amount'=>$item->payin_data->payin_Amount,
                    'currency'=> $item->payin_data->payout_Currency_Code
                ],
                "fee"=>[
                    'amount'=>$item->trx_info->fee_Amount,
                    'currency'=> $item->trx_info->fee_Currency_Code
                ]
            ]
        ]);
    }
    public function query(Request $request, $id)
    {
        if(($request->header('appVersion')??0)<168){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "QUERY-YEAHMONEY",
                    "message"   => [
                        "title"   => "Please update the app!",
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_YEAHMONEY_101",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        $user=$request->user()->userProfile;
        $yeahMoney=YeahMoney::where("external_reference_id", $id)
        ->first();

        if(is_null($yeahMoney)){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "QUERY-YEAHMONEY",
                    "message"   => [
                        "title"   => "Not found",
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_YEAHMONEY_101",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }
        if($yeahMoney->status!=TransactionStatusEnum::COMPLETED->value){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "QUERY-YEAHMONEY",
                    "message"   => [
                        "title"   => "This transaction already expired",
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_YEAHMONEY_101",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        $this->settings=app(\App\Settings\ThirdPartySettings::class)->yeahMoney;
        if($this->settings->is_test){
            $this->settings=$this->settings->test_data;
        }

        $status=$this->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "QUERY-YEAHMONEY-REMOTE - $status - ".($request->retry??-1),
                    "message"   => [
                        "title"   => "Can't access reverse server, please connect help desk to fix problem!",
                        "detail"  => "Can't access reverse server, please connect help desk to fix problem!",
                        "code"    => "DIGX_SWITCH_001",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }
        // Start getting Payout Info.
        $response = Http::withToken($this->settings->token->access_token)
        // ->withHeaders([
        //     "Trx_Token"     =>str_replace("Bearer ","",$token),
        //     "Service_Code"  =>$serviceCode->code,
        //     "Trx_Ref_Id"    =>$timestamp
        // ])
        ->get(
            "{$this->settings->url}/mt/v1/status",
            [
                "language"  =>"en",
                "currency"  => $yeahMoney->payin_data->payout_Currency_Code,
                "amount"  => $yeahMoney->payin_data->payin_Amount,
                "mobile"          => html_entity_decode($yeahMoney->receiver_info->receiver_Mobile),
                "uniqueTrackingNumber"  =>$yeahMoney->external_reference_id,
                //"serviceCode"  =>"YMNY",
            ]
        );

        if($response->status()==401){
            $request->retry=$request->retry+1;
            return $this->query($request,$id);
        }

        $result=$response->object();
        if(isset($result->resultCode) && $result->resultCode==0 && isset($result->data->status)){
            $status=YeahMoneyReceivedStatusEnum::findByKey($result->data->status);
            //$yeahMoney->check_status=$status->name;
            $yeahMoney->check_status=$status->value;
            if(in_array($status,YeahMoneyReceivedStatusEnum::enabledCheck())){
                $yeahMoney->check_enabled==1;
                $yeahMoney->save();
            }else if(in_array($status,YeahMoneyReceivedStatusEnum::disabledCheck())){
                $yeahMoney->check_enabled=0;
                $yeahMoney->save();
            }
            $yeahMoney->save();
            return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "SUCCESSFUL",
                "contextID" => "",
                "message"   => [
                    "title"   => $result->data->status,
                    "detail"  => "",
                    "code"    => "0",
                    "type"    => "INFO"
                ]
            ]
            ])->additional([
                'data'=>[
                    "check_status"=>$yeahMoney->check_status,
                    "check_enabled"=>$yeahMoney->check_enabled,
                ]
            ]));
        }

        $resultStatus=\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED;
        if(in_array(($result->resultCode??-1),[370])){
            $resultStatus=\Symfony\Component\HttpFoundation\Response::HTTP_OK;
        }
        return response()->json(GeneralResponseData::from(array(
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "QUERY-YEAHMONEY",
                "message"   => [
                    "title"   =>isset($result->resultCode)?__("The remittance not found!"):__("Can't check remittance right now! please try again later"),
                    "detail"  => "",
                    "code"    => "DIGX_SWITCH_YEAHMONEY_003",
                    "type"    => "ERROR"
                ]
            ]
        )),$resultStatus);

    }
    /**
     * Show the form for creating a new resource.
     *
     * @return ?\Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator=validator()->make($request->all(),[
            'payin_Data'=>"required",
            'trx_Info'=>"required",
            'payin_Info'=>"required",
            'sender_Info.sender_Account_No'=>"required|max:20|min:20",
            'receiver_Info'=>"required"
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-YEAH-MONEY",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_YEAHMONEY_100",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }
        $user=$request->user()->userProfile;
        if($user->partyId->value!=substr($request->input("sender_Info.sender_Account_No"),4,6)){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-YEAH-MONEY",
                    "message"   => [
                        "title"   => __("You don't have a permission!"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_YEAHMONEY_101",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        $result=CustomerService::yeahMoneyTransefer();
        $yeahMoney=YeahMoney::create([
            'payin_data'    => $request->payin_Data,
            'trx_info'      => $request->trx_Info,
            'payin_info'    => $request->payin_Info,
            'sender_info'   => $request->sender_Info,
            'receiver_info' => $request->receiver_Info,
            'external_reference_id' => $result->unique_Tracking_Code
        ]);
        LogItem::store($yeahMoney);
        NotificationService::sendMessagesToParty([
            [
                'title'=>__("Send remittance"),
                'body'=>sprintf(__("Successfully send remittance to mobile number [%s] through [%s] service"),
                    $yeahMoney->receiver_info->receiver_Mobile,
                    __("Yeah Money")
                ),
                'type'=>'operation',
            ]
        ]);
        $result->receipt= $this->getReceiptData($yeahMoney);

        $this->loyalty(
            $yeahMoney->payin_data->payin_Amount,
            $yeahMoney->payin_info->payin_Notes??$yeahMoney->payin_info->message_to_Beneficiary
        );

        return response()->json($result);
    }


     /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Pass  $pass
     * @return ?\Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, $id)
    {
        $user=$request->user();
        $yeahMoney=YeahMoney::where("external_reference_id", $id)
        ->first();

        if(is_null($yeahMoney)){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "DELETE-YEAHMONEY",
                    "message"   => [
                        "title"   => __("The remittance not found!"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_YEAHMONEY_101",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }
        if($yeahMoney->status!=TransactionStatusEnum::COMPLETED->value){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "DELETE-YEAHMONEY",
                    "message"   => [
                        "title"   => __("You can't cancel this remittance!"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_YEAHMONEY_101",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        // if(!in_array($user->id,['0145262','0183415','0184146','0173539'])){
        //     return response()->json(GeneralResponseData::from([
        //         'status'=>[
        //             "result"    => "ERROR",
        //             "contextID" => "DELETE-YEAHMONEY",
        //             "message"   => [
        //                 "title"   => __("Comming Soon!"),
        //                 "detail"  => "",
        //                 "code"    => "DIGX_SWITCH_YEAHMONEY_101",
        //                 "type"    => "ERROR"
        //             ]
        //          ]
        //     ]));
        // }

        $date=Carbon::parse($yeahMoney->created_at)->addDays(1);
        if(Carbon::now()->isBefore($date)){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "DELETE-YEAHMONEY",
                    "message"   => [
                        "title"   => __("You cannot cancel this transfer until 24 hours have passed since sending it."),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_YEAHMONEY_101",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        LogItem::store($yeahMoney);

        $this->settings=app(\App\Settings\ThirdPartySettings::class)->yeahMoney;
        if($this->settings->is_test){
            $this->settings=$this->settings->test_data;
        }

        $status=$this->getAccessToken(($request->retry??-1)>=0);

        if($status!=200 || ($request->retry??-1)>0){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "DELETE-YEAHMONEY-REMOTE - $status - ".($request->retry??-1),
                    "message"   => [
                        "title"   => __("Can't connect with service provider, please try again later!"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_001",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        $timestamp=round(microtime(true)*1000);

        $accountId=AccountIdData::from([
            "value"=>$yeahMoney->sender_info->sender_Account_No
        ]);

        $agent=$this->settings->agent_info;
        $agent['User_Info']['Agent_User_Name']=$user->name;
        $agent['User_Info']['Language']='en';
        $agent['Branch_Code']=Harvest::branchMapping[$accountId->branchId()];

        //Start login process
        $response = Http::withToken($this->settings->token->access_token)
        ->withHeaders([
            "Trx_Ref_Id"=>$timestamp
        ])
        ->post(
            "{$this->settings->url}/mt/v1/login",
            [
                "Agent_Info" => $agent
            ]
        );
        if($response->status()==401){
            $request->retry=($request->retry??-1)+1;
            return $this->destroy($request,$id);
        }

        $authResult=$response->object();
        if($response->failed() || $response->status()!=200 || ($authResult->Result_Code??-1)!=0){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "[YEAH_MONEY_SERVER_LOGIN] - ".($authResult->Result_Code??-1),
                    "message"   => [
                        "title"   => __("Something wrong please connect customer support for help!"),
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_HARVEST_003",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }
        $token=$response->header("Trx_Token");

        // Start getting Payout Info.
        $response = Http::withToken($this->settings->token->access_token)
        ->withHeaders([
            "Trx_Token"     =>str_replace("Bearer ","",$token),
            "Service_Code"  =>"YMNY",
            "Trx_Ref_Id"    =>$timestamp
        ])
        ->post(
            "{$this->settings->url}/mt/v1/amendment",
            [
                "language"  =>"en",
                "Product_Code"  =>"ACB_2_CASH",
                "Unique_Tracking_Code"=> $yeahMoney->external_reference_id,
                "Account_No"=> html_entity_decode($yeahMoney->sender_info->sender_Account_No),
                "Sender_Mobile"=>"",
                "Receiver_Mobile"=>"",
                "Amendment_Type"=>"C",
                "Transaction_Ref_NO"=>null,
                "Receiver_Old_Name"=>"",
                "Receiver_New_Name"=>""
            ]
        );



        if($response->status()==401){
            $request->retry=$request->retry+1;
            return $this->destroy($request,$id);
        }

        $result=$response->object();
        // if(isset($result->Result_Code) && $result->Result_Code==0){
        //     $status=YeahMoneyReceivedStatusEnum::CANCELED;


        if(isset($result->data->status)){
            //$status=YeahMoneyReceivedStatusEnum::CANCELED;
            $status=YeahMoneyReceivedStatusEnum::findByKey($result->data->status);
            if(!is_null($status)){
                //$yeahMoney->check_status=$status->name;
                $yeahMoney->check_status=$status->value;
                if(in_array($status,YeahMoneyReceivedStatusEnum::enabledCheck())){
                    $yeahMoney->check_enabled==1;
                    $yeahMoney->save();
                }else if(in_array($status,YeahMoneyReceivedStatusEnum::disabledCheck())){
                    $yeahMoney->check_enabled=0;
                    $yeahMoney->save();
                }
                $yeahMoney->save();

                if(isset($result->Result_Code) && $result->Result_Code==0 && $status==YeahMoneyReceivedStatusEnum::CANCELED){
                    $smsMessage=sprintf(trans("yeah_money_cancel_sms"),
                        $yeahMoney->external_reference_id
                    );

                    NotificationService::sendSMS([
                        "mobile"=> $yeahMoney->receiver_info->receiver_Mobile,
                        "message"=> $smsMessage
                    ]);
                }

                if(isset($result->Result_Code) && $result->Result_Code!=0 &&  $status==YeahMoneyReceivedStatusEnum::AVAILABLE){
                    return response()->json(GeneralResponseData::from(array(
                        'status'=>[
                            "result"    => "ERROR",
                            "contextID" => "DELETE-YEAHMONEY",
                            "message"   => [
                                "title"   =>$result->Result_Desc??__("Something wrong please connect customer support for help!"),
                                "detail"  => "",
                                "code"    => "DIGX_SWITCH_YEAHMONEY_003",
                                "type"    => "ERROR"
                            ]
                        ]
                    )));
                }
                return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "SUCCESSFUL",
                    "contextID" => "",
                    "message"   => [
                        "title"   => __("Successfully cancel the remittance"),
                        "detail"  => "",
                        "code"    => "0",
                        "type"    => "INFO"
                    ]
                ]
                ])->additional([
                    'data'=>[
                        "check_status"=>$yeahMoney->check_status,
                        "check_enabled"=>$yeahMoney->check_enabled,
                    ]
                ]));
            }

        }

        return response()->json(GeneralResponseData::from(array(
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "DELETE-YEAHMONEY",
                "message"   => [
                    "title"   =>$result->Result_Desc??__("Something wrong please connect customer support for help!"),
                    "detail"  => "",
                    "code"    => "DIGX_SWITCH_YEAHMONEY_003",
                    "type"    => "ERROR"
                ]
            ]
        )),\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);
    }
}
