# Product Overview

**BankyLite** is a banking application built for Yemen Kuwait Bank (YKB). It's a digital banking platform that provides mobile and web banking services.

## Key Features
- Digital banking services
- Mobile app support (Flutter-based frontend)
- API-driven architecture with Laravel backend
- Firebase integration for push notifications
- Multi-language support
- PDF generation for banking documents
- Real-time notifications and messaging

## Target Environment
- Production deployment on Linux servers
- Oracle database backend
- Redis for caching and queues
- Nginx/Apache web server setup
- SSL-enabled secure communications

## Business Context
- Banking domain with strict security requirements
- Multi-tenant architecture support
- Integration with core banking systems (OBDX)
- Compliance with banking regulations and standards