<?php
namespace Database\Seeders;

//use App\Models\User;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class FixUsersSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        DB::table('model_has_roles')
        ->where('model_type', 'App\User')
        ->update([
            'model_type' => User::class,
        ]);


    }
}
