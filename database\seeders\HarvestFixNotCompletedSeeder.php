<?php
namespace Database\Seeders;

use App\Models\Harvest;
use App\Models\HarvestServiceCode;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class HarvestFixNotCompletedSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        Harvest::whereIn('request_id',["1735398299889","1735204509760","1735337659612","1735230477769","1735227527120","1735469374772"])->update([
            'status' => 0,
        ]);
    }
}
