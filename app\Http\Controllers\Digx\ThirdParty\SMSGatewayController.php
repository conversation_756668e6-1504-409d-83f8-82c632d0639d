<?php

namespace App\Http\Controllers\Digx\ThirdParty;
use App\Data\AccountConfigData;
use App\Data\GeneralResponseData;
use App\Data\OBDX\BillPayment\BillPaymentData;
use App\Http\Controllers\Controller;
use App\Models\PartyVerify;
use App\Scopes\CustomerScope;
use App\Scopes\UsernameScope;
use Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class SMSGatewayController extends Controller
{
    //se:4,im:31,be:236,sr:*********,at:at:0.0,at:cy:YER,pd:se:POSTPAID,pd:te:package,pd:rn:north,pd:lk:sim,pd:fr:4g,pd:br:YEMENMOBILE,pd:by:MOBILE,dd:402CUCI012
    /**
     * Initiate
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // if (!Auth::user()->canAny(['system.thirdparty.*','system.thirdparty.sms'])) {
        //     abort(401);
        // }

        $validator=validator()->make($request->all(),[
            'phoneNumber'=>"required|max:9|min:9",
            'message'=>"required|string"
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-FLEX-EX",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_FLEX_EX_100",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }


        $partyVerifies=PartyVerify::
            withoutGlobalScope(CustomerScope::class)
            ->withoutGlobalScope(UsernameScope::class)
            ->with('party')
            ->where('phone_number',$request->phoneNumber)
            ->get();
        foreach ($partyVerifies as $partyVerify) {
            if($partyVerify->party?->offline==1){
                $party=$partyVerify;
                $token = 'ABC';//$partyVerify->token;
                $plain = $this->decryptGCMCombined('AAAAAAAAAAAAAAAA', $request->message, $token);
                if(!is_null($plain)){
                    break;
                }
            }
        }
        if(isset($plain) && !is_null($plain)){
            $data=$this->handle($partyVerify,$plain);
            if(!is_null($data)){
                return response()->json($data);
            }
        }

        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "STORE-SMS",
                "message"   => [
                    "title"   => "Failed",
                    "detail"  => "",
                    "code"    => "DIGX_SWITCH_SMS_101",
                    "type"    => "ERROR"
                ]
            ]
        ]),400);
    }
    function decryptGCMCombined($ivB64, $combinedB64, $token)
    {
        $iv = base64_decode($ivB64);
        $combined = base64_decode($combinedB64);

        $cipherLength = strlen($combined) - 16; // GCM tag is always 16 bytes

        $cipher = substr($combined, 0, $cipherLength);
        $tag = substr($combined, $cipherLength, 16);

        $key = md5($token, true); // AES-128-GCM

        $plaintext = openssl_decrypt(
            $cipher,
            'aes-128-gcm',
            $key,
            OPENSSL_RAW_DATA,
            $iv,
            $tag
        );

        if ($plaintext === false) {
            return null;
            // throw new \Exception('Decryption failed. Tag mismatch or data tampered.');
        }

        return $plaintext;
    }


    function handle($partyVerify,$plaintext)
    {
        $data=$this->parseNestedString($plaintext);
        //$data["ast"]=AccountConfigData::utilityPayment;
        switch($data["ast"]){
            case AccountConfigData::utilityPayment:
                $data=BillPaymentData::shortFormat($data);
                $data->debitAccountId->value=$data->debitAccountId->branchId().$partyVerify->party_id.$data->amount->currency.substr($data->debitAccountId->value,3, 10);
                //abort(response()->json($data->toArray()),400);
                $response =Http::withHeaders([
                    'token' =>$partyVerify->token,
                    'lang' =>"ar",
                    'devicekey' =>$partyVerify->device_key,
                    'appversion' =>**********,
                ])->post(url("/digx/v1/switch/payment/bill"),$data->toArray());
                $result=$response->object();
                if($result->status->message->code=="0" && isset($result->paymentId)){
                    $response =Http::withHeaders([
                        'token' =>$partyVerify->token,
                        'lang' =>"ar",
                        'devicekey' =>$partyVerify->device_key,
                        'appversion' =>**********,
                    ])->patch(url("/digx/v1/switch/payment/bill/{$result->paymentId}"));
                    $result=$response->object();
                }
                return $result;
            default;
                return null;
        }
    }
    function parseNestedString(string $input): array
    {
        $result = [];

        // Split by commas
        $pairs = explode(',', $input);

        foreach ($pairs as $pair) {
            // Split keys and value by :
            $parts = explode(':', $pair);

            if (count($parts) < 2) {
                continue; // skip invalid
            }

            $value = array_pop($parts);

            // Try to cast to int/float if numeric
            if (is_numeric($value)) {
                if (strpos($value, '.') !== false) {
                    $value = (float)$value;
                } else {
                    $value = (int)$value;
                }
            }

            // Build nested keys
            $temp = $value;

            // Build backwards: innermost to outermost
            for ($i = count($parts) - 1; $i >= 0; $i--) {
                $temp = [ $parts[$i] => $temp ];
            }

            // Merge into result recursively
            $result = array_merge_recursive($result, $temp);
        }

        return $result;
    }
}
