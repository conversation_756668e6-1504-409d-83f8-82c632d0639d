<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;

class Cors
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|null  $guard
     * @return mixed
     */
    public function handle($request, Closure $next, $guard = null)
    {
        if($request->method() === 'OPTIONS') {
            return abort(200);
        }
        return $next($request);
        // return $next($request)
        // ->header('Access-Control-Allow-Origin', 'http://localhost:23941')
        // ->header('Access-Control-Allow-Headers', 'Origin, Content-Type, Cookie, X-CSRF-TOKEN, Accept, Authorization, X-XSRF-TOKEN, Access-Control-Allow-Origin')
        // ->header('Access-Control-Expose-Headers', 'Authorization, authenticated')
        // ->header('Access-Control-Allow-Methods', 'GET, POST, PATCH, PUT, OPTIONS')
        // ->header('Access-Control-Allow-Credentials', 'true');
    }
}
