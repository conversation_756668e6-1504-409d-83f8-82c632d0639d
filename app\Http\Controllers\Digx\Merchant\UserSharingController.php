<?php

namespace App\Http\Controllers\Digx\Merchant;

use App\Data\AccountIdData;
use App\Data\CurrencyAmountData;
use App\Data\GeneralResponseData;
use App\Services\OBDX\AdminService;
use App\Services\OBDX\UtilsService;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;


class UserSharingController extends Controller
{

    public function profile(Request $request,string $signature)
    {
        $result=AdminService::sharingProfile($signature);
        if($result->status->message->code=="0"){
            return response()->json($result);
        }
        return response()->json($result,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);
    }
    protected function getPartyId($id)
    {
        if(substr($id,0,1)!="0"){
            $partyId="0{$id}";
        }else{
            $partyId=substr($id,1);
        }
        return $partyId;
    }
    public function exchangeRate(Request $request,$resultType='json'){
        $validator=validator()->make($request->all(),[
            'debitAccountId.value'=>"required|max:20|min:20",
            'creditAccountId.value'=>"required|max:20|min:20",
            'amount.amount'=>"required|numeric",
            'amount.currency'=>"required"
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
               'status'=>[
                    "result"    => "SUCCESSFUL",
                    "contextID" => "INDEX-GOLD",
                    "message"   => [
                        "title"   => "",
                        "detail"  => "",
                        "code"    => "DIGX_SWITCH_GOLD_HOME_100",
                        "type"    => "INFO"
                    ]
                ]
            ]));
        }

        $debitAccountId=AccountIdData::from($request->input("debitAccountId"));
        $creditAccountId=AccountIdData::from($request->input("creditAccountId"));
        $amount=CurrencyAmountData::from($request->input("amount"));

        $exchangeRate= UtilsService::exchange(
            $amount,
            $debitAccountId,
            $creditAccountId
        );
        return response()->json($exchangeRate);
    }
}
