<?php

namespace App\Http\Controllers\Digx;

use App\Data\GeneralResponseData;
use App\Http\Controllers\Controller;

use App\Models\MapCity;
use App\Models\MapDistrict;
use App\Models\MapEntry;
use App\Models\MapService;
use App\Models\User;
use Illuminate\Http\Request;

class MapController extends Controller
{
    /**
     * Display a listing of the services.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function services(Request $request)
    {
        $services = MapService::select('id', 'title_arabic as name')
            ->where('status', 1)
            ->whereIn('id', function ($query) use ($request) {
                return $query->selectRaw('service_id')
                    ->from('entries')
                    ->where('status', 1);
            })->orderBy("sort","ASC")->get();

        return response()->json(GeneralResponseData::from([
            'status' => [
                "result" => "SUCCESSFUL",
                "contextID" => "",
                "message" => [
                    "code" => "0",
                    "type" => "INFO"
                ]
            ]
        ])->additional([
            "services" => $services,
        ]));
    }
    /**
     * Display a listing of the cities.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function cities(Request $request)
    {
        $cities = MapCity::select('id', 'title_arabic as name', 'latitude', 'longitude', 'maps_zoom')->where('status', 1);

        if ($request->filled("service_id")) {
            $cities = $cities->whereIn('id', function ($query) use ($request) {
                return $query->selectRaw('city_id')
                    ->from('entries')
                    ->where('status', 1)
                    ->where('service_id', $request->service_id);
            });
        }
        $cities = $cities->orderBy("id","ASC")->get();
        return response()->json(GeneralResponseData::from([
            'status' => [
                "result" => "SUCCESSFUL",
                "contextID" => "",
                "message" => [
                    "code" => "0",
                    "type" => "INFO"
                ]
            ]
        ])->additional([
            "cities" => $cities,
        ]));

    }
    /**
     * Display a listing of the districts.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function districts(Request $request)
    {
        $districts = MapDistrict::select('id', 'title_arabic as name', 'latitude', 'longitude', 'maps_zoom')->where('status', 1);


        if ($request->filled("city_id")) {
            $districts = $districts->where('cities_id', $request->city_id);
        }
        if ($request->filled("service_id")) {
            $districts = $districts->whereIn('id', function ($query) use ($request) {
                $query->selectRaw('district_id')
                    ->from('entries')
                    ->where('status', 1)
                    ->where('service_id', $request->service_id);
                if ($request->filled("city_id")) {
                    $query->where('city_id', $request->city_id);
                }
                return $query;
            });
        }

        return response()->json(GeneralResponseData::from([
            'status' => [
                "result" => "SUCCESSFUL",
                "contextID" => "",
                "message" => [
                    "code" => "0",
                    "type" => "INFO"
                ]
            ]
        ])->additional([
            "districts" => $districts->get(),
        ]));
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $from=$request->from??0;
        $limit=$request->limit??50;
        // MapEntry::where("service_id",0)
        // ->whereNotNull("latitude")
        // ->where("latitude","<>",0)
        // ->whereNotNull("longitude")
        // ->where("longitude","<>",0)
        // ->update([
        //     "service_id"=>1
        // ]);
        $entries = MapEntry::select(
            'id',
            'title as name',
            'address',
            'phone1',
            'phone2',
            'mobile1',
            'mobile2',
            'email',
            'image',
            'notes',
            'latitude',
            'longitude',
            'zoom'
        )->where('status', 1);

        if ($request->filled("city_id")) {
            $entries = $entries->where('city_id', $request->city_id);
        }
        if ($request->filled("district_id")) {
            $entries = $entries->where('district_id', $request->district_id);
        }
        if ($request->filled("service_id")) {
            $entries = $entries->where('service_id', $request->service_id);
        }
        $entries = $entries
        // ->skip($from)
        // ->take($limit)
        // ->orderBy("id","DESC")
        ->get();
        return response()->json(GeneralResponseData::from([
            'status' => [
                "result" => "SUCCESSFUL",
                "contextID" => "",
                "message" => [
                    "code" => "0",
                    "type" => "INFO"
                ]
            ]
        ])->additional([
            "entries" => $entries,
        ]));
    }
}
