<?php

namespace App\Data;

use App\Data\Classes\BranchData;
use App\Data\Classes\ProductData;
use Illuminate\Support\Collection;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Data;

class CardData extends Data
{

    public ?string $customerId;
    public ?string $cardNumber;
    public ?string $expiryDate;
    //public ?string $cvv;
    public ?string $walletNumber;
    public ?string $walletCurrency;
    public ?string $msisdn;
    public ?string $registeredEmailId;
    public ?string $name;
    public ?string $type;
    public ?string $classification;

    public ?string $provider;

    public ?string $product;

    public ?array $functions;

}
