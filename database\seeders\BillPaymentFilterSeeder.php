<?php
namespace Database\Seeders;

//use App\Models\User;

use App\Data\NameData;
use App\Models\BillPaymentFilter;
use App\Models\BillPaymentFilterOption;
use App\Models\BillPaymentModelFilter;
use App\Models\BillPaymentModelFilterOption;
use Illuminate\Database\Seeder;

class BillPaymentFilterSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        $this->command->info('Seeding filter options...');

        BillPaymentModelFilterOption::query()->delete();
        BillPaymentModelFilter::query()->delete();
        BillPaymentFilterOption::query()->delete();
        BillPaymentFilter::query()->delete();

        $filter =new BillPaymentFilter();
        $filter->title = NameData::from([
            'ar'=>"نوع الاشتراك",
            'en'=>"Subscriber type"
        ])->toArray();
        $filter->key_name="payload:subscriberType";
        $filter->status=1;
        $filter->sort=1;
        $filter->payload=["sc"=>[42101,42102,42103]];
        $filter->save();
        BillPaymentFilterOption::insert([
            [
                'bill_payment_filter_id'=>$filter->id,
                "key_value"=>"PREPAID",
                "title"=>NameData::from([
                    'ar'=>"دفع مسبق",
                    'en'=>"Prepaid"
                ])->toJson(),
                "status"=>1,
                "payload"=>json_encode(["mt"=>[1]]),
                "created_at"=>\Carbon\Carbon::now()->toDateTimeString()
            ],[
                'bill_payment_filter_id'=>$filter->id,
                "key_value"=>"POSTPAID",
                "title"=>NameData::from([
                    'ar'=>"فوترة",
                    'en'=>"Postpaid"
                ])->toJson(),
                "status"=>1,
                "payload"=>json_encode(["mt"=>[2]]),
                "created_at"=>\Carbon\Carbon::now()->toDateTimeString()
            ]
        ]);

        $filter =new BillPaymentFilter();
        $filter->title = NameData::from([
            'ar'=>"النوع",
            'en'=>"Type"
        ])->toArray();
        $filter->key_name="payload:type";
        $filter->status=1;
        $filter->sort=2;
        $filter->payload=["sc"=>[42101,42102,42103]];
        $filter->save();
        BillPaymentFilterOption::insert([
            [
                'bill_payment_filter_id'=>$filter->id,
                "key_value"=>"payment",
                "title"=>NameData::from([
                    'ar'=>"سداد",
                    'en'=>"Payment"
                ])->toJson(),
                "status"=>1,
                "payload"=>json_encode(["item"=>[3,4,68  ,5,46    ,1,2,1147]]),
                "created_at"=>\Carbon\Carbon::now()->toDateTimeString()
            ],[
                'bill_payment_filter_id'=>$filter->id,
                "key_value"=>"package",
                "title"=>NameData::from([
                    'ar'=>"باقه",
                    'en'=>"Package"
                ])->toJson(),
                "status"=>1,
                "payload"=>json_encode(["item"=>[16,17,62   ,10,11   ,12,1129]]),
                "created_at"=>\Carbon\Carbon::now()->toDateTimeString()
            ]
        ]);


        $filter =new BillPaymentFilter();
        $filter->title = NameData::from([
            'ar'=>"المنطقه",
            'en'=>"Region"
        ])->toArray();
        $filter->key_name="payload:region";
        $filter->status=1;
        $filter->sort=3;
        $filter->payload=["sc"=>[42101,42103]];
        $filter->save();
        BillPaymentFilterOption::insert([
            [
                'bill_payment_filter_id'=>$filter->id,
                "key_value"=>"north",
                "title"=>NameData::from([
                    'ar'=>"شمال",
                    'en'=>"North"
                ])->toJson(),
                "status"=>1,
                "payload"=>json_encode(["item"=>[3,4,16,17  ,1,2,12,1129]]),
                "created_at"=>\Carbon\Carbon::now()->toDateTimeString()
            ],[
                'bill_payment_filter_id'=>$filter->id,
                "key_value"=>"south",
                "title"=>NameData::from([
                    'ar'=>"جنوب",
                    'en'=>"South"
                ])->toJson(),
                "status"=>1,
                "payload"=>json_encode(["item"=>[62,68   ,1147,1,2,12,1129]]),
                "created_at"=>\Carbon\Carbon::now()->toDateTimeString()
            ]
        ]);



        $filter =new BillPaymentFilter();
        $filter->title = NameData::from([
            'ar'=>"الربط",
            'en'=>"Link"
        ])->toArray();
        $filter->key_name="payload:link";
        $filter->status=1;
        $filter->sort=4;
        $filter->payload=["item"=>[12,1129/*,1147*/]];
        $filter->save();
        BillPaymentFilterOption::insert([
            [
                'bill_payment_filter_id'=>$filter->id,
                "key_value"=>"sim",
                "title"=>NameData::from([
                    'ar'=>"شريحة",
                    'en'=>"Sim"
                ])->toJson(),
                "status"=>1,
                "payload"=>json_encode(["denFilter"=>["1","1-1","1-3","2-1"],"denId"=>["A300068","A300009","A115887147"]]),
                "created_at"=>\Carbon\Carbon::now()->toDateTimeString()
            ],[
                'bill_payment_filter_id'=>$filter->id,
                "key_value"=>"programming",
                "title"=>NameData::from([
                    'ar'=>"برمجه",
                    'en'=>"Programming"
                ])->toJson(),
                "status"=>1,
                "payload"=>json_encode(["denFilter"=>["2","1-2"],"denId"=>["A300067","A300007"]]),
                "created_at"=>\Carbon\Carbon::now()->toDateTimeString()
            ]
        ]);


        $filter =new BillPaymentFilter();
        $filter->title = NameData::from([
            'ar'=>"فلتر",
            'en'=>"Filter"
        ])->toArray();
        $filter->key_name="payload:filter";
        $filter->show_title=0;
        $filter->status=1;
        $filter->sort=5;
        $filter->payload=["item"=>[12,1129/*,1147*/]];
        $filter->save();
        BillPaymentFilterOption::insert([
            [
                'bill_payment_filter_id'=>$filter->id,
                "key_value"=>"volte",
                "title"=>NameData::from([
                    'ar'=>"فولتي",
                    'en'=>"Volte"
                ])->toJson(),
                "status"=>1,
                "payload"=>json_encode(["denFilter"=>["volte"]]),
                "created_at"=>\Carbon\Carbon::now()->toDateTimeString()
            ],
            [
                'bill_payment_filter_id'=>$filter->id,
                "key_value"=>"4g",
                "title"=>NameData::from([
                    'ar'=>"فورجي",
                    'en'=>"4G"
                ])->toJson(),
                "status"=>1,
                "payload"=>json_encode(["denFilter"=>["4G"]]),
                "created_at"=>\Carbon\Carbon::now()->toDateTimeString()
            ],
            [
                'bill_payment_filter_id'=>$filter->id,
                "key_value"=>"mazaya",
                "title"=>NameData::from([
                    'ar'=>"مزايا",
                    'en'=>"Mazaya"
                ])->toJson(),
                "status"=>1,
                "payload"=>json_encode(["denFilter"=>["ms"]]),
                "created_at"=>\Carbon\Carbon::now()->toDateTimeString()
            ],
            [
                'bill_payment_filter_id'=>$filter->id,
                "key_value"=>"net",
                "title"=>NameData::from([
                    'ar'=>"نت",
                    'en'=>"Net"
                ])->toJson(),
                "status"=>1,
                "payload"=>json_encode(["denFilter"=>["NET"]]),
                "created_at"=>\Carbon\Carbon::now()->toDateTimeString()
            ],
            [
                'bill_payment_filter_id'=>$filter->id,
                "key_value"=>"hadaya",
                "title"=>NameData::from([
                    'ar'=>"هدايا",
                    'en'=>"Hadaya"
                ])->toJson(),
                "status"=>1,
                "payload"=>json_encode(["denFilter"=>["HD"]]),
                "created_at"=>\Carbon\Carbon::now()->toDateTimeString()
            ],
        ]);


        $filter =new BillPaymentFilter();
        $filter->title = NameData::from([
            'ar'=>"التصنيف",
            'en'=>"Category"
        ])->toArray();
        $filter->key_name="payload:category";
        $filter->status=1;
        $filter->sort=6;
       // $filter->type="header";
        $filter->show_title=0;
        $filter->payload=["sc"=>[50001]];

        $filter->save();
        BillPaymentFilterOption::insert([
            [
                'bill_payment_filter_id'=>$filter->id,
                "key_value"=>"game",
                "title"=>NameData::from([
                    'ar'=>"العاب",
                    'en'=>"Games"
                ])->toJson(),
                "status"=>1,
                "payload"=>json_encode(["item"=>[85, 87, 94, 95, 110, 112, 123, 1113, 1137, 1162, 1163, 1164, 1165, 1166, 1173, 1175, 1176, 1181, 1182, 1184, 1186, 1188, 1198, 1200, 1201, 1203, 1206, 1207, 1208, 1217, 1220, 1232, 1235, 1236, 1238, 1239, 1241, 1243, 1248, 1252, 1254, 1257, 1261, 1284, 1285, 1286, 1288, 1293, 1294, 1295, 1300, 1362, 1364]]),
                "created_at"=>\Carbon\Carbon::now()->toDateTimeString()
            ],[
                'bill_payment_filter_id'=>$filter->id,
                "key_value"=>"program",
                "title"=>NameData::from([
                    'ar'=>"برامج",
                    'en'=>"Programs"
                ])->toJson(),
                "status"=>1,
                "payload"=>json_encode(["item"=>[1172, 1174, 1179, 1202, 1210, 1221, 1258, 1259, 1289, 1290, 1292]]),
                "created_at"=>\Carbon\Carbon::now()->toDateTimeString()
            ],[
                'bill_payment_filter_id'=>$filter->id,
                "key_value"=>"subscription",
                "title"=>NameData::from([
                    'ar'=>"اشتراكات",
                    'en'=>"Subscriptions"
                ])->toJson(),
                "status"=>1,
                "payload"=>json_encode(["item"=>[1116, 1180, 1185, 1196, 1204, 1205, 1212, 1213, 1214, 1215, 1216, 1218, 1219, 1222, 1224, 1225, 1226, 1228, 1230, 1231, 1233, 1240, 1250, 1253, 1255, 1256, 1260, 1262, 1263, 1264, 1265, 1266, 1267, 1291, 1296, 1297, 1298, 1299, 1301, 1302]]),
                "created_at"=>\Carbon\Carbon::now()->toDateTimeString()
            ]
        ]);


        $filter =new BillPaymentFilter();
        $filter->title = NameData::from([
            'ar'=>"المحافظة",
            'en'=>"City"
        ])->toArray();
        $filter->key_name="payload:city";
        $filter->status=1;
        $filter->sort=7;
        $filter->type="select";
        $filter->show_title=1;
        $filter->payload=["sc"=>[1]];
        $filter->save();
        BillPaymentFilterOption::insert([
            [
                'bill_payment_filter_id'=>$filter->id,
                "key_value"=>"sanaa",
                "title"=>NameData::from([
                    'ar'=>"صنعاء",
                    'en'=>"Sana'a"
                ])->toJson(),
                "status"=>1,
                "payload"=>json_encode(["item"=>[1354, 1380, 2380, 2381, 2383, 2384, 2385, 2386, 2387, 2389,2390, 2391, 2397,2398,2403,2404,2405,2412]]),
                "created_at"=>\Carbon\Carbon::now()->toDateTimeString()
            ],[
                'bill_payment_filter_id'=>$filter->id,
                "key_value"=>"ibb",
                "title"=>NameData::from([
                    'ar'=>"اب",
                    'en'=>"Ibb"
                ])->toJson(),
                "status"=>1,
                "payload"=>json_encode(["item"=>[2420]]),
                "created_at"=>\Carbon\Carbon::now()->toDateTimeString()
            ],[
                'bill_payment_filter_id'=>$filter->id,
                "key_value"=>"taiz",
                "title"=>NameData::from([
                    'ar'=>"تعز",
                    'en'=>"Taiz"
                ])->toJson(),
                "status"=>1,
                "payload"=>json_encode(["item"=>[]]),
                "created_at"=>\Carbon\Carbon::now()->toDateTimeString()
            ]
            ,[
                'bill_payment_filter_id'=>$filter->id,
                "key_value"=>"hodeidah",
                "title"=>NameData::from([
                    'ar'=>"الحديده",
                    'en'=>"Alhodeidah"
                ])->toJson(),
                "status"=>1,
                "payload"=>json_encode(["item"=>[2399,2400]]),
                "created_at"=>\Carbon\Carbon::now()->toDateTimeString()
            ]
            ,[
                'bill_payment_filter_id'=>$filter->id,
                "key_value"=>"dhamar",
                "title"=>NameData::from([
                    'ar'=>"ذمار",
                    'en'=>"Dhamar"
                ])->toJson(),
                "status"=>1,
                "payload"=>json_encode(["item"=>[2416]]),
                "created_at"=>\Carbon\Carbon::now()->toDateTimeString()
            ]
        ]);
        $this->command->info('Seeder completed.');
    }
}
