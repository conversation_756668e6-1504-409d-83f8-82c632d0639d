<?php

namespace App\Data;

use Spa<PERSON>\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Normalizers\ObjectNormalizer;
use Spatie\LaravelData\DataCollection;
class GoldConfigData extends Data
{

    public ?string $chargeProductId;
    public ?string $chargeAccountId;

    public ?string $chargeTransferProductId;
    public ?string $chargeTransferAccountId;

    public ?NameData $term;


    #[DataCollectionOf(GoldGeneralItemData::class)]
    public ?DataCollection $goldTypes;

    #[DataCollectionOf(GoldGeneralItemData::class)]
    public ?DataCollection $services;
    public ?NameData $featuresTitle;


    #[DataCollectionOf(GeneralItemData::class)]
    public ?DataCollection $features;

    public function __construct(
        public ?string $chargeProductIdSouth=''
    ) {
    }

    public function toAppJson():array {
        $locale=app()->getLocale();
        return [
            "goldTypes"=> $this->goldTypes,
            "services"=> $this->services,
            'featuresTitle'=>$this->featuresTitle,
            'features'=>$this->features,
            'term'=>strip_tags(str_replace('</span></p>',PHP_EOL.PHP_EOL,$this->term->{"$locale"}))
        ];
    }
}
