<?php

namespace App\Http\Controllers\Digx\Agent;

use App\Data\AccountIdData;
use App\Data\Agent\AgentTransactionData;
use App\Data\Agent\DepositRequestBaseData;
use App\Data\Agent\DepositRequestConfirmData;
use App\Data\Classes\BranchData;
use App\Data\CurrencyAmountData;
use App\Data\GeneralResponseData;
use App\Data\ThirdPartyData;
use App\Data\TokenData;
use App\Data\AccountData;

use App\Http\Controllers\Controller;
use App\LogItem;
use App\Models\AgentDeposit;
use App\Models\AgentTransaction;
use App\Models\AgentTransactionServiceCode;
use App\Services\YeahMoney\AgentService;
use App\Services\CustomerService;
use DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Spatie\LaravelData\DataCollection;
use Symfony\Component\HttpFoundation\Cookie;
use GuzzleHttp\Cookie\CookieJar;
use Validator;

class C2AController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $user=$request->user()->userProfile;
        $agentTransactions=AgentTransaction::select("id","external_reference_id","request_data","init_response_data",
            "amount",'customer_account_id',"created_at")
        ->where("party_id",$user->partyId->value)
        ->where("status",\App\Enums\TransactionStatusEnum::COMPLETED->value)
        ->skip( $request->from)
        ->take($request->limit)
        ->orderBy("id","DESC")
        ->get();
        return response()->json(AgentTransactionData::collect($agentTransactions,DataCollection::class));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function create(Request $request)
    {
        // $validator=validator()->make($request->all(),[
        //     'branchId'=>"required|max:3|min:3",
        //     //"serviceCode"=> "required|numeric",
        // ]);

        // if($validator->fails()){
        //     return response()->json(GeneralResponseData::from([
        //         'status'=>[
        //             "result"    => "ERROR",
        //             "contextID" => "STORE-AGENT_C2A",
        //             "message"   => [
        //                 "title"   => join("\n",$validator->errors()->all()),
        //                 "detail"  => join("\n",$validator->errors()->all()),
        //                 "code"    => "DIGX_SWITCH_AGENT_C2A_100",
        //                 "type"    => "ERROR"
        //             ]
        //          ]
        //     ]));
        // }

        $user=$request->user()->userProfile;
        $middleNames=explode(" ",html_entity_decode(trim($user->middleName)));
        $secondName=collect($middleNames)->first();
        array_shift($middleNames);
        $fullName=html_entity_decode($user->firstName)." ".$secondName." ".join(' ',$middleNames)." ".html_entity_decode($user->lastName);

        $requestData=DepositRequestBaseData::from($request->all());
        $requestData->agentName=$fullName;
        $requestData->agentBranch=$user->agent->branch_code;
        $requestData->agentCode=$user->agent->agent_code;

        $result=AgentService::getHome($requestData);
        // if($request->filled('serviceCode')){
        //     if($request->filled('countryCode')){
        //         if($request->filled('inCurrencyCode') && $request->filled('amount.currency')){
        //             if($request->filled('customerAccountId.value') && $request->filled('partyCode')){
        //                 $result=AgentService::getCustomerAccounts($requestData);
        //             }else{
        //                 $result=AgentService::getParties($requestData);
        //             }
        //         }else{
        //             $result=AgentService::getCurrencies($requestData);
        //         }
        //     }else{
        //         $result=AgentService::getCountries($requestData);
        //     }
        // }else{
        //     $result=AgentService::getHome($requestData);
        // }
        return response()->json($result);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator=validator()->make($request->all(),[
            "serviceCode"=> "required",
            "countryCode"=> "required|max:2|min:2",
            "inCurrencyCode"=> "required|max:3|min:3",
            'amount.amount'=>"required|numeric",
            'amount.currency'=>"required|max:3|min:3",
            "partyCode"=> "required",
            "branchCode"=> "required",
            'customerAccountId.value'=>"required|max:20|min:20"
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-AGENT_C2A",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_AGENT_C2A_100",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }

        $user=$request->user()->userProfile;
        $middleNames=explode(" ",html_entity_decode(trim($user->middleName)));
        $secondName=collect($middleNames)->first();
        array_shift($middleNames);
        $fullName=html_entity_decode($user->firstName)." ".$secondName." ".join(' ',$middleNames)." ".html_entity_decode($user->lastName);


        //$requestData=new DepositRequestBaseData();
        $requestData=DepositRequestBaseData::from($request->all());
        $requestData->agentName=$fullName;
        $requestData->agentBranch=$user->agent->branch_code;
        $requestData->agentCode=$user->agent->agent_code;
        // $object->fullName       = $fullName;
        // $object->branchId       = $request->branchId;
        // $object->serviceCode    = $request->serviceCode??'YMNY';
        // $object->countryCode    = $request->countryCode;
        // $object->inCurrencyCode = $request->inCurrencyCode;
        // $object->amount         = CurrencyAmountData::from($request->amount);
        // $object->partyCode      = $request->partyCode;
        // $object->branchCode     = $request->branchCode;
        // $object->creditAccountId= AccountIdData::from($request->creditAccountId);

        $result=AgentService::initC2A($requestData);
        if($result->status->message->code!="0"){
            return response()->json($result,\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);
        }
        $timestamp=$result->getAdditionalData()['timestamp'];
        $token=$result->getAdditionalData()['token'];
        $result=$result->getAdditionalData()['result'];


        //Create init model
        $agentTransaction=new AgentTransaction();
        LogItem::store($agentTransaction);

        $agentTransaction->type=\App\Enums\AgentTransactionTypeEnum::DEPOSIT->value;
        $agentTransaction->party_id=$user->partyId->value;
        $agentTransaction->reference_id=$timestamp;
        $agentTransaction->token=$token;
        $agentTransaction->request_data=$requestData->toArray();
        $agentTransaction->init_response_data=$result;
        $agentTransaction->customer_account_id=$requestData->customerAccountId->toArray();
        $agentTransaction->amount=CurrencyAmountData::from([
            "amount"    =>$result->Payin_Info->Payin_Amount,
            "currency"  =>$result->Payin_Info->Payin_Currency,
        ])->toArray();
        $agentTransaction->status= \App\Enums\TransactionStatusEnum::INIT->value;

        // $agentTransaction->agent_commission=CurrencyAmountData::from([
        //     "amount"    =>$result->Settllement_Info->Settlement_Fee_Amount,
        //     "currency"  =>$result->Settllement_Info->Settlement_Fee_Currency_Code,
        // ])->toArray();
        // $agentTransaction->fee=CurrencyAmountData::from([
        //     "amount"    =>$result->Payin_Info->Payin_Fee_Amount,
        //     "currency"  =>$result->Payin_Info->Fee_Currency_Code,
        // ])->toArray();

        $agentTransaction->save();

        return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "SUCCESSFUL",
                    "contextID" => "",
                    "message"   => [
                        "title"   => "",
                        "detail"  => "",
                        "code"    => "0",
                        "type"    => "INFO"
                    ]
                ],
            ])->additional([
                "transaction"=>AgentTransactionData::from(collect($agentTransaction->toArray()))
            ])
        );
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\AgentTransaction  $agentTransaction
     * @return ?\Illuminate\Http\JsonResponse
     */
    public function show(AgentTransaction $agentTransaction)
    {
        return response()->json(GeneralResponseData::from([
            'status'=>[
                "result"    => "ERROR",
                "contextID" => "STORE-AGENT_C2A",
                "message"   => [
                    "title"   => "Not found",
                    "detail"  => "Not found",
                    "code"    => "DIGX_SWITCH_AGENT_C2A_100",
                    "type"    => "ERROR"
                ]
             ]
        ])->additional([
        'externalReferenceId' => $agentTransaction->id,
        ]));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\AgentTransaction  $agentTransaction
     * @return ?\Illuminate\Http\JsonResponse
     */
    public function edit(AgentTransaction $agentTransaction)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\AgentTransaction  $agentTransaction
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {

        $validator=validator()->make($request->all(),[
            'senderName.first'=>"required",
            'senderName.second'=>"required",
            'senderName.third'=>"required",
            'senderName.last'=>"required",
        ]);

        if($validator->fails()){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-AGENT_C2A",
                    "message"   => [
                        "title"   => join("\n",$validator->errors()->all()),
                        "detail"  => join("\n",$validator->errors()->all()),
                        "code"    => "DIGX_SWITCH_AGENT_C2A_100",
                        "type"    => "ERROR"
                    ]
                 ]
            ]));
        }
        $user=$request->user()->userProfile;

        $agentTransaction=AgentTransaction::where('id',$id)->where('party_id',$user->partyId->value)->first();
        if(is_null($agentTransaction)){
            return response()->json(GeneralResponseData::from([
                'status'=>[
                    "result"    => "ERROR",
                    "contextID" => "STORE-AGENT_C2A",
                    "message"   => [
                        "title"   => "Not found",
                        "detail"  => "Not found",
                        "code"    => "DIGX_SWITCH_AGENT_C2A_100",
                        "type"    => "ERROR"
                    ]
                 ]
            ]),\Symfony\Component\HttpFoundation\Response::HTTP_NOT_FOUND);
        }
        LogItem::store($agentTransaction);

        $requestData=DepositRequestConfirmData::from($agentTransaction->request_data);
        $requestData->firstName=$request->input("senderName.first");
        $requestData->secondName=$request->input("senderName.second");
        $requestData->thirdName=$request->input("senderName.third");
        $requestData->lastName=$request->input("senderName.last");

        $agentTransactionData=AgentTransactionData::from($agentTransaction);
        $requestData->fee=CurrencyAmountData::from([
            "amount"=>$agentTransactionData->fee->amount + $agentTransactionData->agentCommission->amount,
            "currency"=>$agentTransactionData->fee->currency
        ]);


        $result=AgentService::confirmC2A($requestData);

        $agentTransaction->response_data= $result->getAdditionalData()['result'];
        $agentTransaction->request_data= $requestData->toArray();

        if($result->status->message->code!="0"){
            $agentTransaction->status= \App\Enums\TransactionStatusEnum::ERROR->value;
            $agentTransaction->save();
            return response()->json(GeneralResponseData::from(
                $result->toArray()
            ),\Symfony\Component\HttpFoundation\Response::HTTP_NOT_IMPLEMENTED);
        }

        $agentTransaction->external_reference_id= $result->getAdditionalData()['externalReferenceId'];
        $agentTransaction->status= \App\Enums\TransactionStatusEnum::COMPLETED->value;
        $agentTransaction->save();

        return response()->json(GeneralResponseData::from(
            $result->toArray()
        )->additional([
            'externalReferenceId' => $agentTransaction->external_reference_id,
        ]));
    }

}
