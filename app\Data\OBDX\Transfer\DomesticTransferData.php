<?php

namespace App\Data\OBDX\Transfer;

use App\Data\AccountIdData;
use App\Data\BaseNonNullableData;
use App\Data\CurrencyAmountData;

class DomesticTransferData extends BaseNonNullableData
{
    public function __construct(
        public ?AccountIdData $debitAccountId,
        public ?CurrencyAmountData $amount,
        public ?CurrencyAmountData $fee,
        public ?string $receiverNumber,
        public ?string $receiverName,
        public ?string $remarks,
        public ?string $bankCode,
        public ?string $service,
    ) {
    }

    public static function prepareForPipeline(array $properties) : array
    {
        $properties['debitAccountId']= data_get($properties,"payoutDetails.debitAccountId");
        $purpose= data_get($properties,"payoutDetails.purpose");
        if($purpose=="COMM"){
            $properties['service']="payment";
        }else{
            $properties['service']="transfer";
        }

        $remarks=html_entity_decode(data_get($properties,"payoutDetails.remarks"));
        $parts = explode('~', $remarks);
        $properties['amount']= [
            "amount"=> data_get($properties,"payoutDetails.amount.amount")-$parts[3],
            "currency"=>data_get($properties,"payoutDetails.amount.currency"),
        ];
        $properties['fee']= [
            "amount"=> $parts[3],
            "currency"=>data_get($properties,"amount.currency"),
        ];
        $properties['bankCode']=$parts[count($parts)-2];

        $properties['receiverNumber']=data_get($properties,"payeeDetails.indiaDomesticPayee.accountNumber");

        $accountName=html_entity_decode( data_get($properties,"payeeDetails.indiaDomesticPayee.accountName"));
        $parts = explode('#', $accountName);
        $properties['receiverName']=$parts[0];



        return $properties;
    }

}
