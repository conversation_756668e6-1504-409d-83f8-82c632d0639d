<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Enums\CurrencyTypeEnum;

class GoldTransaction extends Model
{
    use HasFactory;
    protected $fillable = ['gold_id', 'status', 'type','debit_amount','credit_amount','debit_fee','credit_fee','remarks','reference_id', 'external_reference_id'];
    protected $casts = [
        'debit_amount' => 'object',
        'credit_amount' => 'object',
        'debit_fee' => 'object',
        'credit_fee' => 'object'


    ];
    protected $hidden = [
        'gold_id'
    ];
    public function gold()
    {
        return $this->belongsTo('App\Models\Gold');
    }

    // public function debitLocalAmount()
    // {
    //     $amountIsGold=in_array($this->debit_amount->currency,[CurrencyTypeEnum::G21->value,CurrencyTypeEnum::G24->value]);
    //     return $amountIsGold?$this->credit_amount->amount+$this->fee->amount:$this->debit_amount->amount;
    // }

    // public function creditLocalAmount()
    // {
    //     $amountIsGold=in_array($this->credit_amount->currency,[CurrencyTypeEnum::G21->value,CurrencyTypeEnum::G24->value]);
    //     return $amountIsGold?$this->debit_amount->amount-$this->fee->amount:$this->credit_amount->amount;
    // }
}
