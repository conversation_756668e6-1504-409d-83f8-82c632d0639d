<?php

namespace App\Enums;
enum CurrencyTypeEnum:string {
    case YER="YER";
    case USD="USD";
    case EUR="EUR";
    case GBP="GBP";
    case JPY="JPY";
    case AED="AED";
    case SAR="SAR";
    case EGP="EGP";
    case BHD="BHD";
    case OMR="OMR";
    // case D21="D21";
    // case D24="D24";
    case G21="21G";
    case G24="24G";
    case G18="18G";
    public static function values():array {
        return collect(CurrencyTypeEnum::cases())->pluck("value")->toArray();
    }
}
