<?php

namespace App\Data\Form;

use App\Data\AccountIdData;
use App\Data\BaseNonNullableData;
use App\Data\CurrencyAmountData;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Attributes\MapOutputName;
use Spatie\LaravelData\DataCollection;


class FormBaseData extends BaseNonNullableData
{
    public function __construct(
        #[MapOutputName('et'),MapInputName('et')]
        public ?string $element=null
    ) {
    }
}
