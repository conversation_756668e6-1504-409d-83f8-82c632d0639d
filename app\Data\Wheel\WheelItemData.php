<?php

namespace App\Data\Wheel;

use Illuminate\Support\Collection;
use App\Models\CustomerType;
use Spatie\LaravelData\Data;

class WheelItemData extends Data
{

    public function __construct(
        public int $id,
        public string $name,
        public string $image,
        public string $color,
        public float $weight,
        public int $type=0,
        public int $status=0,
        public ?float $value=0,
        public ?float $maxAmountPerDay=-1.0

    ) {
    }


}
