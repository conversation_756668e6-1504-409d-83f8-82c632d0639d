# HarvestController Technical Specification

## System Architecture

### Technology Stack
- **Framework**: Lara<PERSON> 9.x
- **Language**: PHP 8.0+
- **Database**: MySQL 8.0+ / Oracle 19c+
- **Cache**: Redis
- **HTTP Client**: Guzzle (via <PERSON><PERSON> HTTP facade)
- **Data Handling**: <PERSON><PERSON>
- **Settings**: <PERSON><PERSON>

### Design Patterns
- **Repository Pattern**: Model-based data access
- **Data Transfer Objects**: Structured data handling via Spatie Data
- **Service Layer**: External service integration
- **Trait-based Authorization**: Reusable authorization logic
- **Observer Pattern**: Model event handling
- **Factory Pattern**: Model creation

## Class Structure

### HarvestController
```php
namespace App\Http\Controllers\Digx;

class HarvestController extends Controller
{
    use AuthorizesServices;
    
    protected $settings;
    
    // Public Methods
    public function __construct(Request $request)
    public function index(Request $request): JsonResponse
    public function create(): JsonResponse
    public function store(Request $request): JsonResponse
    public function update(Request $request, Harvest $harvest): JsonResponse
    public function receipt(Request $request, Harvest $harvest)
    
    // Protected Methods
    protected function getServiceTags(): array
    protected function getReceiptData(Harvest $harvest): ReceiptData
    
    // Private Methods
    public function getAccessToken($forceUpdate = false): int
}
```

### Data Transfer Objects

#### HarvestRequestData
```php
class HarvestRequestData extends Data
{
    public string $trackingCode;
    public ?CurrencyAmountData $amount;
    public ?string $senderPhone;
    public ?AccountIdData $creditAccountId;
    public int $serviceCode;
    public ?int $branchId;
    public ?string $firstName;
    public ?string $secondName;
    public ?string $thirdName;
    public ?string $lastName;
    public ?string $fullName;
    
    public function fullName(): string
    public function payoutInfoParams(): array
}
```

#### AccountIdData
```php
class AccountIdData extends Data
{
    public ?string $displayValue;
    public string $value; // 20 characters
    
    public function userName(): ?string
    public function partyId(): ?string
    public function currencyId(): ?string
    public function branchId(): ?string
    public function productId(): ?string
    public function isNorth(): bool
    public function availableServices(): array
}
```

## External API Integration

### Yeah Money API

#### Authentication
```http
POST /oauth/token
Content-Type: application/x-www-form-urlencoded
Authorization: Basic {base64(client_id:client_secret)}

grant_type=client_credentials
```

#### Agent Login
```http
POST /mt/v1/login
Authorization: Bearer {access_token}
Trx_Ref_Id: {timestamp}

{
    "Agent_Info": {
        "Agent_Code": "YKB",
        "Branch_Code": "YKBHO",
        "User_Info": {
            "Username": "<EMAIL>",
            "Password": "password",
            "Language": "ar",
            "Agent_User_Name": "Agent Name"
        }
    }
}
```

#### Remittance Inquiry
```http
POST /mt/v1/payoutInfo
Authorization: Bearer {access_token}
Trx_Token: {transaction_token}
Service_Code: YMNY
Trx_Ref_Id: {timestamp}

{
    "Product_Code": "PULL_2_ACB",
    "Unique_Tracking_Code": "YM401707304",
    "Payin_Data": {
        "Receiver_First_Name": "John",
        "Receiver_Second_Name": "Michael",
        "Receiver_Third_Name": "Smith",
        "Receiver_Surname": "Doe",
        "Receiver_Mobile": "*********",
        "Payout_Amount": "124.5",
        "Payout_Currency_Code": "USD",
        "Sender_Mobile": "**********",
        "Receiver_Type": "I"
    }
}
```

#### Transaction Processing
```http
POST /mt/v1/payout
Authorization: Bearer {access_token}
Trx_Token: {transaction_token}
Service_Code: YMNY
Trx_Ref_Id: {timestamp}

{
    "Payout_Info": {
        "Product_Code": "PULL_2_ACB",
        "PayIn_Method": "CSH",
        "Unique_Tracking_Code": "YM401707304",
        "Payout_Agent_Notes": "Banky lite claim remittance",
        "Payout_Agent_Extra_Info": ""
    },
    "Receiver_Info": {
        "Receiver_Account_No": "40118341500100010001",
        "Receiver_Mobile": "*********",
        "Receiver_Home_Address": "Address",
        "Receiver_Country_Code": "YE",
        // ... additional receiver fields
    }
}
```

### OBDX Banking System

#### Account Validation
```php
$result = CustomerService::account($request, $accountId);
// Returns AccountData object or error
```

#### Card Information
```php
$result = CustomerService::cardInfo($request);
// Returns card details for KYC compliance
```

## Database Design

### Table Relationships
```sql
-- Primary relationship
harvests.service_code_id -> harvest_service_codes.id

-- User relationship
harvests.party_id -> users.id (via CustomerScope)

-- Logging relationship
harvests.id <- log_entries.model_id (where model = 'App\Models\Harvest')
```

### JSON Data Structures

#### Harvest.data (HarvestRequestData)
```json
{
    "trackingCode": "YM401707304",
    "amount": {"amount": 124.5, "currency": "USD"},
    "senderPhone": "*********",
    "creditAccountId": {"value": "40118341500100010001"},
    "serviceCode": 1000000,
    "branchId": "401",
    "firstName": "John",
    "secondName": "Michael",
    "thirdName": "Smith",
    "lastName": "Doe",
    "fullName": "John Michael Smith Doe"
}
```

#### Harvest.sender_info
```json
{
    "Sender_Full_Name": "Jane Doe",
    "Sender_Mobile": "**********",
    "Agent_Name": "Yeah Money Agent",
    "Sender_Country_Code": "US",
    "Sender_Currency_Code": "USD"
}
```

#### Harvest.received_data
```json
{
    "Result_Code": 0,
    "Result_Desc": "Success",
    "Payout_Trx_Id": "EXT123456789",
    "Settlement_Info": {
        "Payout_Amount": 124.5,
        "Payout_Currency_Code": "USD"
    }
}
```

## Business Rules

### Account Validation Rules
1. **Account Status**: Must be ACTIVE
2. **Account Type**: Must be cash account (isCash() = true)
3. **Currency Match**: Account currency must match remittance currency
4. **Regional Validation**: For YER currency, account region must match branch region
5. **Ownership**: Account must belong to authenticated user

### Transaction Status Flow
```
0 (Pending) -> 1 (Completed) [Success]
0 (Pending) -> -1 (Failed) [Error]
```

### Branch Mapping
```php
const branchMapping = [
    "401" => "YKBHO",     // Head Office
    "402" => "YKBSATA01", // Sana'a Branch
    "403" => "YKBADCR01", // Aden Branch
    // ... additional mappings
];
```

## Error Handling Strategy

### Error Categories
1. **Validation Errors** (400): Invalid input data
2. **Authentication Errors** (401): Token issues
3. **Authorization Errors** (403): Permission denied
4. **Business Logic Errors** (422): Rule violations
5. **External Service Errors** (502): Third-party failures
6. **System Errors** (500): Internal failures

### Retry Logic
```php
// Token refresh retry
if ($response->status() == 401) {
    $request->retry = ($request->retry ?? -1) + 1;
    return static::store($request);
}
```

### Error Response Structure
```php
GeneralResponseData::from([
    'status' => [
        'result' => 'ERROR',
        'contextID' => 'OPERATION_CONTEXT',
        'message' => [
            'title' => 'User message',
            'detail' => 'Technical details',
            'code' => 'ERROR_CODE',
            'type' => 'ERROR'
        ]
    ]
]);
```

## Security Implementation

### Input Validation
```php
$validator = validator()->make($request->all(), [
    'trackingCode' => 'required',
    'amount.amount' => 'nullable|numeric',
    'amount.currency' => 'nullable|max:3|min:3',
    'senderPhone' => 'nullable|max:9|min:9',
    'branchId' => 'required|max:3|min:3',
    'serviceCode' => 'required|numeric',
]);
```

### Data Sanitization
```php
// HTML entity decoding for names
$requestData->firstName = html_entity_decode($user->firstName);

// Phone number formatting
$params['Receiver_Mobile'] = html_entity_decode(auth()->user()->phone);
```

### Token Management
```php
// Hide sensitive data
protected $hidden = ['rn', 'token'];
public static $hiddenParameters = ['token'];

// Encrypted settings storage
public static function encrypted(): array
{
    return ['yeahMoney', /* other services */];
}
```

## Performance Optimizations

### Database Indexing
```sql
-- Primary indexes
INDEX harvests_party_id (party_id)
INDEX harvests_service_code_id (service_code_id)
INDEX harvests_status (status)
INDEX harvests_request_id (request_id)

-- JSON field indexes (Oracle)
CREATE INDEX harvests_tracking_code 
ON harvests (JSON_VALUE(data, '$.trackingCode'))
```

### Query Optimization
```php
// Efficient pagination
$harvests = Harvest::select(/* specific fields */)
    ->leftJoin('harvest_service_codes', /* join condition */)
    ->where('harvests.status', 1)
    ->skip($request->from)
    ->take($request->limit)
    ->orderBy('harvests.id', 'DESC')
    ->get();
```

### Caching Strategy
- **Token Caching**: OAuth tokens cached until expiration
- **Settings Caching**: Configuration data cached in memory
- **Query Caching**: Frequently accessed data cached in Redis

## Monitoring and Logging

### Audit Trail
```php
// Automatic logging on model changes
LogItem::store($harvest);

// Relationship to log entries
public function logs()
{
    return $this->hasMany('App\Models\LogEntry', 'model_id')
        ->where('model', static::class)
        ->where('type', 'request')
        ->with('relatedEntries');
}
```

### Performance Metrics
- API response times
- Database query performance
- External service availability
- Error rates by endpoint
- Transaction success rates

### Health Checks
- Database connectivity
- External API availability
- Cache system status
- File system permissions
- SSL certificate validity
